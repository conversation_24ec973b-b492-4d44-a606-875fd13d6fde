package com.elewashy.egyfilm.ui.screens

import androidx.compose.animation.core.Animatable
import androidx.compose.animation.core.DecelerateEasing
import androidx.compose.animation.core.tween
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.LinearProgressIndicator
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import com.elewashy.egyfilm.R
import com.elewashy.egyfilm.ui.theme.EgyFilmRed
import com.elewashy.egyfilm.ui.theme.EgyFilmRedTransparent
import kotlinx.coroutines.delay

@Composable
fun SplashScreen(
    onNavigateToMain: () -> Unit,
    onRetryClicked: () -> Unit,
    showRetryButton: Boolean = false,
    showUpdateDialog: Boolean = false,
    updateTitle: String = "",
    updateMessage: String = "",
    onUpdateClicked: () -> Unit = {},
    onUpdateDismissed: () -> Unit = {},
    showDownloadProgress: Boolean = false,
    downloadProgress: Float = 0f,
    downloadStatus: String = ""
) {
    // Animation states
    val logoAlpha = remember { Animatable(0f) }
    val logoScale = remember { Animatable(0.7f) }
    val logoTranslationY = remember { Animatable(-50f) }
    
    val progressAlpha = remember { Animatable(0f) }
    val progressScale = remember { Animatable(0.5f) }
    val progressTranslationY = remember { Animatable(30f) }

    // Start animations
    LaunchedEffect(Unit) {
        // Animate logo first
        kotlinx.coroutines.launch {
            logoAlpha.animateTo(
                targetValue = 1f,
                animationSpec = tween(900, delayMillis = 200, easing = DecelerateEasing)
            )
        }
        kotlinx.coroutines.launch {
            logoScale.animateTo(
                targetValue = 1f,
                animationSpec = tween(900, delayMillis = 200, easing = DecelerateEasing)
            )
        }
        kotlinx.coroutines.launch {
            logoTranslationY.animateTo(
                targetValue = 0f,
                animationSpec = tween(900, delayMillis = 200, easing = DecelerateEasing)
            )
        }
        
        // Wait for logo animation to finish, then animate progress
        delay(1100)
        
        kotlinx.coroutines.launch {
            progressAlpha.animateTo(
                targetValue = 1f,
                animationSpec = tween(700, delayMillis = 100, easing = DecelerateEasing)
            )
        }
        kotlinx.coroutines.launch {
            progressScale.animateTo(
                targetValue = 1f,
                animationSpec = tween(700, delayMillis = 100, easing = DecelerateEasing)
            )
        }
        kotlinx.coroutines.launch {
            progressTranslationY.animateTo(
                targetValue = 0f,
                animationSpec = tween(700, delayMillis = 100, easing = DecelerateEasing)
            )
        }
    }

    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(Color.Black),
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {
            // Logo
            Image(
                painter = painterResource(id = R.mipmap.ic_launcher),
                contentDescription = "EgyFilm Logo",
                modifier = Modifier
                    .size(150.dp)
                    .alpha(logoAlpha.value)
                    .scale(logoScale.value)
            )
            
            Spacer(modifier = Modifier.height(48.dp))
            
            // Loading Progress
            if (!showRetryButton && !showDownloadProgress) {
                CircularProgressIndicator(
                    modifier = Modifier
                        .size(64.dp)
                        .alpha(progressAlpha.value)
                        .scale(progressScale.value),
                    color = EgyFilmRed,
                    trackColor = EgyFilmRedTransparent,
                    strokeWidth = 3.dp
                )
            }
            
            // Retry Button
            if (showRetryButton) {
                Button(
                    onClick = onRetryClicked,
                    colors = ButtonDefaults.buttonColors(
                        containerColor = EgyFilmRed
                    ),
                    shape = RoundedCornerShape(8.dp),
                    modifier = Modifier.padding(16.dp)
                ) {
                    Text(
                        text = "Retry",
                        color = Color.White,
                        fontWeight = FontWeight.Medium
                    )
                }
            }
            
            // Download Progress
            if (showDownloadProgress) {
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally,
                    modifier = Modifier.padding(horizontal = 32.dp)
                ) {
                    LinearProgressIndicator(
                        progress = { downloadProgress },
                        modifier = Modifier
                            .width(200.dp)
                            .height(4.dp),
                        color = EgyFilmRed,
                        trackColor = EgyFilmRedTransparent,
                    )
                    
                    Spacer(modifier = Modifier.height(12.dp))
                    
                    Text(
                        text = downloadStatus,
                        color = Color(0xFFCCCCCC),
                        fontSize = 14.sp,
                        textAlign = TextAlign.Center
                    )
                }
            }
        }
    }
    
    // Update Dialog
    if (showUpdateDialog) {
        UpdateDialog(
            title = updateTitle,
            message = updateMessage,
            onUpdateClicked = onUpdateClicked,
            onDismissed = onUpdateDismissed
        )
    }
}

@Composable
private fun UpdateDialog(
    title: String,
    message: String,
    onUpdateClicked: () -> Unit,
    onDismissed: () -> Unit
) {
    Dialog(onDismissRequest = onDismissed) {
        Card(
            modifier = Modifier.padding(16.dp),
            shape = RoundedCornerShape(8.dp),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surface
            )
        ) {
            Column(
                modifier = Modifier.padding(24.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = title,
                    style = MaterialTheme.typography.titleMedium,
                    color = MaterialTheme.colorScheme.onSurface,
                    textAlign = TextAlign.Center
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                Text(
                    text = message,
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    textAlign = TextAlign.Center
                )
                
                Spacer(modifier = Modifier.height(24.dp))
                
                Button(
                    onClick = onUpdateClicked,
                    colors = ButtonDefaults.buttonColors(
                        containerColor = EgyFilmRed
                    ),
                    shape = RoundedCornerShape(8.dp)
                ) {
                    Text(
                        text = "Update Now",
                        color = Color.White,
                        fontWeight = FontWeight.Medium
                    )
                }
            }
        }
    }
}
