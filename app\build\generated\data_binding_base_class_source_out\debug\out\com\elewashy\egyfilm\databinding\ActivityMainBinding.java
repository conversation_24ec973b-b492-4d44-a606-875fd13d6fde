// Generated by view binder compiler. Do not edit!
package com.elewashy.egyfilm.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageButton;
import android.widget.LinearLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.motion.widget.MotionLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.elewashy.egyfilm.R;
import com.google.android.material.imageview.ShapeableImageView;
import com.google.android.material.progressindicator.LinearProgressIndicator;
import com.google.android.material.textfield.TextInputEditText;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityMainBinding implements ViewBinding {
  @NonNull
  private final MotionLayout rootView;

  @NonNull
  public final ImageButton backButton;

  @NonNull
  public final FrameLayout fragmentContainer;

  @NonNull
  public final ImageButton goBtn;

  @NonNull
  public final ImageButton moreOptionsBtn;

  @NonNull
  public final LinearProgressIndicator progressBar;

  @NonNull
  public final ImageButton refreshBtn;

  @NonNull
  public final LinearLayout toolbar;

  @NonNull
  public final TextInputEditText topSearchBar;

  @NonNull
  public final ShapeableImageView webIcon;

  private ActivityMainBinding(@NonNull MotionLayout rootView, @NonNull ImageButton backButton,
      @NonNull FrameLayout fragmentContainer, @NonNull ImageButton goBtn,
      @NonNull ImageButton moreOptionsBtn, @NonNull LinearProgressIndicator progressBar,
      @NonNull ImageButton refreshBtn, @NonNull LinearLayout toolbar,
      @NonNull TextInputEditText topSearchBar, @NonNull ShapeableImageView webIcon) {
    this.rootView = rootView;
    this.backButton = backButton;
    this.fragmentContainer = fragmentContainer;
    this.goBtn = goBtn;
    this.moreOptionsBtn = moreOptionsBtn;
    this.progressBar = progressBar;
    this.refreshBtn = refreshBtn;
    this.toolbar = toolbar;
    this.topSearchBar = topSearchBar;
    this.webIcon = webIcon;
  }

  @Override
  @NonNull
  public MotionLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityMainBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityMainBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_main, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityMainBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.backButton;
      ImageButton backButton = ViewBindings.findChildViewById(rootView, id);
      if (backButton == null) {
        break missingId;
      }

      id = R.id.fragmentContainer;
      FrameLayout fragmentContainer = ViewBindings.findChildViewById(rootView, id);
      if (fragmentContainer == null) {
        break missingId;
      }

      id = R.id.goBtn;
      ImageButton goBtn = ViewBindings.findChildViewById(rootView, id);
      if (goBtn == null) {
        break missingId;
      }

      id = R.id.moreOptionsBtn;
      ImageButton moreOptionsBtn = ViewBindings.findChildViewById(rootView, id);
      if (moreOptionsBtn == null) {
        break missingId;
      }

      id = R.id.progressBar;
      LinearProgressIndicator progressBar = ViewBindings.findChildViewById(rootView, id);
      if (progressBar == null) {
        break missingId;
      }

      id = R.id.refreshBtn;
      ImageButton refreshBtn = ViewBindings.findChildViewById(rootView, id);
      if (refreshBtn == null) {
        break missingId;
      }

      id = R.id.toolbar;
      LinearLayout toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      id = R.id.topSearchBar;
      TextInputEditText topSearchBar = ViewBindings.findChildViewById(rootView, id);
      if (topSearchBar == null) {
        break missingId;
      }

      id = R.id.webIcon;
      ShapeableImageView webIcon = ViewBindings.findChildViewById(rootView, id);
      if (webIcon == null) {
        break missingId;
      }

      return new ActivityMainBinding((MotionLayout) rootView, backButton, fragmentContainer, goBtn,
          moreOptionsBtn, progressBar, refreshBtn, toolbar, topSearchBar, webIcon);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
