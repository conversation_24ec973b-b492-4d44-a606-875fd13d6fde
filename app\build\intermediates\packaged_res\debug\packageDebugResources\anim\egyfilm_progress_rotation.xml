<?xml version="1.0" encoding="utf-8"?>
<set xmlns:android="http://schemas.android.com/apk/res/android">
    <objectAnimator
        android:duration="1200"
        android:interpolator="@android:anim/linear_interpolator"
        android:propertyName="rotation"
        android:repeatCount="infinite"
        android:repeatMode="restart"
        android:valueFrom="0"
        android:valueTo="360"
        android:valueType="floatType" />
    
    <objectAnimator
        android:duration="1000"
        android:interpolator="@android:interpolator/fast_out_slow_in"
        android:propertyName="trimPathStart"
        android:repeatCount="infinite"
        android:repeatMode="reverse"
        android:valueFrom="0"
        android:valueTo="0.75"
        android:valueType="floatType"
        android:startOffset="200" />
    
    <objectAnimator
        android:duration="1000"
        android:interpolator="@android:interpolator/fast_out_slow_in"
        android:propertyName="trimPathEnd"
        android:repeatCount="infinite"
        android:repeatMode="reverse"
        android:valueFrom="0.25"
        android:valueTo="1"
        android:valueType="floatType" />
</set>
