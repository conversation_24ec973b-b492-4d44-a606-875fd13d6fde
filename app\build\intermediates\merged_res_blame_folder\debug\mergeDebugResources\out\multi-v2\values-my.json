{"logs": [{"outputFile": "com.elewashy.egyfilm.app-mergeDebugResources-66:/values-my/values-my.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\c7adff6432088b47c9cce630115db3d4\\transformed\\material-1.12.0\\res\\values-my\\values-my.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,285,386,485,561,652,736,842,971,1056,1119,1184,1274,1349,1408,1499,1562,1627,1686,1757,1819,1876,1995,2053,2114,2169,2242,2374,2465,2549,2649,2735,2824,2965,3043,3120,3243,3335,3412,3470,3521,3587,3659,3741,3812,3890,3965,4039,4111,4190,4298,4395,4476,4562,4654,4728,4807,4893,4947,5023,5091,5174,5255,5317,5381,5444,5512,5624,5735,5839,5952,6013,6068,6150,6237,6317", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,100,98,75,90,83,105,128,84,62,64,89,74,58,90,62,64,58,70,61,56,118,57,60,54,72,131,90,83,99,85,88,140,77,76,122,91,76,57,50,65,71,81,70,77,74,73,71,78,107,96,80,85,91,73,78,85,53,75,67,82,80,61,63,62,67,111,110,103,112,60,54,81,86,79,77", "endOffsets": "280,381,480,556,647,731,837,966,1051,1114,1179,1269,1344,1403,1494,1557,1622,1681,1752,1814,1871,1990,2048,2109,2164,2237,2369,2460,2544,2644,2730,2819,2960,3038,3115,3238,3330,3407,3465,3516,3582,3654,3736,3807,3885,3960,4034,4106,4185,4293,4390,4471,4557,4649,4723,4802,4888,4942,5018,5086,5169,5250,5312,5376,5439,5507,5619,5730,5834,5947,6008,6063,6145,6232,6312,6390"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,71,72,73,74,77,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,195,199,200,202", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3096,3197,3296,3372,3463,4289,4395,4524,7436,7499,7564,7654,7912,14350,14441,14504,14569,14628,14699,14761,14818,14937,14995,15056,15111,15184,15316,15407,15491,15591,15677,15766,15907,15985,16062,16185,16277,16354,16412,16463,16529,16601,16683,16754,16832,16907,16981,17053,17132,17240,17337,17418,17504,17596,17670,17749,17835,17889,17965,18033,18116,18197,18259,18323,18386,18454,18566,18677,18781,18894,18955,19181,19522,19609,19764", "endLines": "5,33,34,35,36,37,45,46,47,71,72,73,74,77,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,195,199,200,202", "endColumns": "12,100,98,75,90,83,105,128,84,62,64,89,74,58,90,62,64,58,70,61,56,118,57,60,54,72,131,90,83,99,85,88,140,77,76,122,91,76,57,50,65,71,81,70,77,74,73,71,78,107,96,80,85,91,73,78,85,53,75,67,82,80,61,63,62,67,111,110,103,112,60,54,81,86,79,77", "endOffsets": "330,3192,3291,3367,3458,3542,4390,4519,4604,7494,7559,7649,7724,7966,14436,14499,14564,14623,14694,14756,14813,14932,14990,15051,15106,15179,15311,15402,15486,15586,15672,15761,15902,15980,16057,16180,16272,16349,16407,16458,16524,16596,16678,16749,16827,16902,16976,17048,17127,17235,17332,17413,17499,17591,17665,17744,17830,17884,17960,18028,18111,18192,18254,18318,18381,18449,18561,18672,18776,18889,18950,19005,19258,19604,19684,19837"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\71f8bc87e80bd35756c6bcfc97f71602\\transformed\\ui-release\\res\\values-my\\values-my.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,199,287,391,495,578,662,761,850,932,1019,1105,1180,1257,1330,1403,1484,1550", "endColumns": "93,87,103,103,82,83,98,88,81,86,85,74,76,72,72,80,65,125", "endOffsets": "194,282,386,490,573,657,756,845,927,1014,1100,1175,1252,1325,1398,1479,1545,1671"}, "to": {"startLines": "48,49,68,69,70,75,76,193,194,196,197,201,203,204,205,207,208,209", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4609,4703,7145,7249,7353,7729,7813,19010,19099,19263,19350,19689,19842,19919,19992,20166,20247,20313", "endColumns": "93,87,103,103,82,83,98,88,81,86,85,74,76,72,72,80,65,125", "endOffsets": "4698,4786,7244,7348,7431,7808,7907,19094,19176,19345,19431,19759,19914,19987,20060,20242,20308,20434"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\68cc43b1f116f6befee1484e2c2e084f\\transformed\\core-1.15.0\\res\\values-my\\values-my.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,158,262,365,467,572,678,797", "endColumns": "102,103,102,101,104,105,118,100", "endOffsets": "153,257,360,462,567,673,792,893"}, "to": {"startLines": "38,39,40,41,42,43,44,206", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3547,3650,3754,3857,3959,4064,4170,20065", "endColumns": "102,103,102,101,104,105,118,100", "endOffsets": "3645,3749,3852,3954,4059,4165,4284,20161"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\e8013e1c38b0d03c268afa9230065a69\\transformed\\appcompat-1.7.0\\res\\values-my\\values-my.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,218,325,441,528,637,760,839,917,1008,1101,1196,1290,1390,1483,1578,1672,1763,1854,1939,2054,2163,2262,2388,2495,2603,2763,2866", "endColumns": "112,106,115,86,108,122,78,77,90,92,94,93,99,92,94,93,90,90,84,114,108,98,125,106,107,159,102,85", "endOffsets": "213,320,436,523,632,755,834,912,1003,1096,1191,1285,1385,1478,1573,1667,1758,1849,1934,2049,2158,2257,2383,2490,2598,2758,2861,2947"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,198", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "335,448,555,671,758,867,990,1069,1147,1238,1331,1426,1520,1620,1713,1808,1902,1993,2084,2169,2284,2393,2492,2618,2725,2833,2993,19436", "endColumns": "112,106,115,86,108,122,78,77,90,92,94,93,99,92,94,93,90,90,84,114,108,98,125,106,107,159,102,85", "endOffsets": "443,550,666,753,862,985,1064,1142,1233,1326,1421,1515,1615,1708,1803,1897,1988,2079,2164,2279,2388,2487,2613,2720,2828,2988,3091,19517"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\1df05e36fa9740a9bf8fb7606a9ee5d1\\transformed\\play-services-base-18.1.0\\res\\values-my\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,456,586,693,836,964,1083,1189,1361,1463,1629,1768,1922,2105,2171,2240", "endColumns": "102,159,129,106,142,127,118,105,171,101,165,138,153,182,65,68,84", "endOffsets": "295,455,585,692,835,963,1082,1188,1360,1462,1628,1767,1921,2104,2170,2239,2324"}, "to": {"startLines": "50,51,52,53,54,55,56,57,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4791,4898,5062,5196,5307,5454,5586,5709,5973,6149,6255,6425,6568,6726,6913,6983,7056", "endColumns": "106,163,133,110,146,131,122,109,175,105,169,142,157,186,69,72,88", "endOffsets": "4893,5057,5191,5302,5449,5581,5704,5814,6144,6250,6420,6563,6721,6908,6978,7051,7140"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\695db91b505a701047f9507726e12587\\transformed\\material3-release\\res\\values-my\\values-my.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,192,326,437,571,686,786,903,1052,1176,1339,1425,1524,1617,1719,1839,1966,2070,2196,2327,2471,2639,2761,2878,2997,3124,3218,3315,3446,3583,3685,3797,3902,4028,4157,4260,4363,4444,4542,4638,4746,4833,4919,5038,5118,5202,5302,5404,5500,5598,5685,5792,5891,5992,6113,6193,6316", "endColumns": "136,133,110,133,114,99,116,148,123,162,85,98,92,101,119,126,103,125,130,143,167,121,116,118,126,93,96,130,136,101,111,104,125,128,102,102,80,97,95,107,86,85,118,79,83,99,101,95,97,86,106,98,100,120,79,122,117", "endOffsets": "187,321,432,566,681,781,898,1047,1171,1334,1420,1519,1612,1714,1834,1961,2065,2191,2322,2466,2634,2756,2873,2992,3119,3213,3310,3441,3578,3680,3792,3897,4023,4152,4255,4358,4439,4537,4633,4741,4828,4914,5033,5113,5197,5297,5399,5495,5593,5680,5787,5886,5987,6108,6188,6311,6429"}, "to": {"startLines": "78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7971,8108,8242,8353,8487,8602,8702,8819,8968,9092,9255,9341,9440,9533,9635,9755,9882,9986,10112,10243,10387,10555,10677,10794,10913,11040,11134,11231,11362,11499,11601,11713,11818,11944,12073,12176,12279,12360,12458,12554,12662,12749,12835,12954,13034,13118,13218,13320,13416,13514,13601,13708,13807,13908,14029,14109,14232", "endColumns": "136,133,110,133,114,99,116,148,123,162,85,98,92,101,119,126,103,125,130,143,167,121,116,118,126,93,96,130,136,101,111,104,125,128,102,102,80,97,95,107,86,85,118,79,83,99,101,95,97,86,106,98,100,120,79,122,117", "endOffsets": "8103,8237,8348,8482,8597,8697,8814,8963,9087,9250,9336,9435,9528,9630,9750,9877,9981,10107,10238,10382,10550,10672,10789,10908,11035,11129,11226,11357,11494,11596,11708,11813,11939,12068,12171,12274,12355,12453,12549,12657,12744,12830,12949,13029,13113,13213,13315,13411,13509,13596,13703,13802,13903,14024,14104,14227,14345"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\52ee2787b45be912bfe498d0191617c7\\transformed\\foundation-release\\res\\values-my\\values-my.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,164", "endColumns": "108,107", "endOffsets": "159,267"}, "to": {"startLines": "210,211", "startColumns": "4,4", "startOffsets": "20439,20548", "endColumns": "108,107", "endOffsets": "20543,20651"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\233292c89c1b85b15f1b8e2d4e33fbe5\\transformed\\play-services-basement-18.3.0\\res\\values-my\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "149", "endOffsets": "344"}, "to": {"startLines": "58", "startColumns": "4", "startOffsets": "5819", "endColumns": "153", "endOffsets": "5968"}}]}]}