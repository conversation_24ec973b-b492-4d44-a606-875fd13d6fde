// Generated by view binder compiler. Do not edit!
package com.elewashy.egyfilm.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageButton;
import android.widget.ImageView;
import android.widget.ProgressBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.cardview.widget.CardView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.elewashy.egyfilm.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemDownloadBinding implements ViewBinding {
  @NonNull
  private final CardView rootView;

  @NonNull
  public final ImageButton btnCancel;

  @NonNull
  public final ImageButton btnPauseResume;

  @NonNull
  public final CardView cvCancel;

  @NonNull
  public final CardView cvFileIcon;

  @NonNull
  public final CardView cvPauseResume;

  @NonNull
  public final ImageView ivFileIcon;

  @NonNull
  public final ProgressBar pbDownloadProgress;

  @NonNull
  public final TextView tvDownloadSpeed;

  @NonNull
  public final TextView tvDownloadStatus;

  @NonNull
  public final TextView tvFileName;

  private ItemDownloadBinding(@NonNull CardView rootView, @NonNull ImageButton btnCancel,
      @NonNull ImageButton btnPauseResume, @NonNull CardView cvCancel, @NonNull CardView cvFileIcon,
      @NonNull CardView cvPauseResume, @NonNull ImageView ivFileIcon,
      @NonNull ProgressBar pbDownloadProgress, @NonNull TextView tvDownloadSpeed,
      @NonNull TextView tvDownloadStatus, @NonNull TextView tvFileName) {
    this.rootView = rootView;
    this.btnCancel = btnCancel;
    this.btnPauseResume = btnPauseResume;
    this.cvCancel = cvCancel;
    this.cvFileIcon = cvFileIcon;
    this.cvPauseResume = cvPauseResume;
    this.ivFileIcon = ivFileIcon;
    this.pbDownloadProgress = pbDownloadProgress;
    this.tvDownloadSpeed = tvDownloadSpeed;
    this.tvDownloadStatus = tvDownloadStatus;
    this.tvFileName = tvFileName;
  }

  @Override
  @NonNull
  public CardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemDownloadBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemDownloadBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_download, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemDownloadBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_cancel;
      ImageButton btnCancel = ViewBindings.findChildViewById(rootView, id);
      if (btnCancel == null) {
        break missingId;
      }

      id = R.id.btn_pause_resume;
      ImageButton btnPauseResume = ViewBindings.findChildViewById(rootView, id);
      if (btnPauseResume == null) {
        break missingId;
      }

      id = R.id.cv_cancel;
      CardView cvCancel = ViewBindings.findChildViewById(rootView, id);
      if (cvCancel == null) {
        break missingId;
      }

      id = R.id.cv_file_icon;
      CardView cvFileIcon = ViewBindings.findChildViewById(rootView, id);
      if (cvFileIcon == null) {
        break missingId;
      }

      id = R.id.cv_pause_resume;
      CardView cvPauseResume = ViewBindings.findChildViewById(rootView, id);
      if (cvPauseResume == null) {
        break missingId;
      }

      id = R.id.iv_file_icon;
      ImageView ivFileIcon = ViewBindings.findChildViewById(rootView, id);
      if (ivFileIcon == null) {
        break missingId;
      }

      id = R.id.pb_download_progress;
      ProgressBar pbDownloadProgress = ViewBindings.findChildViewById(rootView, id);
      if (pbDownloadProgress == null) {
        break missingId;
      }

      id = R.id.tv_download_speed;
      TextView tvDownloadSpeed = ViewBindings.findChildViewById(rootView, id);
      if (tvDownloadSpeed == null) {
        break missingId;
      }

      id = R.id.tv_download_status;
      TextView tvDownloadStatus = ViewBindings.findChildViewById(rootView, id);
      if (tvDownloadStatus == null) {
        break missingId;
      }

      id = R.id.tv_file_name;
      TextView tvFileName = ViewBindings.findChildViewById(rootView, id);
      if (tvFileName == null) {
        break missingId;
      }

      return new ItemDownloadBinding((CardView) rootView, btnCancel, btnPauseResume, cvCancel,
          cvFileIcon, cvPauseResume, ivFileIcon, pbDownloadProgress, tvDownloadSpeed,
          tvDownloadStatus, tvFileName);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
