{"logs": [{"outputFile": "com.elewashy.egyfilm.app-mergeDebugResources-66:/values-en-rAU/values-en-rAU.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\68cc43b1f116f6befee1484e2c2e084f\\transformed\\core-1.15.0\\res\\values-en-rAU\\values-en-rAU.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,451,555,658,774", "endColumns": "95,101,98,98,103,102,115,100", "endOffsets": "146,248,347,446,550,653,769,870"}, "to": {"startLines": "29,30,31,32,33,34,35,109", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2762,2858,2960,3059,3158,3262,3365,10927", "endColumns": "95,101,98,98,103,102,115,100", "endOffsets": "2853,2955,3054,3153,3257,3360,3476,11023"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\71f8bc87e80bd35756c6bcfc97f71602\\transformed\\ui-release\\res\\values-en-rAU\\values-en-rAU.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,197,279,373,472,559,641,730,819,903,981,1063,1136,1212,1284,1354,1431,1497", "endColumns": "91,81,93,98,86,81,88,88,83,77,81,72,75,71,69,76,65,120", "endOffsets": "192,274,368,467,554,636,725,814,898,976,1058,1131,1207,1279,1349,1426,1492,1613"}, "to": {"startLines": "36,37,38,39,40,41,42,100,101,102,103,105,106,107,108,110,111,112", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3481,3573,3655,3749,3848,3935,4017,10220,10309,10393,10471,10636,10709,10785,10857,11028,11105,11171", "endColumns": "91,81,93,98,86,81,88,88,83,77,81,72,75,71,69,76,65,120", "endOffsets": "3568,3650,3744,3843,3930,4012,4101,10304,10388,10466,10548,10704,10780,10852,10922,11100,11166,11287"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\e8013e1c38b0d03c268afa9230065a69\\transformed\\appcompat-1.7.0\\res\\values-en-rAU\\values-en-rAU.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,209,309,417,501,601,716,794,869,960,1053,1148,1242,1342,1435,1530,1624,1715,1806,1888,1991,2094,2193,2298,2402,2506,2662,2762", "endColumns": "103,99,107,83,99,114,77,74,90,92,94,93,99,92,94,93,90,90,81,102,102,98,104,103,103,155,99,82", "endOffsets": "204,304,412,496,596,711,789,864,955,1048,1143,1237,1337,1430,1525,1619,1710,1801,1883,1986,2089,2188,2293,2397,2501,2657,2757,2840"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,104", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,209,309,417,501,601,716,794,869,960,1053,1148,1242,1342,1435,1530,1624,1715,1806,1888,1991,2094,2193,2298,2402,2506,2662,10553", "endColumns": "103,99,107,83,99,114,77,74,90,92,94,93,99,92,94,93,90,90,81,102,102,98,104,103,103,155,99,82", "endOffsets": "204,304,412,496,596,711,789,864,955,1048,1143,1237,1337,1430,1525,1619,1710,1801,1883,1986,2089,2188,2293,2397,2501,2657,2757,10631"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\695db91b505a701047f9507726e12587\\transformed\\material3-release\\res\\values-en-rAU\\values-en-rAU.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,289,400,514,613,708,820,956,1072,1208,1292,1391,1482,1579,1698,1823,1927,2054,2177,2305,2466,2587,2703,2826,2951,3043,3141,3258,3382,3479,3581,3683,3813,3952,4058,4157,4235,4331,4425,4530,4617,4704,4806,4888,4972,5073,5174,5274,5373,5461,5567,5668,5772,5892,5974,6074", "endColumns": "117,115,110,113,98,94,111,135,115,135,83,98,90,96,118,124,103,126,122,127,160,120,115,122,124,91,97,116,123,96,101,101,129,138,105,98,77,95,93,104,86,86,101,81,83,100,100,99,98,87,105,100,103,119,81,99,94", "endOffsets": "168,284,395,509,608,703,815,951,1067,1203,1287,1386,1477,1574,1693,1818,1922,2049,2172,2300,2461,2582,2698,2821,2946,3038,3136,3253,3377,3474,3576,3678,3808,3947,4053,4152,4230,4326,4420,4525,4612,4699,4801,4883,4967,5068,5169,5269,5368,5456,5562,5663,5767,5887,5969,6069,6164"}, "to": {"startLines": "43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4106,4224,4340,4451,4565,4664,4759,4871,5007,5123,5259,5343,5442,5533,5630,5749,5874,5978,6105,6228,6356,6517,6638,6754,6877,7002,7094,7192,7309,7433,7530,7632,7734,7864,8003,8109,8208,8286,8382,8476,8581,8668,8755,8857,8939,9023,9124,9225,9325,9424,9512,9618,9719,9823,9943,10025,10125", "endColumns": "117,115,110,113,98,94,111,135,115,135,83,98,90,96,118,124,103,126,122,127,160,120,115,122,124,91,97,116,123,96,101,101,129,138,105,98,77,95,93,104,86,86,101,81,83,100,100,99,98,87,105,100,103,119,81,99,94", "endOffsets": "4219,4335,4446,4560,4659,4754,4866,5002,5118,5254,5338,5437,5528,5625,5744,5869,5973,6100,6223,6351,6512,6633,6749,6872,6997,7089,7187,7304,7428,7525,7627,7729,7859,7998,8104,8203,8281,8377,8471,8576,8663,8750,8852,8934,9018,9119,9220,9320,9419,9507,9613,9714,9818,9938,10020,10120,10215"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\52ee2787b45be912bfe498d0191617c7\\transformed\\foundation-release\\res\\values-en-rAU\\values-en-rAU.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,141", "endColumns": "85,84", "endOffsets": "136,221"}, "to": {"startLines": "113,114", "startColumns": "4,4", "startOffsets": "11292,11378", "endColumns": "85,84", "endOffsets": "11373,11458"}}]}]}