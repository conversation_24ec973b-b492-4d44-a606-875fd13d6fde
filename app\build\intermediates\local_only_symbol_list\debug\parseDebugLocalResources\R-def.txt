R_DEF: Internal format may change without notice
local
anim activity_fade_out_down
anim activity_slide_in_up
anim bounce_in
anim egyfilm_progress_rotation
anim fade_in
anim fade_out
anim fade_out_slide_down
anim pulse_animation
anim slide_in_right
anim slide_out_left
anim slide_up
anim slow_progress_rotation
animator egyfilm_progress_animator
animator enhanced_loading_animator
array myColors
color background
color black
color colorPrimary
color egyfilm_background
color egyfilm_black
color egyfilm_dark_gray
color egyfilm_gray
color egyfilm_light_gray
color egyfilm_red
color egyfilm_red_dark
color gray_text
color ic_launcher_background
color primary
color primary_dark
color surface
color text_primary
color text_secondary
color white
drawable bg_gradient
drawable circular_button_background
drawable circular_button_background_stroke
drawable custom_design
drawable custom_tabs_textview
drawable download_card_gradient
drawable egyfilm_progress_drawable
drawable enhanced_loading_arc
drawable enhanced_loading_drawable
drawable ic_add
drawable ic_back
drawable ic_cancel
drawable ic_d_google
drawable ic_d_youtube
drawable ic_delete
drawable ic_download
drawable ic_download_file
drawable ic_file_complete
drawable ic_forward
drawable ic_fullscreen
drawable ic_fullscreen_exit
drawable ic_globe
drawable ic_home
drawable ic_launcher_background
drawable ic_launcher_foreground
drawable ic_more_vert
drawable ic_open_file
drawable ic_pause
drawable ic_play_arrow
drawable ic_refresh
drawable ic_search
drawable ic_settings
drawable loading_arc
drawable no_internet_background
drawable progress_circle
drawable progress_indeterminate_anim
drawable progress_vector
drawable search_bar_background
id adBlockerLastUpdate
id adBlockerProgress
id adBlockerStatus
id backBtn
id backButton
id btn_cancel
id btn_pause_resume
id customView
id cv_cancel
id cv_file_icon
id cv_pause_resume
id downloadProgress
id downloadStatus
id downloadsBtn
id end
id filterUpdatesCard
id forwardBtn
id fragmentContainer
id fullscreenBtn
id goBtn
id iv_file_icon
id layout_no_downloads
id loadingProgress
id moreOptionsBtn
id noInternetText
id openLinksLastUpdate
id openLinksProgress
id openLinksStatus
id pb_download_progress
id progressBar
id recyclerView
id refreshBtn
id retryButton
id rv_downloads
id searchView
id settingsBtn
id splashLogo
id start
id swipeRefreshLayout
id toolbar
id toolbar_downloads
id topSearchBar
id tv_download_speed
id tv_download_status
id tv_file_name
id tv_no_downloads
id updateAdBlockerBtn
id updateAllFiltersBtn
id updateButton
id updateDialog
id updateMessage
id updateOpenLinksBtn
id updateTitle
id updateValidLinksBtn
id validLinksLastUpdate
id validLinksProgress
id validLinksStatus
id viewAllBtn
id webIcon
id webView
layout activity_download
layout activity_filter_updates
layout activity_main
layout activity_settings
layout activity_splash
layout bottom_sheet_more_options
layout fragment_browse
layout fragment_home
layout item_download
layout more_features
mipmap ic_launcher
mipmap ic_launcher_foreground
mipmap ic_launcher_round
string adblocker_filter
string app_name
string back
string cancel_button_desc
string delete_button_desc
string download_icon_desc
string downloads
string enter_url
string filter_updates
string filter_updates_desc
string forward
string fullscreen
string gcm_defaultSenderId
string google_api_key
string google_app_id
string google_crash_reporting_api_key
string google_storage_bucket
string homePage
string home_btn
string last_updated
string more_options
string never_updated
string open_file_button_desc
string open_filter
string pause_resume_button_desc
string project_id
string refresh_btn
string retry_button_desc
string search_hint
string settings
string settings_btn
string update
string update_all_filters
string update_failed
string update_success
string updating
string valid_filter
string view_all
style Theme.egyfilm
style Theme.egyfilm.NoActionBar.Fullscreen
style egyfilmProgressStyle
style roundCornerDialog
style shape
xml activity_main_scene
xml backup_rules
xml data_extraction_rules
xml network_security_config
xml provider_paths
