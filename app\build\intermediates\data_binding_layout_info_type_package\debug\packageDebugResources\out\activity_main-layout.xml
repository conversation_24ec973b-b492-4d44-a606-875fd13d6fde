<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_main" modulePackage="com.elewashy.egyfilm" filePath="app\src\main\res\layout\activity_main.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.motion.widget.MotionLayout"><Targets><Target tag="layout/activity_main_0" view="androidx.constraintlayout.motion.widget.MotionLayout"><Expressions/><location startLine="1" startOffset="0" endLine="134" endOffset="54"/></Target><Target id="@+id/toolbar" view="LinearLayout"><Expressions/><location startLine="10" startOffset="4" endLine="119" endOffset="18"/></Target><Target id="@+id/backButton" view="ImageButton"><Expressions/><location startLine="28" startOffset="12" endLine="36" endOffset="41"/></Target><Target id="@+id/webIcon" view="com.google.android.material.imageview.ShapeableImageView"><Expressions/><location startLine="42" startOffset="12" endLine="48" endOffset="51"/></Target><Target id="@+id/topSearchBar" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="54" startOffset="12" endLine="64" endOffset="61"/></Target><Target id="@+id/refreshBtn" view="ImageButton"><Expressions/><location startLine="71" startOffset="12" endLine="79" endOffset="41"/></Target><Target id="@+id/goBtn" view="ImageButton"><Expressions/><location startLine="85" startOffset="12" endLine="93" endOffset="41"/></Target><Target id="@+id/moreOptionsBtn" view="ImageButton"><Expressions/><location startLine="99" startOffset="12" endLine="107" endOffset="41"/></Target><Target id="@+id/progressBar" view="com.google.android.material.progressindicator.LinearProgressIndicator"><Expressions/><location startLine="111" startOffset="8" endLine="117" endOffset="39"/></Target><Target id="@+id/fragmentContainer" view="FrameLayout"><Expressions/><location startLine="122" startOffset="4" endLine="130" endOffset="59"/></Target></Targets></Layout>