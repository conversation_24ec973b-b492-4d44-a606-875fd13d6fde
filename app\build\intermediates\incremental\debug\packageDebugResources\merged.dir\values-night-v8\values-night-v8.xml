<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:ns1="http://schemas.android.com/tools">
    <style name="Theme.egyfilm" parent="Theme.MaterialComponents.NoActionBar">
        
        <item name="colorPrimary">@color/egyfilm_red</item>
        <item name="colorPrimaryVariant">@color/egyfilm_red_dark</item>
        <item name="colorOnPrimary">@color/white</item>
        
        <item name="colorSecondary">@color/egyfilm_red</item>
        <item name="colorSecondaryVariant">@color/egyfilm_red_dark</item>
        <item name="colorOnSecondary">@color/white</item>
        
        <item name="android:windowBackground">@color/egyfilm_black</item>
        <item name="backgroundColor">@color/egyfilm_black</item>
        <item name="colorSurface">@color/egyfilm_dark_gray</item>
        <item name="colorOnSurface">@color/white</item>
        
        <item name="android:textColorPrimary">@color/white</item>
        <item name="android:textColorSecondary">@color/egyfilm_gray</item>
        
        <item name="android:statusBarColor">@color/egyfilm_black</item>
        
        <item name="android:navigationBarColor">@color/egyfilm_black</item>
    </style>
    <style name="Theme.egyfilm.NoActionBar.Fullscreen" parent="Theme.egyfilm">
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowLayoutInDisplayCutoutMode" ns1:targetApi="o_mr1">shortEdges</item>
        <item name="android:windowTranslucentStatus">true</item>
        <item name="android:windowTranslucentNavigation">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:fitsSystemWindows">false</item>
    </style>
</resources>