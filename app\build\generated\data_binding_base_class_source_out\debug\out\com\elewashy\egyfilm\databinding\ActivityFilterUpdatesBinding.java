// Generated by view binder compiler. Do not edit!
package com.elewashy.egyfilm.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageButton;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.elewashy.egyfilm.R;
import com.google.android.material.button.MaterialButton;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityFilterUpdatesBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final TextView adBlockerLastUpdate;

  @NonNull
  public final ProgressBar adBlockerProgress;

  @NonNull
  public final TextView adBlockerStatus;

  @NonNull
  public final ImageButton backButton;

  @NonNull
  public final TextView openLinksLastUpdate;

  @NonNull
  public final ProgressBar openLinksProgress;

  @NonNull
  public final TextView openLinksStatus;

  @NonNull
  public final MaterialButton updateAdBlockerBtn;

  @NonNull
  public final MaterialButton updateAllFiltersBtn;

  @NonNull
  public final MaterialButton updateOpenLinksBtn;

  @NonNull
  public final MaterialButton updateValidLinksBtn;

  @NonNull
  public final TextView validLinksLastUpdate;

  @NonNull
  public final ProgressBar validLinksProgress;

  @NonNull
  public final TextView validLinksStatus;

  private ActivityFilterUpdatesBinding(@NonNull LinearLayout rootView,
      @NonNull TextView adBlockerLastUpdate, @NonNull ProgressBar adBlockerProgress,
      @NonNull TextView adBlockerStatus, @NonNull ImageButton backButton,
      @NonNull TextView openLinksLastUpdate, @NonNull ProgressBar openLinksProgress,
      @NonNull TextView openLinksStatus, @NonNull MaterialButton updateAdBlockerBtn,
      @NonNull MaterialButton updateAllFiltersBtn, @NonNull MaterialButton updateOpenLinksBtn,
      @NonNull MaterialButton updateValidLinksBtn, @NonNull TextView validLinksLastUpdate,
      @NonNull ProgressBar validLinksProgress, @NonNull TextView validLinksStatus) {
    this.rootView = rootView;
    this.adBlockerLastUpdate = adBlockerLastUpdate;
    this.adBlockerProgress = adBlockerProgress;
    this.adBlockerStatus = adBlockerStatus;
    this.backButton = backButton;
    this.openLinksLastUpdate = openLinksLastUpdate;
    this.openLinksProgress = openLinksProgress;
    this.openLinksStatus = openLinksStatus;
    this.updateAdBlockerBtn = updateAdBlockerBtn;
    this.updateAllFiltersBtn = updateAllFiltersBtn;
    this.updateOpenLinksBtn = updateOpenLinksBtn;
    this.updateValidLinksBtn = updateValidLinksBtn;
    this.validLinksLastUpdate = validLinksLastUpdate;
    this.validLinksProgress = validLinksProgress;
    this.validLinksStatus = validLinksStatus;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityFilterUpdatesBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityFilterUpdatesBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_filter_updates, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityFilterUpdatesBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.adBlockerLastUpdate;
      TextView adBlockerLastUpdate = ViewBindings.findChildViewById(rootView, id);
      if (adBlockerLastUpdate == null) {
        break missingId;
      }

      id = R.id.adBlockerProgress;
      ProgressBar adBlockerProgress = ViewBindings.findChildViewById(rootView, id);
      if (adBlockerProgress == null) {
        break missingId;
      }

      id = R.id.adBlockerStatus;
      TextView adBlockerStatus = ViewBindings.findChildViewById(rootView, id);
      if (adBlockerStatus == null) {
        break missingId;
      }

      id = R.id.backButton;
      ImageButton backButton = ViewBindings.findChildViewById(rootView, id);
      if (backButton == null) {
        break missingId;
      }

      id = R.id.openLinksLastUpdate;
      TextView openLinksLastUpdate = ViewBindings.findChildViewById(rootView, id);
      if (openLinksLastUpdate == null) {
        break missingId;
      }

      id = R.id.openLinksProgress;
      ProgressBar openLinksProgress = ViewBindings.findChildViewById(rootView, id);
      if (openLinksProgress == null) {
        break missingId;
      }

      id = R.id.openLinksStatus;
      TextView openLinksStatus = ViewBindings.findChildViewById(rootView, id);
      if (openLinksStatus == null) {
        break missingId;
      }

      id = R.id.updateAdBlockerBtn;
      MaterialButton updateAdBlockerBtn = ViewBindings.findChildViewById(rootView, id);
      if (updateAdBlockerBtn == null) {
        break missingId;
      }

      id = R.id.updateAllFiltersBtn;
      MaterialButton updateAllFiltersBtn = ViewBindings.findChildViewById(rootView, id);
      if (updateAllFiltersBtn == null) {
        break missingId;
      }

      id = R.id.updateOpenLinksBtn;
      MaterialButton updateOpenLinksBtn = ViewBindings.findChildViewById(rootView, id);
      if (updateOpenLinksBtn == null) {
        break missingId;
      }

      id = R.id.updateValidLinksBtn;
      MaterialButton updateValidLinksBtn = ViewBindings.findChildViewById(rootView, id);
      if (updateValidLinksBtn == null) {
        break missingId;
      }

      id = R.id.validLinksLastUpdate;
      TextView validLinksLastUpdate = ViewBindings.findChildViewById(rootView, id);
      if (validLinksLastUpdate == null) {
        break missingId;
      }

      id = R.id.validLinksProgress;
      ProgressBar validLinksProgress = ViewBindings.findChildViewById(rootView, id);
      if (validLinksProgress == null) {
        break missingId;
      }

      id = R.id.validLinksStatus;
      TextView validLinksStatus = ViewBindings.findChildViewById(rootView, id);
      if (validLinksStatus == null) {
        break missingId;
      }

      return new ActivityFilterUpdatesBinding((LinearLayout) rootView, adBlockerLastUpdate,
          adBlockerProgress, adBlockerStatus, backButton, openLinksLastUpdate, openLinksProgress,
          openLinksStatus, updateAdBlockerBtn, updateAllFiltersBtn, updateOpenLinksBtn,
          updateValidLinksBtn, validLinksLastUpdate, validLinksProgress, validLinksStatus);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
