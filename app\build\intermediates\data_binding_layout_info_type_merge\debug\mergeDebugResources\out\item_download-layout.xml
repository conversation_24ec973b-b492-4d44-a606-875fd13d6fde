<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_download" modulePackage="com.elewashy.egyfilm" filePath="app\src\main\res\layout\item_download.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.cardview.widget.CardView"><Targets><Target tag="layout/item_download_0" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="1" startOffset="0" endLine="145" endOffset="35"/></Target><Target id="@+id/cv_file_icon" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="19" startOffset="8" endLine="39" endOffset="43"/></Target><Target id="@+id/iv_file_icon" view="ImageView"><Expressions/><location startLine="30" startOffset="12" endLine="37" endOffset="73"/></Target><Target id="@+id/tv_file_name" view="TextView"><Expressions/><location startLine="41" startOffset="8" endLine="56" endOffset="47"/></Target><Target id="@+id/pb_download_progress" view="ProgressBar"><Expressions/><location startLine="58" startOffset="8" endLine="71" endOffset="33"/></Target><Target id="@+id/tv_download_status" view="TextView"><Expressions/><location startLine="73" startOffset="8" endLine="83" endOffset="63"/></Target><Target id="@+id/tv_download_speed" view="TextView"><Expressions/><location startLine="85" startOffset="8" endLine="96" endOffset="35"/></Target><Target id="@+id/cv_pause_resume" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="98" startOffset="8" endLine="119" endOffset="43"/></Target><Target id="@+id/btn_pause_resume" view="ImageButton"><Expressions/><location startLine="110" startOffset="12" endLine="117" endOffset="41"/></Target><Target id="@+id/cv_cancel" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="121" startOffset="8" endLine="141" endOffset="43"/></Target><Target id="@+id/btn_cancel" view="ImageButton"><Expressions/><location startLine="132" startOffset="12" endLine="139" endOffset="41"/></Target></Targets></Layout>