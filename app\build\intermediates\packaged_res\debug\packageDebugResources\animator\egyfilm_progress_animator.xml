<?xml version="1.0" encoding="utf-8"?>
<set xmlns:android="http://schemas.android.com/apk/res/android"
    android:ordering="together">
    <objectAnimator
        android:propertyName="trimPathStart"
        android:valueFrom="0"
        android:valueTo="0.75"
        android:valueType="floatType"
        android:duration="1000"
        android:interpolator="@android:interpolator/fast_out_slow_in"
        android:repeatCount="infinite"
        android:repeatMode="reverse"
        android:startOffset="100"/>
    <objectAnimator
        android:propertyName="trimPathEnd"
        android:valueFrom="0.25"
        android:valueTo="1"
        android:valueType="floatType"
        android:duration="1000"
        android:interpolator="@android:interpolator/fast_out_slow_in"
        android:repeatCount="infinite"
        android:repeatMode="reverse"/>
    <objectAnimator
        android:propertyName="rotation"
        android:valueFrom="0"
        android:valueTo="360"
        android:valueType="floatType"
        android:duration="1200"
        android:interpolator="@android:anim/linear_interpolator"
        android:repeatCount="infinite"
        android:repeatMode="restart"/>
</set>
