<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_splash" modulePackage="com.elewashy.egyfilm" filePath="app\src\main\res\layout\activity_splash.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/activity_splash_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="155" endOffset="51"/></Target><Target id="@+id/splashLogo" view="ImageView"><Expressions/><location startLine="7" startOffset="4" endLine="17" endOffset="50"/></Target><Target id="@+id/loadingProgress" view="com.google.android.material.progressindicator.CircularProgressIndicator"><Expressions/><location startLine="19" startOffset="4" endLine="38" endOffset="62"/></Target><Target id="@+id/noInternetText" view="TextView"><Expressions/><location startLine="40" startOffset="4" endLine="58" endOffset="67"/></Target><Target id="@+id/retryButton" view="Button"><Expressions/><location startLine="60" startOffset="4" endLine="74" endOffset="66"/></Target><Target id="@+id/updateDialog" view="LinearLayout"><Expressions/><location startLine="77" startOffset="4" endLine="153" endOffset="18"/></Target><Target id="@+id/updateTitle" view="TextView"><Expressions/><location startLine="93" startOffset="8" endLine="102" endOffset="38"/></Target><Target id="@+id/updateMessage" view="TextView"><Expressions/><location startLine="104" startOffset="8" endLine="112" endOffset="37"/></Target><Target id="@+id/downloadProgress" view="com.google.android.material.progressindicator.LinearProgressIndicator"><Expressions/><location startLine="114" startOffset="8" endLine="122" endOffset="38"/></Target><Target id="@+id/downloadStatus" view="TextView"><Expressions/><location startLine="124" startOffset="8" endLine="133" endOffset="39"/></Target><Target id="@+id/updateButton" view="Button"><Expressions/><location startLine="141" startOffset="12" endLine="149" endOffset="43"/></Target></Targets></Layout>