{"logs": [{"outputFile": "com.elewashy.egyfilm.app-mergeDebugResources-66:/values-uz/values-uz.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\695db91b505a701047f9507726e12587\\transformed\\material3-release\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,172,287,401,515,612,710,825,958,1067,1209,1293,1397,1491,1589,1703,1824,1933,2058,2181,2311,2479,2604,2725,2849,2970,3065,3163,3280,3406,3510,3620,3727,3850,3978,4091,4195,4279,4375,4469,4599,4687,4773,4874,4954,5038,5138,5242,5338,5437,5525,5633,5733,5836,5975,6055,6171", "endColumns": "116,114,113,113,96,97,114,132,108,141,83,103,93,97,113,120,108,124,122,129,167,124,120,123,120,94,97,116,125,103,109,106,122,127,112,103,83,95,93,129,87,85,100,79,83,99,103,95,98,87,107,99,102,138,79,115,102", "endOffsets": "167,282,396,510,607,705,820,953,1062,1204,1288,1392,1486,1584,1698,1819,1928,2053,2176,2306,2474,2599,2720,2844,2965,3060,3158,3275,3401,3505,3615,3722,3845,3973,4086,4190,4274,4370,4464,4594,4682,4768,4869,4949,5033,5133,5237,5333,5432,5520,5628,5728,5831,5970,6050,6166,6269"}, "to": {"startLines": "78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7736,7853,7968,8082,8196,8293,8391,8506,8639,8748,8890,8974,9078,9172,9270,9384,9505,9614,9739,9862,9992,10160,10285,10406,10530,10651,10746,10844,10961,11087,11191,11301,11408,11531,11659,11772,11876,11960,12056,12150,12280,12368,12454,12555,12635,12719,12819,12923,13019,13118,13206,13314,13414,13517,13656,13736,13852", "endColumns": "116,114,113,113,96,97,114,132,108,141,83,103,93,97,113,120,108,124,122,129,167,124,120,123,120,94,97,116,125,103,109,106,122,127,112,103,83,95,93,129,87,85,100,79,83,99,103,95,98,87,107,99,102,138,79,115,102", "endOffsets": "7848,7963,8077,8191,8288,8386,8501,8634,8743,8885,8969,9073,9167,9265,9379,9500,9609,9734,9857,9987,10155,10280,10401,10525,10646,10741,10839,10956,11082,11186,11296,11403,11526,11654,11767,11871,11955,12051,12145,12275,12363,12449,12550,12630,12714,12814,12918,13014,13113,13201,13309,13409,13512,13651,13731,13847,13950"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\e8013e1c38b0d03c268afa9230065a69\\transformed\\appcompat-1.7.0\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,305,405,487,587,704,789,867,958,1051,1146,1240,1334,1427,1522,1617,1708,1800,1884,1994,2100,2200,2308,2414,2516,2677,2776", "endColumns": "104,94,99,81,99,116,84,77,90,92,94,93,93,92,94,94,90,91,83,109,105,99,107,105,101,160,98,83", "endOffsets": "205,300,400,482,582,699,784,862,953,1046,1141,1235,1329,1422,1517,1612,1703,1795,1879,1989,2095,2195,2303,2409,2511,2672,2771,2855"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,198", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "333,438,533,633,715,815,932,1017,1095,1186,1279,1374,1468,1562,1655,1750,1845,1936,2028,2112,2222,2328,2428,2536,2642,2744,2905,19044", "endColumns": "104,94,99,81,99,116,84,77,90,92,94,93,93,92,94,94,90,91,83,109,105,99,107,105,101,160,98,83", "endOffsets": "433,528,628,710,810,927,1012,1090,1181,1274,1369,1463,1557,1650,1745,1840,1931,2023,2107,2217,2323,2423,2531,2637,2739,2900,2999,19123"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\1df05e36fa9740a9bf8fb7606a9ee5d1\\transformed\\play-services-base-18.1.0\\res\\values-uz\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,294,440,565,670,811,940,1056,1158,1326,1430,1585,1713,1863,2021,2083,2140", "endColumns": "100,145,124,104,140,128,115,101,167,103,154,127,149,157,61,56,75", "endOffsets": "293,439,564,669,810,939,1055,1157,1325,1429,1584,1712,1862,2020,2082,2139,2215"}, "to": {"startLines": "50,51,52,53,54,55,56,57,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4651,4756,4906,5035,5144,5289,5422,5542,5798,5970,6078,6237,6369,6523,6685,6751,6812", "endColumns": "104,149,128,108,144,132,119,105,171,107,158,131,153,161,65,60,79", "endOffsets": "4751,4901,5030,5139,5284,5417,5537,5643,5965,6073,6232,6364,6518,6680,6746,6807,6887"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\68cc43b1f116f6befee1484e2c2e084f\\transformed\\core-1.15.0\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,259,360,460,568,672,791", "endColumns": "101,101,100,99,107,103,118,100", "endOffsets": "152,254,355,455,563,667,786,887"}, "to": {"startLines": "38,39,40,41,42,43,44,206", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3425,3527,3629,3730,3830,3938,4042,19663", "endColumns": "101,101,100,99,107,103,118,100", "endOffsets": "3522,3624,3725,3825,3933,4037,4156,19759"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\c7adff6432088b47c9cce630115db3d4\\transformed\\material-1.12.0\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,283,360,440,523,617,704,799,926,1010,1070,1134,1237,1307,1374,1483,1546,1613,1672,1746,1809,1863,1978,2036,2098,2152,2227,2356,2446,2526,2619,2703,2792,2933,3015,3097,3236,3322,3406,3466,3517,3583,3656,3734,3805,3886,3958,4035,4110,4181,4282,4376,4455,4551,4645,4719,4795,4881,4934,5021,5087,5172,5263,5325,5389,5452,5521,5623,5724,5820,5921,5985,6040,6123,6209,6286", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,76,79,82,93,86,94,126,83,59,63,102,69,66,108,62,66,58,73,62,53,114,57,61,53,74,128,89,79,92,83,88,140,81,81,138,85,83,59,50,65,72,77,70,80,71,76,74,70,100,93,78,95,93,73,75,85,52,86,65,84,90,61,63,62,68,101,100,95,100,63,54,82,85,76,73", "endOffsets": "278,355,435,518,612,699,794,921,1005,1065,1129,1232,1302,1369,1478,1541,1608,1667,1741,1804,1858,1973,2031,2093,2147,2222,2351,2441,2521,2614,2698,2787,2928,3010,3092,3231,3317,3401,3461,3512,3578,3651,3729,3800,3881,3953,4030,4105,4176,4277,4371,4450,4546,4640,4714,4790,4876,4929,5016,5082,5167,5258,5320,5384,5447,5516,5618,5719,5815,5916,5980,6035,6118,6204,6281,6355"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,71,72,73,74,77,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,195,199,200,202", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3004,3081,3161,3244,3338,4161,4256,4383,7199,7259,7323,7426,7669,13955,14064,14127,14194,14253,14327,14390,14444,14559,14617,14679,14733,14808,14937,15027,15107,15200,15284,15373,15514,15596,15678,15817,15903,15987,16047,16098,16164,16237,16315,16386,16467,16539,16616,16691,16762,16863,16957,17036,17132,17226,17300,17376,17462,17515,17602,17668,17753,17844,17906,17970,18033,18102,18204,18305,18401,18502,18566,18797,19128,19214,19365", "endLines": "5,33,34,35,36,37,45,46,47,71,72,73,74,77,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,195,199,200,202", "endColumns": "12,76,79,82,93,86,94,126,83,59,63,102,69,66,108,62,66,58,73,62,53,114,57,61,53,74,128,89,79,92,83,88,140,81,81,138,85,83,59,50,65,72,77,70,80,71,76,74,70,100,93,78,95,93,73,75,85,52,86,65,84,90,61,63,62,68,101,100,95,100,63,54,82,85,76,73", "endOffsets": "328,3076,3156,3239,3333,3420,4251,4378,4462,7254,7318,7421,7491,7731,14059,14122,14189,14248,14322,14385,14439,14554,14612,14674,14728,14803,14932,15022,15102,15195,15279,15368,15509,15591,15673,15812,15898,15982,16042,16093,16159,16232,16310,16381,16462,16534,16611,16686,16757,16858,16952,17031,17127,17221,17295,17371,17457,17510,17597,17663,17748,17839,17901,17965,18028,18097,18199,18300,18396,18497,18561,18616,18875,19209,19286,19434"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\71f8bc87e80bd35756c6bcfc97f71602\\transformed\\ui-release\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,204,289,393,500,596,679,769,862,945,1026,1109,1183,1259,1334,1407,1490,1558", "endColumns": "98,84,103,106,95,82,89,92,82,80,82,73,75,74,72,82,67,116", "endOffsets": "199,284,388,495,591,674,764,857,940,1021,1104,1178,1254,1329,1402,1485,1553,1670"}, "to": {"startLines": "48,49,68,69,70,75,76,193,194,196,197,201,203,204,205,207,208,209", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4467,4566,6892,6996,7103,7496,7579,18621,18714,18880,18961,19291,19439,19515,19590,19764,19847,19915", "endColumns": "98,84,103,106,95,82,89,92,82,80,82,73,75,74,72,82,67,116", "endOffsets": "4561,4646,6991,7098,7194,7574,7664,18709,18792,18956,19039,19360,19510,19585,19658,19842,19910,20027"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\233292c89c1b85b15f1b8e2d4e33fbe5\\transformed\\play-services-basement-18.3.0\\res\\values-uz\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "145", "endOffsets": "340"}, "to": {"startLines": "58", "startColumns": "4", "startOffsets": "5648", "endColumns": "149", "endOffsets": "5793"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\52ee2787b45be912bfe498d0191617c7\\transformed\\foundation-release\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,166", "endColumns": "110,100", "endOffsets": "161,262"}, "to": {"startLines": "210,211", "startColumns": "4,4", "startOffsets": "20032,20143", "endColumns": "110,100", "endOffsets": "20138,20239"}}]}]}