-- Merging decision tree log ---
provider#androidx.core.content.FileProvider
INJECTED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:105:9-113:20
	android:grantUriPermissions
		ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:109:13-47
	android:authorities
		INJECTED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml
		ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:107:13-60
	android:exported
		ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:108:13-37
	android:name
		ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:106:13-62
manifest
ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:2:1-117:12
INJECTED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:2:1-117:12
INJECTED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:2:1-117:12
INJECTED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:2:1-117:12
MERGED from [androidx.databinding:viewbinding:8.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\533e77f4c642f9e9946e94d7d6be4bf7\transformed\viewbinding-8.10.1\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\fae9f0ca4ac55eb32e79a04faf91d05d\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:17:1-66:12
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c7adff6432088b47c9cce630115db3d4\transformed\material-1.12.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.constraintlayout:constraintlayout:2.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\831bf7afe39e8df50dba31e2b1fd2f85\transformed\constraintlayout-2.2.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a226c1b5d9e52d20876a847f305c795a\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e8013e1c38b0d03c268afa9230065a69\transformed\appcompat-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material3:material3-android:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\695db91b505a701047f9507726e12587\transformed\material3-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.material:material-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\afc5176a2e5e4fb605c3354adbbf078c\transformed\material-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-ripple-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\cfded2619449d224217c0a508987f980\transformed\material-ripple-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\52ee2787b45be912bfe498d0191617c7\transformed\foundation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b229db24e5eedb5dc94f14882d7717f8\transformed\animation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-layout-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e4413c14852caf431fb8ce146ca081c5\transformed\foundation-layout-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-core-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\6bad64ae3e036b96cf8fb7da6a09808b\transformed\animation-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5f79ad552993a0d3067ff5f3c2167f91\transformed\ui-tooling-data-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-unit-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\dea83b57819430148b1099a48ffe1413\transformed\ui-unit-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-geometry-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\515b85b312b4c762661466f241b3340e\transformed\ui-geometry-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-util-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\39cc9172696dfbd0f2b9cfd99c30b22f\transformed\ui-util-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-text-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\acf84386b9d1b7fe9652886d9cafd118\transformed\ui-text-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\543278ced685ded6d84c3a8cdb669382\transformed\ui-tooling-preview-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\1d1684a99660c9b1402a98e603172ba5\transformed\ui-tooling-release\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.compose.ui:ui-graphics-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\1ccdf3738aa9bc8fad37749be4938516\transformed\ui-graphics-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.6] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\d313966f601023f5d516e5fc78ab17e6\transformed\ui-test-manifest-1.7.6\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\80e0e71346892bcb4046881f0cc72aa6\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\1df05e36fa9740a9bf8fb7606a9ee5d1\transformed\play-services-base-18.1.0\AndroidManifest.xml:16:1-24:12
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\7b85ec348d063a5dcae1f1c4418fa227\transformed\firebase-iid-interop-17.1.0\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b19c41b67cd759777cf1edbb6811bebe\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:17:1-25:12
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\09c457764d087d31ea82c5890f3235cf\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:2:1-13:12
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e0af1f0e50ee69fdc31e8afa409078b5\transformed\firebase-installations-18.0.0\AndroidManifest.xml:2:1-24:12
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e6d313ed5945af51e1794fd393cd8096\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b9e26c7402de9e844275032b4ef156b1\transformed\firebase-common-21.0.0\AndroidManifest.xml:15:1-41:12
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\6517582f2f6edcd48d7628592fb76eca\transformed\firebase-installations-interop-17.1.1\AndroidManifest.xml:15:1-19:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\d29935f6ece86a6604f013b57be7920b\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\d4d270792897be155b60c6cd49b63346\transformed\emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c86159d7c215742bee809e1e85168455\transformed\play-services-stats-17.0.2\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2bb0040b844347e937cd3476aa5aad96\transformed\transition-1.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9622936d5fde8c262ebf5a11d500325b\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a5d14353ca44cdc48ea5f9116db26854\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\adf919c83e9210c83dd2e87d6fa100f0\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\24067b132f06350a51e24fd209973968\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8ba17d3c74b410a19e7559ae3148c402\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\857ddad20e08b75d93f063077036c04e\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\d57432e28bce31cd3d884929b7ea98e3\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0131bc6adb003fd694c3673b92dccbf8\transformed\lifecycle-livedata-core-ktx-2.8.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\436988e724ab83eb516edf0541addaf6\transformed\lifecycle-runtime-ktx-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f15b42135e0670911b3fc39723999e68\transformed\lifecycle-runtime-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.7] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\94a4a322fd05f240251541bc9bee7a29\transformed\lifecycle-viewmodel-2.8.7\AndroidManifest.xml:2:1-5:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ee6008d512dc0c0de3149936b730d120\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.7] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\25dcbfa16fef18f12078eb4055a52a50\transformed\lifecycle-livedata-2.8.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e95d80f105be2a06b573fe0043326a74\transformed\lifecycle-viewmodel-ktx-2.8.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\80eddb6ef5ef38982a63ca8a0e892aa5\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\27c3bda8329de60caf7b3c0cf6a28468\transformed\lifecycle-viewmodel-savedstate-2.8.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\dd0a489f68766566a69ed5a1f8197f16\transformed\runtime-saveable-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ffb8e4251e29430ae2d465015f2f51b0\transformed\runtime-release\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e311996a43126c768efcf4fddc604f57\transformed\play-services-tasks-18.1.0\AndroidManifest.xml:2:1-6:12
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\233292c89c1b85b15f1b8e2d4e33fbe5\transformed\play-services-basement-18.3.0\AndroidManifest.xml:16:1-26:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9cdc9dba1e3ff9ef43a13de722f0a2db\transformed\viewpager2-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c4f9b6ce2204681505376234330e0aa6\transformed\fragment-1.5.4\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.7] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9ad76374ddfeaee9324f4acb7cf0362b\transformed\lifecycle-livedata-core-2.8.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\71b908f85124d4a944779a15494e5f0d\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-core-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\13824920ad7e3101b17ec4ffe6d282d2\transformed\material-icons-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\71f8bc87e80bd35756c6bcfc97f71602\transformed\ui-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-ktx:1.9.3] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\41c18e634893365bd19b6fa77dd30b69\transformed\activity-ktx-1.9.3\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-compose:1.9.3] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\62042f22c1f53a7657472c34d627a26d\transformed\activity-compose-1.9.3\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity:1.9.3] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\fca5191f1b82c0cc8f97208032a885cd\transformed\activity-1.9.3\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3f8fd8385c2f6be3b66f9b0da118e5a0\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\6265cc9d2c8da2facb09907e97a4a1a2\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\cc2af64cf0c9784d467476b18ed1ea3e\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\72900a782c7fae377259da36d11b68a2\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.media:media:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\68b2ac0528356dccccb7c3c356bd12c3\transformed\media-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\aa1400bd51024eeda8c343acf1726187\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c8d901032af27ea42c65daa48e65c295\transformed\recyclerview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\aedce3d3a9a680b89c0c498df3fca164\transformed\slidingpanelayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a62c62d1645cb490224ac8ad326b6b70\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5b52272596afb03c074055459879234e\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\716e163df9e30cbd33f29bb300efbdda\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\007f9c18ed41b3f3f8384c64dfe7f496\transformed\autofill-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2168352cc6fad925f8ed37f2d2e3378b\transformed\graphics-path-1.0.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\68cc43b1f116f6befee1484e2c2e084f\transformed\core-1.15.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\79b47bdf9422cd6c4e08bb01da93e689\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.core:core-ktx:1.15.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8d741fafacec5ad8567742c04d3053ac\transformed\core-ktx-1.15.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b94c9432c60705fe3d7d43a041f44041\transformed\firebase-components-18.0.0\AndroidManifest.xml:15:1-20:12
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\7b740101977d373ec406c6bd4b8cb42c\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:15:1-31:12
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5591d3a09fa272b495c23c89c72b26dd\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:15:1-37:12
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\31894156825545756ccf808b138db399\transformed\firebase-encoders-json-18.0.0\AndroidManifest.xml:15:1-23:12
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9ce015f77af2aac248576586aa0b9dca\transformed\transport-runtime-3.1.9\AndroidManifest.xml:15:1-41:12
MERGED from [com.google.android.datatransport:transport-api:3.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\01711a781a01b188897c1a54ca2dbc69\transformed\transport-api-3.1.0\AndroidManifest.xml:15:1-20:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\cad7085020957fc10a9ac44a41d14148\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\6f372b7ef589b9737f878308a146eb2c\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\315e8c9abacd3678f40653877ba85984\transformed\profileinstaller-1.4.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\87ab9f39d1bdbd8b50e4371414d76734\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\354890e2dc633c89a9eecf4e0f7d7b9d\transformed\tracing-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b729fefded952686dd13fef96804f087\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\6ed8a535af03642915ccb547ce9f08db\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2240f982107a30a1eba745723a5da75a\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f89780e4750d68808bd358dda4dc108b\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\606d43b70b310a8ca4761db56d8a7b25\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\402b6c977d442916327ea6bde2fda720\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\eb8da4da1f7f347d22249ac10139658d\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:2:1-7:12
	package
		INJECTED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:5:5-79
MERGED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\fae9f0ca4ac55eb32e79a04faf91d05d\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:22:5-79
MERGED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\fae9f0ca4ac55eb32e79a04faf91d05d\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:22:5-79
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\09c457764d087d31ea82c5890f3235cf\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\09c457764d087d31ea82c5890f3235cf\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e0af1f0e50ee69fdc31e8afa409078b5\transformed\firebase-installations-18.0.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e0af1f0e50ee69fdc31e8afa409078b5\transformed\firebase-installations-18.0.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5591d3a09fa272b495c23c89c72b26dd\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5591d3a09fa272b495c23c89c72b26dd\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9ce015f77af2aac248576586aa0b9dca\transformed\transport-runtime-3.1.9\AndroidManifest.xml:22:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9ce015f77af2aac248576586aa0b9dca\transformed\transport-runtime-3.1.9\AndroidManifest.xml:22:5-79
	android:name
		ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:5:22-76
uses-permission#android.permission.INTERNET
ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:6:5-67
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\09c457764d087d31ea82c5890f3235cf\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\09c457764d087d31ea82c5890f3235cf\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e0af1f0e50ee69fdc31e8afa409078b5\transformed\firebase-installations-18.0.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e0af1f0e50ee69fdc31e8afa409078b5\transformed\firebase-installations-18.0.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5591d3a09fa272b495c23c89c72b26dd\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:25:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5591d3a09fa272b495c23c89c72b26dd\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:25:5-67
	android:name
		ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:6:22-64
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:7:5-108
	android:maxSdkVersion
		ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:7:79-105
	android:name
		ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:7:22-78
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:8:5-80
	android:name
		ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:8:22-77
uses-permission#android.permission.POST_NOTIFICATIONS
ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:9:5-77
MERGED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\fae9f0ca4ac55eb32e79a04faf91d05d\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:23:5-77
MERGED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\fae9f0ca4ac55eb32e79a04faf91d05d\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:23:5-77
	android:name
		ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:9:22-74
uses-permission#android.permission.MANAGE_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:10:5-81
	android:name
		ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:10:22-79
uses-permission#android.permission.RECEIVE_BOOT_COMPLETED
ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:11:5-80
	android:name
		ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:11:22-78
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:12:5-76
	android:name
		ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:12:22-74
uses-permission#android.permission.FOREGROUND_SERVICE_DATA_SYNC
ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:13:5-86
	android:name
		ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:13:22-84
uses-permission#android.permission.REQUEST_INSTALL_PACKAGES
ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:14:5-82
	android:name
		ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:14:22-80
uses-permission#android.permission.REQUEST_DELETE_PACKAGES
ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:15:5-81
	android:name
		ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:15:22-79
application
ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:17:5-115:19
INJECTED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:17:5-115:19
MERGED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\fae9f0ca4ac55eb32e79a04faf91d05d\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:28:5-64:19
MERGED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\fae9f0ca4ac55eb32e79a04faf91d05d\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:28:5-64:19
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c7adff6432088b47c9cce630115db3d4\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c7adff6432088b47c9cce630115db3d4\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\831bf7afe39e8df50dba31e2b1fd2f85\transformed\constraintlayout-2.2.0\AndroidManifest.xml:7:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\831bf7afe39e8df50dba31e2b1fd2f85\transformed\constraintlayout-2.2.0\AndroidManifest.xml:7:5-20
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\1d1684a99660c9b1402a98e603172ba5\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\1d1684a99660c9b1402a98e603172ba5\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.6] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\d313966f601023f5d516e5fc78ab17e6\transformed\ui-test-manifest-1.7.6\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.6] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\d313966f601023f5d516e5fc78ab17e6\transformed\ui-test-manifest-1.7.6\AndroidManifest.xml:22:5-26:19
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\1df05e36fa9740a9bf8fb7606a9ee5d1\transformed\play-services-base-18.1.0\AndroidManifest.xml:19:5-23:19
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\1df05e36fa9740a9bf8fb7606a9ee5d1\transformed\play-services-base-18.1.0\AndroidManifest.xml:19:5-23:19
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\7b85ec348d063a5dcae1f1c4418fa227\transformed\firebase-iid-interop-17.1.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\7b85ec348d063a5dcae1f1c4418fa227\transformed\firebase-iid-interop-17.1.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b19c41b67cd759777cf1edbb6811bebe\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b19c41b67cd759777cf1edbb6811bebe\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e0af1f0e50ee69fdc31e8afa409078b5\transformed\firebase-installations-18.0.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e0af1f0e50ee69fdc31e8afa409078b5\transformed\firebase-installations-18.0.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e6d313ed5945af51e1794fd393cd8096\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e6d313ed5945af51e1794fd393cd8096\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b9e26c7402de9e844275032b4ef156b1\transformed\firebase-common-21.0.0\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b9e26c7402de9e844275032b4ef156b1\transformed\firebase-common-21.0.0\AndroidManifest.xml:22:5-39:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\d4d270792897be155b60c6cd49b63346\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\d4d270792897be155b60c6cd49b63346\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c86159d7c215742bee809e1e85168455\transformed\play-services-stats-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c86159d7c215742bee809e1e85168455\transformed\play-services-stats-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\80eddb6ef5ef38982a63ca8a0e892aa5\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\80eddb6ef5ef38982a63ca8a0e892aa5\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:23:5-33:19
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e311996a43126c768efcf4fddc604f57\transformed\play-services-tasks-18.1.0\AndroidManifest.xml:5:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e311996a43126c768efcf4fddc604f57\transformed\play-services-tasks-18.1.0\AndroidManifest.xml:5:5-20
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\233292c89c1b85b15f1b8e2d4e33fbe5\transformed\play-services-basement-18.3.0\AndroidManifest.xml:20:5-24:19
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\233292c89c1b85b15f1b8e2d4e33fbe5\transformed\play-services-basement-18.3.0\AndroidManifest.xml:20:5-24:19
MERGED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\68cc43b1f116f6befee1484e2c2e084f\transformed\core-1.15.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\68cc43b1f116f6befee1484e2c2e084f\transformed\core-1.15.0\AndroidManifest.xml:28:5-89
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\7b740101977d373ec406c6bd4b8cb42c\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:21:5-29:19
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\7b740101977d373ec406c6bd4b8cb42c\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:21:5-29:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5591d3a09fa272b495c23c89c72b26dd\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:27:5-35:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5591d3a09fa272b495c23c89c72b26dd\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:27:5-35:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9ce015f77af2aac248576586aa0b9dca\transformed\transport-runtime-3.1.9\AndroidManifest.xml:25:5-39:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9ce015f77af2aac248576586aa0b9dca\transformed\transport-runtime-3.1.9\AndroidManifest.xml:25:5-39:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\315e8c9abacd3678f40653877ba85984\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\315e8c9abacd3678f40653877ba85984\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\87ab9f39d1bdbd8b50e4371414d76734\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\87ab9f39d1bdbd8b50e4371414d76734\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\6ed8a535af03642915ccb547ce9f08db\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\6ed8a535af03642915ccb547ce9f08db\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
	android:extractNativeLibs
		INJECTED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\68cc43b1f116f6befee1484e2c2e084f\transformed\core-1.15.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:22:9-35
	android:label
		ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:20:9-41
	android:hardwareAccelerated
		ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:25:9-43
	android:roundIcon
		ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:21:9-54
	tools:targetApi
		ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:26:9-28
	android:icon
		ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:19:9-43
	android:allowBackup
		ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:18:9-35
	android:theme
		ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:23:9-45
	android:networkSecurityConfig
		ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:24:9-69
activity#com.elewashy.egyfilm.activity.SplashActivity
ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:29:9-37:20
	android:exported
		ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:31:13-36
	android:theme
		ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:32:13-72
	android:name
		ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:30:13-52
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:33:13-36:29
action#android.intent.action.MAIN
ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:34:17-69
	android:name
		ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:34:25-66
category#android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:35:17-77
	android:name
		ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:35:27-74
activity#com.elewashy.egyfilm.activity.MainActivity
ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:39:9-43:46
	android:launchMode
		ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:43:13-43
	android:exported
		ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:42:13-36
	android:configChanges
		ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:41:13-122
	android:name
		ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:40:13-50
activity#com.elewashy.egyfilm.activity.DownloadActivity
ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:46:9-55:20
	android:parentActivityName
		ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:50:13-64
	android:label
		ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:49:13-38
	android:exported
		ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:48:13-37
	android:theme
		ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:51:13-49
	android:name
		ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:47:13-54
meta-data#android.app.lib_name
ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:52:13-54:36
	android:value
		ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:54:17-33
	android:name
		ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:53:17-52
activity#com.elewashy.egyfilm.activity.SettingsActivity
ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:58:9-67:20
	android:parentActivityName
		ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:62:13-64
	android:label
		ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:61:13-45
	android:exported
		ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:60:13-37
	android:theme
		ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:63:13-49
	android:name
		ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:59:13-54
activity#com.elewashy.egyfilm.activity.FilterUpdatesActivity
ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:70:9-79:20
	android:parentActivityName
		ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:74:13-68
	android:label
		ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:73:13-51
	android:exported
		ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:72:13-37
	android:theme
		ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:75:13-49
	android:name
		ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:71:13-59
service#com.elewashy.egyfilm.service.DownloadService
ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:82:9-85:56
	android:exported
		ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:84:13-37
	android:foregroundServiceType
		ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:85:13-53
	android:name
		ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:83:13-52
service#com.elewashy.egyfilm.service.MyFirebaseMessagingService
ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:88:9-94:19
	android:exported
		ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:90:13-37
	android:name
		ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:89:13-63
intent-filter#action:name:com.google.firebase.MESSAGING_EVENT
ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:91:13-93:29
action#com.google.firebase.MESSAGING_EVENT
ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:92:17-78
	android:name
		ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:92:25-75
meta-data#com.google.firebase.messaging.default_notification_icon
ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:97:9-99:54
	android:resource
		ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:99:13-51
	android:name
		ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:98:13-83
meta-data#com.google.firebase.messaging.default_notification_channel_id
ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:100:9-102:53
	android:value
		ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:102:13-50
	android:name
		ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:101:13-89
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:110:13-112:58
	android:resource
		ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:112:17-55
	android:name
		ADDED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml:111:17-67
uses-sdk
INJECTED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml
MERGED from [androidx.databinding:viewbinding:8.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\533e77f4c642f9e9946e94d7d6be4bf7\transformed\viewbinding-8.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\533e77f4c642f9e9946e94d7d6be4bf7\transformed\viewbinding-8.10.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\fae9f0ca4ac55eb32e79a04faf91d05d\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\fae9f0ca4ac55eb32e79a04faf91d05d\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c7adff6432088b47c9cce630115db3d4\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c7adff6432088b47c9cce630115db3d4\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\831bf7afe39e8df50dba31e2b1fd2f85\transformed\constraintlayout-2.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\831bf7afe39e8df50dba31e2b1fd2f85\transformed\constraintlayout-2.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a226c1b5d9e52d20876a847f305c795a\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a226c1b5d9e52d20876a847f305c795a\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e8013e1c38b0d03c268afa9230065a69\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e8013e1c38b0d03c268afa9230065a69\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material3:material3-android:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\695db91b505a701047f9507726e12587\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3-android:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\695db91b505a701047f9507726e12587\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material:material-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\afc5176a2e5e4fb605c3354adbbf078c\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\afc5176a2e5e4fb605c3354adbbf078c\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\cfded2619449d224217c0a508987f980\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\cfded2619449d224217c0a508987f980\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\52ee2787b45be912bfe498d0191617c7\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\52ee2787b45be912bfe498d0191617c7\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b229db24e5eedb5dc94f14882d7717f8\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b229db24e5eedb5dc94f14882d7717f8\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e4413c14852caf431fb8ce146ca081c5\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e4413c14852caf431fb8ce146ca081c5\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\6bad64ae3e036b96cf8fb7da6a09808b\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\6bad64ae3e036b96cf8fb7da6a09808b\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5f79ad552993a0d3067ff5f3c2167f91\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5f79ad552993a0d3067ff5f3c2167f91\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\dea83b57819430148b1099a48ffe1413\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\dea83b57819430148b1099a48ffe1413\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\515b85b312b4c762661466f241b3340e\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\515b85b312b4c762661466f241b3340e\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\39cc9172696dfbd0f2b9cfd99c30b22f\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\39cc9172696dfbd0f2b9cfd99c30b22f\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\acf84386b9d1b7fe9652886d9cafd118\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\acf84386b9d1b7fe9652886d9cafd118\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\543278ced685ded6d84c3a8cdb669382\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\543278ced685ded6d84c3a8cdb669382\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\1d1684a99660c9b1402a98e603172ba5\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\1d1684a99660c9b1402a98e603172ba5\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\1ccdf3738aa9bc8fad37749be4938516\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\1ccdf3738aa9bc8fad37749be4938516\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.6] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\d313966f601023f5d516e5fc78ab17e6\transformed\ui-test-manifest-1.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.6] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\d313966f601023f5d516e5fc78ab17e6\transformed\ui-test-manifest-1.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\80e0e71346892bcb4046881f0cc72aa6\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\80e0e71346892bcb4046881f0cc72aa6\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\1df05e36fa9740a9bf8fb7606a9ee5d1\transformed\play-services-base-18.1.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\1df05e36fa9740a9bf8fb7606a9ee5d1\transformed\play-services-base-18.1.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\7b85ec348d063a5dcae1f1c4418fa227\transformed\firebase-iid-interop-17.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\7b85ec348d063a5dcae1f1c4418fa227\transformed\firebase-iid-interop-17.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b19c41b67cd759777cf1edbb6811bebe\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b19c41b67cd759777cf1edbb6811bebe\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\09c457764d087d31ea82c5890f3235cf\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\09c457764d087d31ea82c5890f3235cf\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e0af1f0e50ee69fdc31e8afa409078b5\transformed\firebase-installations-18.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e0af1f0e50ee69fdc31e8afa409078b5\transformed\firebase-installations-18.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e6d313ed5945af51e1794fd393cd8096\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e6d313ed5945af51e1794fd393cd8096\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b9e26c7402de9e844275032b4ef156b1\transformed\firebase-common-21.0.0\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b9e26c7402de9e844275032b4ef156b1\transformed\firebase-common-21.0.0\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\6517582f2f6edcd48d7628592fb76eca\transformed\firebase-installations-interop-17.1.1\AndroidManifest.xml:17:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\6517582f2f6edcd48d7628592fb76eca\transformed\firebase-installations-interop-17.1.1\AndroidManifest.xml:17:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\d29935f6ece86a6604f013b57be7920b\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\d29935f6ece86a6604f013b57be7920b\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\d4d270792897be155b60c6cd49b63346\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\d4d270792897be155b60c6cd49b63346\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c86159d7c215742bee809e1e85168455\transformed\play-services-stats-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c86159d7c215742bee809e1e85168455\transformed\play-services-stats-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2bb0040b844347e937cd3476aa5aad96\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2bb0040b844347e937cd3476aa5aad96\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9622936d5fde8c262ebf5a11d500325b\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9622936d5fde8c262ebf5a11d500325b\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a5d14353ca44cdc48ea5f9116db26854\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a5d14353ca44cdc48ea5f9116db26854\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\adf919c83e9210c83dd2e87d6fa100f0\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\adf919c83e9210c83dd2e87d6fa100f0\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\24067b132f06350a51e24fd209973968\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\24067b132f06350a51e24fd209973968\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8ba17d3c74b410a19e7559ae3148c402\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8ba17d3c74b410a19e7559ae3148c402\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\857ddad20e08b75d93f063077036c04e\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\857ddad20e08b75d93f063077036c04e\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\d57432e28bce31cd3d884929b7ea98e3\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\d57432e28bce31cd3d884929b7ea98e3\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0131bc6adb003fd694c3673b92dccbf8\transformed\lifecycle-livedata-core-ktx-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0131bc6adb003fd694c3673b92dccbf8\transformed\lifecycle-livedata-core-ktx-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\436988e724ab83eb516edf0541addaf6\transformed\lifecycle-runtime-ktx-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\436988e724ab83eb516edf0541addaf6\transformed\lifecycle-runtime-ktx-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f15b42135e0670911b3fc39723999e68\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f15b42135e0670911b3fc39723999e68\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.7] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\94a4a322fd05f240251541bc9bee7a29\transformed\lifecycle-viewmodel-2.8.7\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.7] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\94a4a322fd05f240251541bc9bee7a29\transformed\lifecycle-viewmodel-2.8.7\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ee6008d512dc0c0de3149936b730d120\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ee6008d512dc0c0de3149936b730d120\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.7] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\25dcbfa16fef18f12078eb4055a52a50\transformed\lifecycle-livedata-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.7] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\25dcbfa16fef18f12078eb4055a52a50\transformed\lifecycle-livedata-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e95d80f105be2a06b573fe0043326a74\transformed\lifecycle-viewmodel-ktx-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e95d80f105be2a06b573fe0043326a74\transformed\lifecycle-viewmodel-ktx-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\80eddb6ef5ef38982a63ca8a0e892aa5\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\80eddb6ef5ef38982a63ca8a0e892aa5\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\27c3bda8329de60caf7b3c0cf6a28468\transformed\lifecycle-viewmodel-savedstate-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\27c3bda8329de60caf7b3c0cf6a28468\transformed\lifecycle-viewmodel-savedstate-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\dd0a489f68766566a69ed5a1f8197f16\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\dd0a489f68766566a69ed5a1f8197f16\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ffb8e4251e29430ae2d465015f2f51b0\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ffb8e4251e29430ae2d465015f2f51b0\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e311996a43126c768efcf4fddc604f57\transformed\play-services-tasks-18.1.0\AndroidManifest.xml:4:5-43
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e311996a43126c768efcf4fddc604f57\transformed\play-services-tasks-18.1.0\AndroidManifest.xml:4:5-43
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\233292c89c1b85b15f1b8e2d4e33fbe5\transformed\play-services-basement-18.3.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\233292c89c1b85b15f1b8e2d4e33fbe5\transformed\play-services-basement-18.3.0\AndroidManifest.xml:18:5-43
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9cdc9dba1e3ff9ef43a13de722f0a2db\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9cdc9dba1e3ff9ef43a13de722f0a2db\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c4f9b6ce2204681505376234330e0aa6\transformed\fragment-1.5.4\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c4f9b6ce2204681505376234330e0aa6\transformed\fragment-1.5.4\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.7] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9ad76374ddfeaee9324f4acb7cf0362b\transformed\lifecycle-livedata-core-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.7] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9ad76374ddfeaee9324f4acb7cf0362b\transformed\lifecycle-livedata-core-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\71b908f85124d4a944779a15494e5f0d\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\71b908f85124d4a944779a15494e5f0d\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\13824920ad7e3101b17ec4ffe6d282d2\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\13824920ad7e3101b17ec4ffe6d282d2\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\71f8bc87e80bd35756c6bcfc97f71602\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\71f8bc87e80bd35756c6bcfc97f71602\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.9.3] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\41c18e634893365bd19b6fa77dd30b69\transformed\activity-ktx-1.9.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.9.3] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\41c18e634893365bd19b6fa77dd30b69\transformed\activity-ktx-1.9.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.9.3] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\62042f22c1f53a7657472c34d627a26d\transformed\activity-compose-1.9.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.9.3] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\62042f22c1f53a7657472c34d627a26d\transformed\activity-compose-1.9.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity:1.9.3] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\fca5191f1b82c0cc8f97208032a885cd\transformed\activity-1.9.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.9.3] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\fca5191f1b82c0cc8f97208032a885cd\transformed\activity-1.9.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3f8fd8385c2f6be3b66f9b0da118e5a0\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3f8fd8385c2f6be3b66f9b0da118e5a0\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\6265cc9d2c8da2facb09907e97a4a1a2\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\6265cc9d2c8da2facb09907e97a4a1a2\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\cc2af64cf0c9784d467476b18ed1ea3e\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\cc2af64cf0c9784d467476b18ed1ea3e\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\72900a782c7fae377259da36d11b68a2\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\72900a782c7fae377259da36d11b68a2\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media:media:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\68b2ac0528356dccccb7c3c356bd12c3\transformed\media-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media:media:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\68b2ac0528356dccccb7c3c356bd12c3\transformed\media-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\aa1400bd51024eeda8c343acf1726187\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\aa1400bd51024eeda8c343acf1726187\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c8d901032af27ea42c65daa48e65c295\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c8d901032af27ea42c65daa48e65c295\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\aedce3d3a9a680b89c0c498df3fca164\transformed\slidingpanelayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\aedce3d3a9a680b89c0c498df3fca164\transformed\slidingpanelayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a62c62d1645cb490224ac8ad326b6b70\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a62c62d1645cb490224ac8ad326b6b70\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5b52272596afb03c074055459879234e\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5b52272596afb03c074055459879234e\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\716e163df9e30cbd33f29bb300efbdda\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\716e163df9e30cbd33f29bb300efbdda\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\007f9c18ed41b3f3f8384c64dfe7f496\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\007f9c18ed41b3f3f8384c64dfe7f496\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2168352cc6fad925f8ed37f2d2e3378b\transformed\graphics-path-1.0.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2168352cc6fad925f8ed37f2d2e3378b\transformed\graphics-path-1.0.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\68cc43b1f116f6befee1484e2c2e084f\transformed\core-1.15.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\68cc43b1f116f6befee1484e2c2e084f\transformed\core-1.15.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\79b47bdf9422cd6c4e08bb01da93e689\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\79b47bdf9422cd6c4e08bb01da93e689\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.core:core-ktx:1.15.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8d741fafacec5ad8567742c04d3053ac\transformed\core-ktx-1.15.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.15.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8d741fafacec5ad8567742c04d3053ac\transformed\core-ktx-1.15.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b94c9432c60705fe3d7d43a041f44041\transformed\firebase-components-18.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b94c9432c60705fe3d7d43a041f44041\transformed\firebase-components-18.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\7b740101977d373ec406c6bd4b8cb42c\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\7b740101977d373ec406c6bd4b8cb42c\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5591d3a09fa272b495c23c89c72b26dd\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5591d3a09fa272b495c23c89c72b26dd\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\31894156825545756ccf808b138db399\transformed\firebase-encoders-json-18.0.0\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\31894156825545756ccf808b138db399\transformed\firebase-encoders-json-18.0.0\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9ce015f77af2aac248576586aa0b9dca\transformed\transport-runtime-3.1.9\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9ce015f77af2aac248576586aa0b9dca\transformed\transport-runtime-3.1.9\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-api:3.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\01711a781a01b188897c1a54ca2dbc69\transformed\transport-api-3.1.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-api:3.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\01711a781a01b188897c1a54ca2dbc69\transformed\transport-api-3.1.0\AndroidManifest.xml:18:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\cad7085020957fc10a9ac44a41d14148\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\cad7085020957fc10a9ac44a41d14148\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\6f372b7ef589b9737f878308a146eb2c\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\6f372b7ef589b9737f878308a146eb2c\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\315e8c9abacd3678f40653877ba85984\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\315e8c9abacd3678f40653877ba85984\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\87ab9f39d1bdbd8b50e4371414d76734\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\87ab9f39d1bdbd8b50e4371414d76734\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\354890e2dc633c89a9eecf4e0f7d7b9d\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\354890e2dc633c89a9eecf4e0f7d7b9d\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b729fefded952686dd13fef96804f087\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b729fefded952686dd13fef96804f087\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\6ed8a535af03642915ccb547ce9f08db\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\6ed8a535af03642915ccb547ce9f08db\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2240f982107a30a1eba745723a5da75a\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2240f982107a30a1eba745723a5da75a\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f89780e4750d68808bd358dda4dc108b\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f89780e4750d68808bd358dda4dc108b\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\606d43b70b310a8ca4761db56d8a7b25\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\606d43b70b310a8ca4761db56d8a7b25\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\402b6c977d442916327ea6bde2fda720\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\402b6c977d442916327ea6bde2fda720\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\eb8da4da1f7f347d22249ac10139658d\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\eb8da4da1f7f347d22249ac10139658d\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\AndroidManifest.xml
uses-permission#android.permission.WAKE_LOCK
ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\fae9f0ca4ac55eb32e79a04faf91d05d\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:24:5-68
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\09c457764d087d31ea82c5890f3235cf\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:9:5-68
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\09c457764d087d31ea82c5890f3235cf\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:9:5-68
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\fae9f0ca4ac55eb32e79a04faf91d05d\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:24:22-65
uses-permission#com.google.android.c2dm.permission.RECEIVE
ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\fae9f0ca4ac55eb32e79a04faf91d05d\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:26:5-82
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\09c457764d087d31ea82c5890f3235cf\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:11:5-82
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\09c457764d087d31ea82c5890f3235cf\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:11:5-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\fae9f0ca4ac55eb32e79a04faf91d05d\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:26:22-79
receiver#com.google.firebase.iid.FirebaseInstanceIdReceiver
ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\fae9f0ca4ac55eb32e79a04faf91d05d\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:29:9-40:20
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\fae9f0ca4ac55eb32e79a04faf91d05d\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:31:13-36
	android:permission
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\fae9f0ca4ac55eb32e79a04faf91d05d\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:32:13-73
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\fae9f0ca4ac55eb32e79a04faf91d05d\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:30:13-78
intent-filter#action:name:com.google.android.c2dm.intent.RECEIVE
ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\fae9f0ca4ac55eb32e79a04faf91d05d\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:33:13-35:29
action#com.google.android.c2dm.intent.RECEIVE
ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\fae9f0ca4ac55eb32e79a04faf91d05d\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:34:17-81
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\fae9f0ca4ac55eb32e79a04faf91d05d\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:34:25-78
meta-data#com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED
ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\fae9f0ca4ac55eb32e79a04faf91d05d\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:37:13-39:40
	android:value
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\fae9f0ca4ac55eb32e79a04faf91d05d\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:39:17-37
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\fae9f0ca4ac55eb32e79a04faf91d05d\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:38:17-92
service#com.google.firebase.messaging.FirebaseMessagingService
ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\fae9f0ca4ac55eb32e79a04faf91d05d\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:46:9-53:19
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\fae9f0ca4ac55eb32e79a04faf91d05d\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:49:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\fae9f0ca4ac55eb32e79a04faf91d05d\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:48:13-43
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\fae9f0ca4ac55eb32e79a04faf91d05d\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:47:13-82
service#com.google.firebase.components.ComponentDiscoveryService
ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\fae9f0ca4ac55eb32e79a04faf91d05d\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:54:9-63:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e0af1f0e50ee69fdc31e8afa409078b5\transformed\firebase-installations-18.0.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e0af1f0e50ee69fdc31e8afa409078b5\transformed\firebase-installations-18.0.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e6d313ed5945af51e1794fd393cd8096\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e6d313ed5945af51e1794fd393cd8096\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b9e26c7402de9e844275032b4ef156b1\transformed\firebase-common-21.0.0\AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b9e26c7402de9e844275032b4ef156b1\transformed\firebase-common-21.0.0\AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\7b740101977d373ec406c6bd4b8cb42c\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:22:9-28:19
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\7b740101977d373ec406c6bd4b8cb42c\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:22:9-28:19
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\fae9f0ca4ac55eb32e79a04faf91d05d\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:56:13-37
	tools:targetApi
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b9e26c7402de9e844275032b4ef156b1\transformed\firebase-common-21.0.0\AndroidManifest.xml:34:13-32
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b9e26c7402de9e844275032b4ef156b1\transformed\firebase-common-21.0.0\AndroidManifest.xml:32:13-43
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\fae9f0ca4ac55eb32e79a04faf91d05d\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:55:13-84
meta-data#com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar
ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\fae9f0ca4ac55eb32e79a04faf91d05d\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:57:13-59:85
	android:value
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\fae9f0ca4ac55eb32e79a04faf91d05d\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:59:17-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\fae9f0ca4ac55eb32e79a04faf91d05d\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:58:17-122
meta-data#com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar
ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\fae9f0ca4ac55eb32e79a04faf91d05d\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:60:13-62:85
	android:value
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\fae9f0ca4ac55eb32e79a04faf91d05d\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:62:17-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\fae9f0ca4ac55eb32e79a04faf91d05d\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:61:17-119
activity#androidx.compose.ui.tooling.PreviewActivity
ADDED from [androidx.compose.ui:ui-tooling-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\1d1684a99660c9b1402a98e603172ba5\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-tooling-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\1d1684a99660c9b1402a98e603172ba5\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-tooling-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\1d1684a99660c9b1402a98e603172ba5\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
activity#androidx.activity.ComponentActivity
ADDED from [androidx.compose.ui:ui-test-manifest:1.7.6] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\d313966f601023f5d516e5fc78ab17e6\transformed\ui-test-manifest-1.7.6\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-test-manifest:1.7.6] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\d313966f601023f5d516e5fc78ab17e6\transformed\ui-test-manifest-1.7.6\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-test-manifest:1.7.6] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\d313966f601023f5d516e5fc78ab17e6\transformed\ui-test-manifest-1.7.6\AndroidManifest.xml:24:13-63
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\1df05e36fa9740a9bf8fb7606a9ee5d1\transformed\play-services-base-18.1.0\AndroidManifest.xml:20:9-22:45
	android:exported
		ADDED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\1df05e36fa9740a9bf8fb7606a9ee5d1\transformed\play-services-base-18.1.0\AndroidManifest.xml:22:19-43
	android:theme
		ADDED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\1df05e36fa9740a9bf8fb7606a9ee5d1\transformed\play-services-base-18.1.0\AndroidManifest.xml:21:19-78
	android:name
		ADDED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\1df05e36fa9740a9bf8fb7606a9ee5d1\transformed\play-services-base-18.1.0\AndroidManifest.xml:20:19-85
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar
ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e0af1f0e50ee69fdc31e8afa409078b5\transformed\firebase-installations-18.0.0\AndroidManifest.xml:15:13-17:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e0af1f0e50ee69fdc31e8afa409078b5\transformed\firebase-installations-18.0.0\AndroidManifest.xml:17:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e0af1f0e50ee69fdc31e8afa409078b5\transformed\firebase-installations-18.0.0\AndroidManifest.xml:16:17-130
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar
ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e0af1f0e50ee69fdc31e8afa409078b5\transformed\firebase-installations-18.0.0\AndroidManifest.xml:18:13-20:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e0af1f0e50ee69fdc31e8afa409078b5\transformed\firebase-installations-18.0.0\AndroidManifest.xml:20:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e0af1f0e50ee69fdc31e8afa409078b5\transformed\firebase-installations-18.0.0\AndroidManifest.xml:19:17-127
meta-data#com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar
ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e6d313ed5945af51e1794fd393cd8096\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e6d313ed5945af51e1794fd393cd8096\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e6d313ed5945af51e1794fd393cd8096\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
provider#com.google.firebase.provider.FirebaseInitProvider
ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b9e26c7402de9e844275032b4ef156b1\transformed\firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
	android:authorities
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b9e26c7402de9e844275032b4ef156b1\transformed\firebase-common-21.0.0\AndroidManifest.xml:25:13-72
	android:exported
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b9e26c7402de9e844275032b4ef156b1\transformed\firebase-common-21.0.0\AndroidManifest.xml:27:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b9e26c7402de9e844275032b4ef156b1\transformed\firebase-common-21.0.0\AndroidManifest.xml:26:13-43
	android:initOrder
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b9e26c7402de9e844275032b4ef156b1\transformed\firebase-common-21.0.0\AndroidManifest.xml:28:13-36
	android:name
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b9e26c7402de9e844275032b4ef156b1\transformed\firebase-common-21.0.0\AndroidManifest.xml:24:13-77
meta-data#com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar
ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b9e26c7402de9e844275032b4ef156b1\transformed\firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
	android:value
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b9e26c7402de9e844275032b4ef156b1\transformed\firebase-common-21.0.0\AndroidManifest.xml:37:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b9e26c7402de9e844275032b4ef156b1\transformed\firebase-common-21.0.0\AndroidManifest.xml:36:17-109
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\d4d270792897be155b60c6cd49b63346\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\80eddb6ef5ef38982a63ca8a0e892aa5\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\80eddb6ef5ef38982a63ca8a0e892aa5\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\315e8c9abacd3678f40653877ba85984\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\315e8c9abacd3678f40653877ba85984\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\87ab9f39d1bdbd8b50e4371414d76734\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\87ab9f39d1bdbd8b50e4371414d76734\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\d4d270792897be155b60c6cd49b63346\transformed\emoji2-1.3.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\d4d270792897be155b60c6cd49b63346\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\d4d270792897be155b60c6cd49b63346\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\d4d270792897be155b60c6cd49b63346\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\d4d270792897be155b60c6cd49b63346\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\d4d270792897be155b60c6cd49b63346\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\d4d270792897be155b60c6cd49b63346\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\80eddb6ef5ef38982a63ca8a0e892aa5\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\80eddb6ef5ef38982a63ca8a0e892aa5\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\80eddb6ef5ef38982a63ca8a0e892aa5\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:30:17-78
meta-data#com.google.android.gms.version
ADDED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\233292c89c1b85b15f1b8e2d4e33fbe5\transformed\play-services-basement-18.3.0\AndroidManifest.xml:21:9-23:69
	android:value
		ADDED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\233292c89c1b85b15f1b8e2d4e33fbe5\transformed\play-services-basement-18.3.0\AndroidManifest.xml:23:13-66
	android:name
		ADDED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\233292c89c1b85b15f1b8e2d4e33fbe5\transformed\play-services-basement-18.3.0\AndroidManifest.xml:22:13-58
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\68cc43b1f116f6befee1484e2c2e084f\transformed\core-1.15.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\68cc43b1f116f6befee1484e2c2e084f\transformed\core-1.15.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\68cc43b1f116f6befee1484e2c2e084f\transformed\core-1.15.0\AndroidManifest.xml:23:9-81
permission#com.elewashy.egyfilm.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\68cc43b1f116f6befee1484e2c2e084f\transformed\core-1.15.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\68cc43b1f116f6befee1484e2c2e084f\transformed\core-1.15.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\68cc43b1f116f6befee1484e2c2e084f\transformed\core-1.15.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\68cc43b1f116f6befee1484e2c2e084f\transformed\core-1.15.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\68cc43b1f116f6befee1484e2c2e084f\transformed\core-1.15.0\AndroidManifest.xml:26:22-94
uses-permission#com.elewashy.egyfilm.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\68cc43b1f116f6befee1484e2c2e084f\transformed\core-1.15.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\68cc43b1f116f6befee1484e2c2e084f\transformed\core-1.15.0\AndroidManifest.xml:26:22-94
meta-data#com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar
ADDED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\7b740101977d373ec406c6bd4b8cb42c\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:25:13-27:85
	android:value
		ADDED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\7b740101977d373ec406c6bd4b8cb42c\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:27:17-82
	android:name
		ADDED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\7b740101977d373ec406c6bd4b8cb42c\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:26:17-115
service#com.google.android.datatransport.runtime.backends.TransportBackendDiscovery
ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5591d3a09fa272b495c23c89c72b26dd\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:28:9-34:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9ce015f77af2aac248576586aa0b9dca\transformed\transport-runtime-3.1.9\AndroidManifest.xml:36:9-38:40
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9ce015f77af2aac248576586aa0b9dca\transformed\transport-runtime-3.1.9\AndroidManifest.xml:36:9-38:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5591d3a09fa272b495c23c89c72b26dd\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:30:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5591d3a09fa272b495c23c89c72b26dd\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:29:13-103
meta-data#backend:com.google.android.datatransport.cct.CctBackendFactory
ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5591d3a09fa272b495c23c89c72b26dd\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:31:13-33:39
	android:value
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5591d3a09fa272b495c23c89c72b26dd\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:33:17-36
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5591d3a09fa272b495c23c89c72b26dd\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:32:17-94
service#com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService
ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9ce015f77af2aac248576586aa0b9dca\transformed\transport-runtime-3.1.9\AndroidManifest.xml:26:9-30:19
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9ce015f77af2aac248576586aa0b9dca\transformed\transport-runtime-3.1.9\AndroidManifest.xml:28:13-37
	android:permission
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9ce015f77af2aac248576586aa0b9dca\transformed\transport-runtime-3.1.9\AndroidManifest.xml:29:13-69
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9ce015f77af2aac248576586aa0b9dca\transformed\transport-runtime-3.1.9\AndroidManifest.xml:27:13-117
receiver#com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver
ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9ce015f77af2aac248576586aa0b9dca\transformed\transport-runtime-3.1.9\AndroidManifest.xml:32:9-34:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9ce015f77af2aac248576586aa0b9dca\transformed\transport-runtime-3.1.9\AndroidManifest.xml:34:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9ce015f77af2aac248576586aa0b9dca\transformed\transport-runtime-3.1.9\AndroidManifest.xml:33:13-132
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\315e8c9abacd3678f40653877ba85984\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\315e8c9abacd3678f40653877ba85984\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\315e8c9abacd3678f40653877ba85984\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\315e8c9abacd3678f40653877ba85984\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\315e8c9abacd3678f40653877ba85984\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\315e8c9abacd3678f40653877ba85984\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\315e8c9abacd3678f40653877ba85984\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\315e8c9abacd3678f40653877ba85984\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\315e8c9abacd3678f40653877ba85984\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\315e8c9abacd3678f40653877ba85984\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\315e8c9abacd3678f40653877ba85984\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\315e8c9abacd3678f40653877ba85984\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\315e8c9abacd3678f40653877ba85984\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\315e8c9abacd3678f40653877ba85984\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\315e8c9abacd3678f40653877ba85984\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\315e8c9abacd3678f40653877ba85984\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\315e8c9abacd3678f40653877ba85984\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\315e8c9abacd3678f40653877ba85984\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\315e8c9abacd3678f40653877ba85984\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\315e8c9abacd3678f40653877ba85984\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\315e8c9abacd3678f40653877ba85984\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
