<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:ns1="http://schemas.android.com/tools">
    <integer-array name="myColors">
        <item>@color/egyfilm_red</item>
        <item>@color/egyfilm_red_dark</item>
        <item>@color/egyfilm_black</item>
        <item>@color/egyfilm_dark_gray</item>
        <item>@color/egyfilm_gray</item>
    </integer-array>
    <color name="background">@color/egyfilm_black</color>
    <color name="black">#FF000000</color>
    <color name="colorPrimary">@color/egyfilm_red</color>
    <color name="egyfilm_background">@color/egyfilm_dark_gray</color>
    <color name="egyfilm_black">#000000</color>
    <color name="egyfilm_dark_gray">#141414</color>
    <color name="egyfilm_gray">#808080</color>
    <color name="egyfilm_light_gray">#E5E5E5</color>
    <color name="egyfilm_red">#E50914</color>
    <color name="egyfilm_red_dark">#B81D24</color>
    <color name="gray_text">@color/egyfilm_gray</color>
    <color name="ic_launcher_background">#FFFFFF</color>
    <color name="primary">@color/egyfilm_red</color>
    <color name="primary_dark">@color/egyfilm_red_dark</color>
    <color name="surface">@color/egyfilm_dark_gray</color>
    <color name="text_primary">@color/white</color>
    <color name="text_secondary">@color/egyfilm_gray</color>
    <color name="white">#FFFFFFFF</color>
    <string name="adblocker_filter">AdBlocker Filter</string>
    <string name="app_name">EgyFilm</string>
    <string name="back">Back</string>
    <string name="cancel_button_desc">Cancel Download</string>
    <string name="delete_button_desc">Delete File</string>
    <string name="download_icon_desc">File type icon</string>
    <string name="downloads">Downloads</string>
    <string name="enter_url">Welcome to EgyFilm</string>
    <string name="filter_updates">Filter Updates</string>
    <string name="filter_updates_desc">Manage and update app filters</string>
    <string name="forward">Forward</string>
    <string name="fullscreen">FullScreen</string>
    <string name="gcm_defaultSenderId" translatable="false">617958848640</string>
    <string name="google_api_key" translatable="false">AIzaSyCoJkU_0yI-CtYeVU6U40fEOzn_5MN3SwI</string>
    <string name="google_app_id" translatable="false">1:617958848640:android:6beb8157cec53bbbdf4e65</string>
    <string name="google_crash_reporting_api_key" translatable="false">AIzaSyCoJkU_0yI-CtYeVU6U40fEOzn_5MN3SwI</string>
    <string name="google_storage_bucket" translatable="false">egyfilm-app.firebasestorage.app</string>
    <string name="homePage">HomePage</string>
    <string name="home_btn">Home Button</string>
    <string name="last_updated">Last Updated: %s</string>
    <string name="more_options">More Options</string>
    <string name="never_updated">Never Updated</string>
    <string name="open_file_button_desc">Open File</string>
    <string name="open_filter">Open Links Filter</string>
    <string name="pause_resume_button_desc">Pause or Resume Download</string>
    <string name="project_id" translatable="false">egyfilm-app</string>
    <string name="refresh_btn">Refresh</string>
    <string name="retry_button_desc">Retry Download</string>
    <string name="search_hint">Search</string>
    <string name="settings">Settings</string>
    <string name="settings_btn">Settings Button</string>
    <string name="update">Update</string>
    <string name="update_all_filters">Update All Filters</string>
    <string name="update_failed">Update Failed</string>
    <string name="update_success">Update Successful</string>
    <string name="updating">Updating...</string>
    <string name="valid_filter">Valid Links Filter</string>
    <string name="view_all">View All</string>
    <style name="Theme.egyfilm" parent="Theme.MaterialComponents.NoActionBar">
        
        <item name="colorPrimary">@color/egyfilm_red</item>
        <item name="colorPrimaryVariant">@color/egyfilm_red_dark</item>
        <item name="colorOnPrimary">@color/white</item>
        
        <item name="colorSecondary">@color/egyfilm_red</item>
        <item name="colorSecondaryVariant">@color/egyfilm_red_dark</item>
        <item name="colorOnSecondary">@color/white</item>
        
        <item name="android:windowBackground">@color/egyfilm_black</item>
        <item name="backgroundColor">@color/egyfilm_black</item>
        <item name="colorSurface">@color/egyfilm_dark_gray</item>
        <item name="colorOnSurface">@color/white</item>
        
        <item name="android:textColorPrimary">@color/white</item>
        <item name="android:textColorSecondary">@color/egyfilm_gray</item>
        
        <item name="android:statusBarColor">@color/egyfilm_black</item>
        
        <item name="android:navigationBarColor">@color/egyfilm_black</item>
        
        <item name="android:forceDarkAllowed" ns1:targetApi="q">false</item>
    </style>
    <style name="Theme.egyfilm.NoActionBar.Fullscreen" parent="Theme.egyfilm">
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowLayoutInDisplayCutoutMode" ns1:targetApi="o_mr1">shortEdges</item>
        <item name="android:windowTranslucentStatus">true</item>
        <item name="android:windowTranslucentNavigation">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:fitsSystemWindows">false</item>
    </style>
    <style name="egyfilmProgressStyle" parent="Widget.MaterialComponents.CircularProgressIndicator">
        <item name="indicatorSize">48dp</item>
        <item name="trackThickness">3dp</item>
        <item name="trackCornerRadius">2dp</item>
        <item name="indicatorColor">@color/egyfilm_red</item>
        <item name="trackColor">@android:color/transparent</item>
        <item name="indicatorDirectionCircular">clockwise</item>
        <item name="android:indeterminateOnly">true</item>
        <item name="android:indeterminateDuration">2500</item>
        <item name="circularProgressIndicatorStyle">@style/Widget.MaterialComponents.CircularProgressIndicator</item>
        
        <item name="indicatorInset">0dp</item>
        <item name="minHideDelay">0</item>
    </style>
    <style name="roundCornerDialog" parent="ThemeOverlay.MaterialComponents.Dialog.Alert">
        <item name="shapeAppearanceOverlay">@style/shape</item>
        <item name="android:background">@color/egyfilm_dark_gray</item>
        <item name="colorSurface">@color/egyfilm_dark_gray</item>
        <item name="android:textColorPrimary">@color/white</item>
        <item name="android:textColorSecondary">@color/egyfilm_gray</item>
    </style>
    <style name="shape">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">8dp</item>
    </style>
</resources>