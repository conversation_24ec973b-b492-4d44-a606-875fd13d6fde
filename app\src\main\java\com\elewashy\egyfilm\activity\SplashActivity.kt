package com.elewashy.egyfilm.activity

import android.content.Context
import android.content.Intent
import android.net.ConnectivityManager
import android.net.NetworkCapabilities
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.provider.Settings
import android.widget.Toast
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.core.view.WindowCompat
import com.elewashy.egyfilm.BuildConfig
import com.elewashy.egyfilm.R
import com.elewashy.egyfilm.activity.DownloadActivity
import com.elewashy.egyfilm.fragment.AdBlocker
import com.elewashy.egyfilm.fragment.OpenLinkValidator
import com.elewashy.egyfilm.fragment.ValidLinkChecker
import com.elewashy.egyfilm.model.UpdateResponse
import com.elewashy.egyfilm.service.DownloadService
import com.elewashy.egyfilm.ui.screens.SplashScreen
import com.elewashy.egyfilm.ui.theme.EgyFilmTheme
import com.google.gson.Gson
import java.io.IOException
import java.net.HttpURLConnection
import java.net.URL
import kotlin.concurrent.thread

class SplashActivity : ComponentActivity() {

    // UI State variables
    private var showRetryButton by mutableStateOf(false)
    private var showUpdateDialog by mutableStateOf(false)
    private var updateTitle by mutableStateOf("")
    private var updateMessage by mutableStateOf("")
    private var showDownloadProgress by mutableStateOf(false)
    private var downloadProgress by mutableFloatStateOf(0f)
    private var downloadStatus by mutableStateOf("")
    private val handler = Handler(Looper.getMainLooper())
    private var updateResponse: UpdateResponse? = null
    
    // Modern activity result launcher
    private lateinit var installPermissionLauncher: ActivityResultLauncher<Intent>
    
    companion object {
        private const val UPDATE_CHECK_URL = "https://ad-hosts.vercel.app/update.json"
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // Enable edge-to-edge display
        WindowCompat.setDecorFitsSystemWindows(window, false)

        // Initialize modern activity result launcher
        setupActivityResultLaunchers()

        // Set Compose content
        setContent {
            EgyFilmTheme {
                SplashScreen(
                    onNavigateToMain = { proceedToMainActivity() },
                    onRetryClicked = { onRetryClicked() },
                    showRetryButton = showRetryButton,
                    showUpdateDialog = showUpdateDialog,
                    updateTitle = updateTitle,
                    updateMessage = updateMessage,
                    onUpdateClicked = { onUpdateClicked() },
                    onUpdateDismissed = { showUpdateDialog = false },
                    showDownloadProgress = showDownloadProgress,
                    downloadProgress = downloadProgress,
                    downloadStatus = downloadStatus
                )
            }
        }

        // Initialize background services
        initializeBackgroundServices()

        // Start update check
        checkForUpdates()
    }

    private fun initializeBackgroundServices() {
        // Initialize AdBlocker in background to avoid blocking startup
        thread {
            val adBlocker = AdBlocker.getInstance(this@SplashActivity)
            adBlocker.updateEasyList()
        }

        // Initialize ValidLinkChecker and start update in background
        val validLinkChecker = ValidLinkChecker.getInstance(this)
        thread {
            validLinkChecker.updateValidLinks()
        }

        // Initialize OpenLinkValidator and start update in background
        val openLinkValidator = OpenLinkValidator.getInstance(this)
        thread {
            openLinkValidator.updateOpenLinks()
        }
    }
    
    private fun setupActivityResultLaunchers() {
        // Modern replacement for startActivityForResult
        installPermissionLauncher = registerForActivityResult(
            ActivityResultContracts.StartActivityForResult()
        ) { result ->
            if (canInstallApks()) {
                updateResponse?.let { response ->
                    startDownload(response.apk_url)
                }
            } else {
                Toast.makeText(this, "You must allow installation of apps from unknown sources", Toast.LENGTH_LONG).show()
            }
        }
    }

    private fun onRetryClicked() {
        showRetryButton = false
        checkInternetAndProceed()
    }

    private fun onUpdateClicked() {
        updateResponse?.let { response ->
            if (canInstallApps()) {
                startDownload(response.apk_url)
            } else {
                requestInstallPermission()
            }
        }
    }



    private fun checkInternetAndProceed() {
        handler.postDelayed({
            if (isInternetAvailable()) {
                proceedToMainActivity()
            } else {
                showNoInternetUI()
            }
        }, 2500) // Adjusted for new animation duration
    }
    
    private fun checkForUpdates() {
        if (!isInternetAvailable()) {
            checkInternetAndProceed()
            return
        }
        
        thread {
            try {
                val url = URL(UPDATE_CHECK_URL)
                val connection = url.openConnection() as HttpURLConnection
                connection.requestMethod = "GET"
                connection.connectTimeout = 10000
                connection.readTimeout = 10000
                
                val responseCode = connection.responseCode
                if (responseCode == HttpURLConnection.HTTP_OK) {
                    val response = connection.inputStream.bufferedReader().use { it.readText() }
                    val gson = Gson()
                    val updateInfo = gson.fromJson<UpdateResponse>(response, UpdateResponse::class.java)
                    
                    runOnUiThread {
                        if (isUpdateAvailable(updateInfo.latest_version)) {
                            updateResponse = updateInfo
                            showUpdateDialog(updateInfo)
                        } else {
                            checkInternetAndProceed()
                        }
                    }
                } else {
                    runOnUiThread {
                        checkInternetAndProceed()
                    }
                }
                connection.disconnect()
            } catch (e: IOException) {
                runOnUiThread {
                    checkInternetAndProceed()
                }
            }
        }
    }
    
    private fun isUpdateAvailable(latestVersion: String): Boolean {
        val currentVersion = BuildConfig.VERSION_NAME
        return compareVersions(latestVersion, currentVersion) > 0
    }
    
    private fun compareVersions(version1: String, version2: String): Int {
        val parts1 = version1.split(".").map { it.toIntOrNull() ?: 0 }
        val parts2 = version2.split(".").map { it.toIntOrNull() ?: 0 }
        
        val maxLength = maxOf(parts1.size, parts2.size)
        
        for (i in 0 until maxLength) {
            val v1 = if (i < parts1.size) parts1[i] else 0
            val v2 = if (i < parts2.size) parts2[i] else 0
            
            when {
                v1 > v2 -> return 1
                v1 < v2 -> return -1
            }
        }
        return 0
    }

    private fun showNoInternetUI() {
        runOnUiThread {
            showRetryButton = true
        }
    }

    private fun proceedToMainActivity() {
        val intent = Intent(this, MainActivity::class.java)
        startActivity(intent)
        // Modern transition using overrideActivityTransition (API 34+) or overridePendingTransition for older versions
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
            overrideActivityTransition(OVERRIDE_TRANSITION_OPEN, R.anim.activity_slide_in_up, R.anim.activity_fade_out_down)
        } else {
            @Suppress("DEPRECATION")
            overridePendingTransition(R.anim.activity_slide_in_up, R.anim.activity_fade_out_down)
        }
        finish()
    }

    private fun isInternetAvailable(): Boolean {
        val connectivityManager = getSystemService(CONNECTIVITY_SERVICE) as ConnectivityManager

        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            val network = connectivityManager.activeNetwork
            val capabilities = connectivityManager.getNetworkCapabilities(network)
            capabilities?.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET) == true
        } else {
            @Suppress("DEPRECATION")
            val networkInfo = connectivityManager.activeNetworkInfo
            @Suppress("DEPRECATION")
            networkInfo != null && networkInfo.isConnected
        }
    }
    
    private fun showUpdateDialog(updateInfo: UpdateResponse) {
        updateTitle = "Update Available"
        updateMessage = updateInfo.update_message
        showUpdateDialog = true
    }

    private fun hideUpdateDialog() {
        showUpdateDialog = false
    }
    
    private fun canInstallApps(): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            packageManager.canRequestPackageInstalls()
        } else {
            true
        }
    }
    
    private fun requestInstallPermission() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val intent = Intent(Settings.ACTION_MANAGE_UNKNOWN_APP_SOURCES)
                .setData(Uri.parse("package:$packageName"))
            installPermissionLauncher.launch(intent)
        }
    }
    
    @Deprecated("Use registerForActivityResult instead")
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        // This method is deprecated and replaced with modern activity result launchers
        // All functionality has been moved to setupActivityResultLaunchers()
    }
    
    private fun canInstallApks(): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            packageManager.canRequestPackageInstalls()
        } else {
            true
        }
    }
    
    private fun startDownload(url: String) {
        showDownloadProgress = true
        downloadStatus = "Starting download..."

        // Create and start download through DownloadService
        val intent = DownloadService.createStartIntent(
            context = this,
            url = url,
            fileName = "egyfilm_update.apk",
            mimeType = "application/vnd.android.package-archive",
            userAgent = null,
            referer = null,
            origin = null,
            cookies = null,
            source = "UPDATE"
        )
        startService(intent)

        // Launch DownloadActivity
        val downloadActivityIntent = Intent(this, DownloadActivity::class.java)
        startActivity(downloadActivityIntent)

        // Hide update dialog since download is starting
        hideUpdateDialog()
    }
}
