// Generated by view binder compiler. Do not edit!
package com.elewashy.egyfilm.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.elewashy.egyfilm.R;
import com.google.android.material.button.MaterialButton;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class MoreFeaturesBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final MaterialButton backBtn;

  @NonNull
  public final MaterialButton downloadsBtn;

  @NonNull
  public final MaterialButton forwardBtn;

  @NonNull
  public final MaterialButton fullscreenBtn;

  private MoreFeaturesBinding(@NonNull LinearLayout rootView, @NonNull MaterialButton backBtn,
      @NonNull MaterialButton downloadsBtn, @NonNull MaterialButton forwardBtn,
      @NonNull MaterialButton fullscreenBtn) {
    this.rootView = rootView;
    this.backBtn = backBtn;
    this.downloadsBtn = downloadsBtn;
    this.forwardBtn = forwardBtn;
    this.fullscreenBtn = fullscreenBtn;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static MoreFeaturesBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static MoreFeaturesBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.more_features, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static MoreFeaturesBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.backBtn;
      MaterialButton backBtn = ViewBindings.findChildViewById(rootView, id);
      if (backBtn == null) {
        break missingId;
      }

      id = R.id.downloadsBtn;
      MaterialButton downloadsBtn = ViewBindings.findChildViewById(rootView, id);
      if (downloadsBtn == null) {
        break missingId;
      }

      id = R.id.forwardBtn;
      MaterialButton forwardBtn = ViewBindings.findChildViewById(rootView, id);
      if (forwardBtn == null) {
        break missingId;
      }

      id = R.id.fullscreenBtn;
      MaterialButton fullscreenBtn = ViewBindings.findChildViewById(rootView, id);
      if (fullscreenBtn == null) {
        break missingId;
      }

      return new MoreFeaturesBinding((LinearLayout) rootView, backBtn, downloadsBtn, forwardBtn,
          fullscreenBtn);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
