package com.elewashy.egyfilm.ui.theme

import androidx.compose.ui.graphics.Color

// EgyFilm Brand Colors
val EgyFilmRed = Color(0xFFE50914)
val EgyFilmRedDark = Color(0xFFB81D24)
val EgyFilmBlack = Color(0xFF000000)
val EgyFilmDarkGray = Color(0xFF141414)
val EgyFilmGray = Color(0xFF808080)
val EgyFilmLightGray = Color(0xFFE5E5E5)

// Basic Colors
val White = Color(0xFFFFFFFF)
val Black = Color(0xFF000000)

// Additional UI Colors
val EgyFilmBackground = Color(0xFF0F0F0F)
val EgyFilmSurface = Color(0xFF1A1A1A)
val EgyFilmSurfaceVariant = Color(0xFF2A2A2A)
val EgyFilmOnSurface = Color(0xFFE1E1E1)
val EgyFilmOnSurfaceVariant = Color(0xFFB3B3B3)

// Status Colors
val EgyFilmSuccess = Color(0xFF4CAF50)
val EgyFilmWarning = Color(0xFFFF9800)
val EgyFilmError = Color(0xFFF44336)

// Transparent Colors
val EgyFilmRedTransparent = Color(0x22E50914)
val EgyFilmBlackTransparent = Color(0x80000000)
val EgyFilmWhiteTransparent = Color(0x80FFFFFF)
