plugins {
    id("com.android.application")
    id("org.jetbrains.kotlin.android")
    id("org.jetbrains.kotlin.plugin.compose") version "2.1.0"
    id("com.google.gms.google-services")
}

android {
    namespace = "com.elewashy.egyfilm"
    compileSdk = 35

    defaultConfig {
        applicationId = "com.elewashy.egyfilm"
        minSdk = 21
        targetSdk = 35
        versionCode = 1
        versionName = "1.0.8"
        
        // Gradle 9.0 compatibility - use modern configuration avoidance
        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"
    }

    buildTypes {
        release {
            isMinifyEnabled = false
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "proguard-rules.pro"
            )
        }
    }
    
    // Modern Java toolchain configuration for Gradle 9.0+
    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
        // Enable core library desugaring for better compatibility
        isCoreLibraryDesugaringEnabled = true
    }
    
    kotlinOptions {
        jvmTarget = "17"
        // Modern Kotlin compiler options
        freeCompilerArgs += listOf(
            "-opt-in=kotlin.RequiresOptIn"
        )
    }

    buildFeatures {
        // For viewBinding (keeping for gradual migration)
        viewBinding = true
        // Enable Jetpack Compose
        compose = true
        // Enable BuildConfig generation
        buildConfig = true
    }
    
    // Modern packaging options
    packaging {
        resources {
            excludes += "/META-INF/{AL2.0,LGPL2.1}"
        }
    }
}

// Java toolchain for Gradle 9.0 compatibility
java {
    toolchain {
        languageVersion.set(JavaLanguageVersion.of(17))
    }
}

dependencies {
    // Core library desugaring for Gradle 9.0 compatibility
    coreLibraryDesugaring("com.android.tools:desugar_jdk_libs:2.0.4")
    
    implementation(platform("com.google.firebase:firebase-bom:33.14.0"))
    implementation("com.google.firebase:firebase-messaging:24.1.1")
    implementation(libs.androidx.core.ktx)
    implementation(libs.androidx.appcompat)
    implementation(libs.material)
    implementation(libs.androidx.activity)
    implementation(libs.androidx.constraintlayout)

    // Jetpack Compose BOM
    implementation(platform(libs.compose.bom))
    implementation(libs.compose.ui)
    implementation(libs.compose.ui.graphics)
    implementation(libs.compose.ui.tooling.preview)
    implementation(libs.compose.material3)
    implementation(libs.compose.activity)
    implementation(libs.compose.lifecycle.viewmodel)

    // Compose debugging tools (only in debug builds)
    debugImplementation(libs.compose.ui.tooling)
    debugImplementation(libs.compose.ui.test.manifest)

    // Pull to Refresh
    implementation(libs.legacy.support)

    // For storing objects in shared preferences
    implementation(libs.gson)

    // Coroutines
    implementation(libs.kotlinx.coroutines)
}
