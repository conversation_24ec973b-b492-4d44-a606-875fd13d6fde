{"logs": [{"outputFile": "com.elewashy.egyfilm.app-mergeDebugResources-66:/values-night-v8/values-night-v8.xml", "map": [{"source": "C:\\Users\\<USER>\\Desktop\\EgyFilm-App\\app\\src\\main\\res\\values-night\\themes.xml", "from": {"startLines": "3,27", "startColumns": "4,4", "startOffsets": "139,1412", "endLines": "24,34", "endColumns": "12,12", "endOffsets": "1373,1917"}, "to": {"startLines": "35,57", "startColumns": "4,4", "startOffsets": "3620,4698", "endLines": "56,64", "endColumns": "12,12", "endOffsets": "4693,5201"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\e8013e1c38b0d03c268afa9230065a69\\transformed\\appcompat-1.7.0\\res\\values-night-v8\\values-night-v8.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,125,209,293,389,491,593,687", "endColumns": "69,83,83,95,101,101,93,88", "endOffsets": "120,204,288,384,486,588,682,771"}, "to": {"startLines": "2,3,4,5,6,7,8,65", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "100,170,254,338,434,536,638,5206", "endColumns": "69,83,83,95,101,101,93,88", "endOffsets": "165,249,333,429,531,633,727,5290"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\c7adff6432088b47c9cce630115db3d4\\transformed\\material-1.12.0\\res\\values-night-v8\\values-night-v8.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,241,330,431,538,645,744,851,954,1081,1169,1293,1395,1497,1613,1715,1829,1957,2073,2195,2331,2451,2585,2705,2817,2943,3060,3184,3314,3436,3574,3708,3824", "endColumns": "74,110,88,100,106,106,98,106,102,126,87,123,101,101,115,101,113,127,115,121,135,119,133,119,111,125,116,123,129,121,137,133,115,119", "endOffsets": "125,236,325,426,533,640,739,846,949,1076,1164,1288,1390,1492,1608,1710,1824,1952,2068,2190,2326,2446,2580,2700,2812,2938,3055,3179,3309,3431,3569,3703,3819,3939"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "732,807,918,1007,1108,1215,1322,1421,1528,1631,1758,1846,1970,2072,2174,2290,2392,2506,2634,2750,2872,3008,3128,3262,3382,3494,5295,5412,5536,5666,5788,5926,6060,6176", "endColumns": "74,110,88,100,106,106,98,106,102,126,87,123,101,101,115,101,113,127,115,121,135,119,133,119,111,125,116,123,129,121,137,133,115,119", "endOffsets": "802,913,1002,1103,1210,1317,1416,1523,1626,1753,1841,1965,2067,2169,2285,2387,2501,2629,2745,2867,3003,3123,3257,3377,3489,3615,5407,5531,5661,5783,5921,6055,6171,6291"}}]}]}