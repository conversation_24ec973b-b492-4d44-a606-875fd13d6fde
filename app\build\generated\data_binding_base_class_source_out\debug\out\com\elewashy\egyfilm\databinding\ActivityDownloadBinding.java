// Generated by view binder compiler. Do not edit!
package com.elewashy.egyfilm.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.Toolbar;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.elewashy.egyfilm.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityDownloadBinding implements ViewBinding {
  @NonNull
  private final CoordinatorLayout rootView;

  @NonNull
  public final LinearLayout layoutNoDownloads;

  @NonNull
  public final RecyclerView rvDownloads;

  @NonNull
  public final Toolbar toolbarDownloads;

  @NonNull
  public final TextView tvNoDownloads;

  private ActivityDownloadBinding(@NonNull CoordinatorLayout rootView,
      @NonNull LinearLayout layoutNoDownloads, @NonNull RecyclerView rvDownloads,
      @NonNull Toolbar toolbarDownloads, @NonNull TextView tvNoDownloads) {
    this.rootView = rootView;
    this.layoutNoDownloads = layoutNoDownloads;
    this.rvDownloads = rvDownloads;
    this.toolbarDownloads = toolbarDownloads;
    this.tvNoDownloads = tvNoDownloads;
  }

  @Override
  @NonNull
  public CoordinatorLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityDownloadBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityDownloadBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_download, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityDownloadBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.layout_no_downloads;
      LinearLayout layoutNoDownloads = ViewBindings.findChildViewById(rootView, id);
      if (layoutNoDownloads == null) {
        break missingId;
      }

      id = R.id.rv_downloads;
      RecyclerView rvDownloads = ViewBindings.findChildViewById(rootView, id);
      if (rvDownloads == null) {
        break missingId;
      }

      id = R.id.toolbar_downloads;
      Toolbar toolbarDownloads = ViewBindings.findChildViewById(rootView, id);
      if (toolbarDownloads == null) {
        break missingId;
      }

      id = R.id.tv_no_downloads;
      TextView tvNoDownloads = ViewBindings.findChildViewById(rootView, id);
      if (tvNoDownloads == null) {
        break missingId;
      }

      return new ActivityDownloadBinding((CoordinatorLayout) rootView, layoutNoDownloads,
          rvDownloads, toolbarDownloads, tvNoDownloads);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
