<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/egyfilm_dark_gray"
    android:fitsSystemWindows="true">

    <!-- Top App Bar -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:background="@color/colorPrimary"
        android:elevation="4dp"
        android:paddingStart="8dp"
        android:paddingEnd="16dp">

        <ImageButton
            android:id="@+id/backButton"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:src="@drawable/ic_back"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:contentDescription="@string/back"
            app:tint="@android:color/white" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/settings"
            android:textColor="@android:color/white"
            android:textSize="20sp"
            android:textStyle="bold"
            android:layout_marginStart="8dp" />

    </LinearLayout>

    <!-- Main Content -->
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:padding="16dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <!-- Filter Updates Section -->
            <LinearLayout
                android:id="@+id/filterUpdatesCard"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:orientation="horizontal"
                android:padding="16dp"
                android:gravity="center_vertical"
                android:background="?attr/selectableItemBackground"
                android:clickable="true"
                android:focusable="true">

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@drawable/ic_refresh"
                    app:tint="@color/colorPrimary"
                    android:layout_marginEnd="16dp" />

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/filter_updates"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:textColor="@color/white" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/filter_updates_desc"
                        android:textSize="14sp"
                        android:textColor="@color/egyfilm_gray"
                        android:layout_marginTop="4dp" />

                </LinearLayout>

                <ImageView
                    android:layout_width="16dp"
                    android:layout_height="16dp"
                    android:src="@drawable/ic_forward"
                    app:tint="@color/egyfilm_gray" />

            </LinearLayout>

            <!-- Future sections can be added here -->

        </LinearLayout>

    </ScrollView>

</LinearLayout>
