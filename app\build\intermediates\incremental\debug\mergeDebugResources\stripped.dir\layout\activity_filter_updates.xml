<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/egyfilm_dark_gray"
    android:fitsSystemWindows="true">

    <!-- Top App Bar -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:background="@color/colorPrimary"
        android:elevation="4dp"
        android:paddingStart="8dp"
        android:paddingEnd="16dp">

        <ImageButton
            android:id="@+id/backButton"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:src="@drawable/ic_back"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:contentDescription="@string/back"
            app:tint="@android:color/white" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/filter_updates"
            android:textColor="@android:color/white"
            android:textSize="20sp"
            android:textStyle="bold"
            android:layout_marginStart="8dp" />

    </LinearLayout>

    <!-- Main Content -->
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:padding="16dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <!-- Section Title -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/filter_updates"
                android:textSize="18sp"
                android:textStyle="bold"
                android:textColor="@color/white"
                android:layout_marginBottom="24dp"
                android:drawableStart="@drawable/ic_settings"
                android:drawablePadding="8dp"
                android:gravity="center_vertical"
                app:drawableTint="@color/colorPrimary" />

            <!-- AdBlocker Filter -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp"
                android:layout_marginBottom="16dp"
                android:background="@drawable/search_bar_background">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical">

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/adblocker_filter"
                            android:textSize="16sp"
                            android:textStyle="bold"
                            android:textColor="@color/white" />

                        <TextView
                            android:id="@+id/adBlockerLastUpdate"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/never_updated"
                            android:textSize="12sp"
                            android:textColor="@color/egyfilm_gray"
                            android:layout_marginTop="2dp" />

                        <TextView
                            android:id="@+id/adBlockerStatus"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textSize="12sp"
                            android:textColor="@color/colorPrimary"
                            android:layout_marginTop="2dp"
                            android:visibility="gone" />

                    </LinearLayout>

                    <ProgressBar
                        android:id="@+id/adBlockerProgress"
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:layout_marginEnd="8dp"
                        android:visibility="gone" />

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/updateAdBlockerBtn"
                        android:layout_width="wrap_content"
                        android:layout_height="36dp"
                        android:text="@string/update"
                        android:textSize="12sp"
                        app:cornerRadius="18dp"
                        style="@style/Widget.MaterialComponents.Button" />

                </LinearLayout>

            </LinearLayout>

            <!-- Valid Links Filter -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp"
                android:layout_marginBottom="16dp"
                android:background="@drawable/search_bar_background">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical">

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/valid_filter"
                            android:textSize="16sp"
                            android:textStyle="bold"
                            android:textColor="@color/white" />

                        <TextView
                            android:id="@+id/validLinksLastUpdate"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/never_updated"
                            android:textSize="12sp"
                            android:textColor="@color/egyfilm_gray"
                            android:layout_marginTop="2dp" />

                        <TextView
                            android:id="@+id/validLinksStatus"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textSize="12sp"
                            android:textColor="@color/colorPrimary"
                            android:layout_marginTop="2dp"
                            android:visibility="gone" />

                    </LinearLayout>

                    <ProgressBar
                        android:id="@+id/validLinksProgress"
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:layout_marginEnd="8dp"
                        android:visibility="gone" />

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/updateValidLinksBtn"
                        android:layout_width="wrap_content"
                        android:layout_height="36dp"
                        android:text="@string/update"
                        android:textSize="12sp"
                        app:cornerRadius="18dp"
                        style="@style/Widget.MaterialComponents.Button" />

                </LinearLayout>

            </LinearLayout>

            <!-- Open Links Filter -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp"
                android:layout_marginBottom="24dp"
                android:background="@drawable/search_bar_background">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical">

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/open_filter"
                            android:textSize="16sp"
                            android:textStyle="bold"
                            android:textColor="@color/white" />

                        <TextView
                            android:id="@+id/openLinksLastUpdate"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/never_updated"
                            android:textSize="12sp"
                            android:textColor="@color/egyfilm_gray"
                            android:layout_marginTop="2dp" />

                        <TextView
                            android:id="@+id/openLinksStatus"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textSize="12sp"
                            android:textColor="@color/colorPrimary"
                            android:layout_marginTop="2dp"
                            android:visibility="gone" />

                    </LinearLayout>

                    <ProgressBar
                        android:id="@+id/openLinksProgress"
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:layout_marginEnd="8dp"
                        android:visibility="gone" />

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/updateOpenLinksBtn"
                        android:layout_width="wrap_content"
                        android:layout_height="36dp"
                        android:text="@string/update"
                        android:textSize="12sp"
                        app:cornerRadius="18dp"
                        style="@style/Widget.MaterialComponents.Button" />

                </LinearLayout>

            </LinearLayout>

            <!-- Update All Filters Button -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/updateAllFiltersBtn"
                android:layout_width="match_parent"
                android:layout_height="48dp"
                android:text="@string/update_all_filters"
                android:textSize="16sp"
                android:textStyle="bold"
                app:cornerRadius="24dp"
                app:icon="@drawable/ic_refresh"
                app:iconGravity="textStart"
                style="@style/Widget.MaterialComponents.Button" />

        </LinearLayout>

    </ScrollView>

</LinearLayout>
