// Generated by view binder compiler. Do not edit!
package com.elewashy.egyfilm.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.elewashy.egyfilm.R;
import com.google.android.material.progressindicator.CircularProgressIndicator;
import com.google.android.material.progressindicator.LinearProgressIndicator;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivitySplashBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final LinearProgressIndicator downloadProgress;

  @NonNull
  public final TextView downloadStatus;

  @NonNull
  public final CircularProgressIndicator loadingProgress;

  @NonNull
  public final TextView noInternetText;

  @NonNull
  public final Button retryButton;

  @NonNull
  public final ImageView splashLogo;

  @NonNull
  public final Button updateButton;

  @NonNull
  public final LinearLayout updateDialog;

  @NonNull
  public final TextView updateMessage;

  @NonNull
  public final TextView updateTitle;

  private ActivitySplashBinding(@NonNull ConstraintLayout rootView,
      @NonNull LinearProgressIndicator downloadProgress, @NonNull TextView downloadStatus,
      @NonNull CircularProgressIndicator loadingProgress, @NonNull TextView noInternetText,
      @NonNull Button retryButton, @NonNull ImageView splashLogo, @NonNull Button updateButton,
      @NonNull LinearLayout updateDialog, @NonNull TextView updateMessage,
      @NonNull TextView updateTitle) {
    this.rootView = rootView;
    this.downloadProgress = downloadProgress;
    this.downloadStatus = downloadStatus;
    this.loadingProgress = loadingProgress;
    this.noInternetText = noInternetText;
    this.retryButton = retryButton;
    this.splashLogo = splashLogo;
    this.updateButton = updateButton;
    this.updateDialog = updateDialog;
    this.updateMessage = updateMessage;
    this.updateTitle = updateTitle;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivitySplashBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivitySplashBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_splash, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivitySplashBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.downloadProgress;
      LinearProgressIndicator downloadProgress = ViewBindings.findChildViewById(rootView, id);
      if (downloadProgress == null) {
        break missingId;
      }

      id = R.id.downloadStatus;
      TextView downloadStatus = ViewBindings.findChildViewById(rootView, id);
      if (downloadStatus == null) {
        break missingId;
      }

      id = R.id.loadingProgress;
      CircularProgressIndicator loadingProgress = ViewBindings.findChildViewById(rootView, id);
      if (loadingProgress == null) {
        break missingId;
      }

      id = R.id.noInternetText;
      TextView noInternetText = ViewBindings.findChildViewById(rootView, id);
      if (noInternetText == null) {
        break missingId;
      }

      id = R.id.retryButton;
      Button retryButton = ViewBindings.findChildViewById(rootView, id);
      if (retryButton == null) {
        break missingId;
      }

      id = R.id.splashLogo;
      ImageView splashLogo = ViewBindings.findChildViewById(rootView, id);
      if (splashLogo == null) {
        break missingId;
      }

      id = R.id.updateButton;
      Button updateButton = ViewBindings.findChildViewById(rootView, id);
      if (updateButton == null) {
        break missingId;
      }

      id = R.id.updateDialog;
      LinearLayout updateDialog = ViewBindings.findChildViewById(rootView, id);
      if (updateDialog == null) {
        break missingId;
      }

      id = R.id.updateMessage;
      TextView updateMessage = ViewBindings.findChildViewById(rootView, id);
      if (updateMessage == null) {
        break missingId;
      }

      id = R.id.updateTitle;
      TextView updateTitle = ViewBindings.findChildViewById(rootView, id);
      if (updateTitle == null) {
        break missingId;
      }

      return new ActivitySplashBinding((ConstraintLayout) rootView, downloadProgress,
          downloadStatus, loadingProgress, noInternetText, retryButton, splashLogo, updateButton,
          updateDialog, updateMessage, updateTitle);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
