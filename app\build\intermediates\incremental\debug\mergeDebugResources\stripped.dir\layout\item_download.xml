<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="12dp"
    android:layout_marginVertical="6dp"
    app:cardCornerRadius="12dp"
    app:cardElevation="4dp"
    app:cardBackgroundColor="@android:color/transparent"
    android:foreground="?android:attr/selectableItemBackground">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/download_card_gradient"
        android:padding="16dp">

        <androidx.cardview.widget.CardView
            android:id="@+id/cv_file_icon"
            android:layout_width="52dp"
            android:layout_height="52dp"
            app:cardCornerRadius="10dp"
            app:cardElevation="2dp"
            app:cardBackgroundColor="@color/egyfilm_red"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent">

            <ImageView
                android:id="@+id/iv_file_icon"
                android:layout_width="28dp"
                android:layout_height="28dp"
                android:layout_gravity="center"
                android:src="@drawable/ic_download_file"
                app:tint="@color/white"
                android:contentDescription="@string/download_icon_desc" />

        </androidx.cardview.widget.CardView>

        <TextView
            android:id="@+id/tv_file_name"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="12dp"
            android:ellipsize="end"
            android:maxLines="1"
            android:textAppearance="?attr/textAppearanceSubtitle1"
            android:textColor="@color/white"
            android:textStyle="bold"
            android:textSize="16sp"
            app:layout_constraintEnd_toStartOf="@+id/cv_pause_resume"
            app:layout_constraintStart_toEndOf="@+id/cv_file_icon"
            app:layout_constraintTop_toTopOf="@+id/cv_file_icon"
            tools:text="Movie_Example_HD.mp4" />

        <ProgressBar
            android:id="@+id/pb_download_progress"
            style="?android:attr/progressBarStyleHorizontal"
            android:layout_width="0dp"
            android:layout_height="6dp"
            android:layout_marginTop="10dp"
            android:max="100"
            android:progressTint="@color/egyfilm_red"
            android:progressBackgroundTint="@color/egyfilm_gray"
            android:alpha="0.9"
            app:layout_constraintEnd_toEndOf="@+id/tv_file_name"
            app:layout_constraintStart_toStartOf="@+id/tv_file_name"
            app:layout_constraintTop_toBottomOf="@+id/tv_file_name"
            tools:progress="65" />

        <TextView
            android:id="@+id/tv_download_status"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:textAppearance="?attr/textAppearanceCaption"
            android:textColor="@color/egyfilm_gray"
            android:textSize="12sp"
            app:layout_constraintStart_toStartOf="@+id/pb_download_progress"
            app:layout_constraintTop_toBottomOf="@+id/pb_download_progress"
            tools:text="Downloading: 65% (12.3 MB / 18.9 MB)" />

        <TextView
            android:id="@+id/tv_download_speed"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:textAppearance="?attr/textAppearanceCaption"
            android:textColor="@color/egyfilm_red"
            android:textStyle="bold"
            android:textSize="12sp"
            app:layout_constraintBaseline_toBaselineOf="@+id/tv_download_status"
            app:layout_constraintStart_toEndOf="@+id/tv_download_status"
            tools:text="1.2 MB/s" />

        <androidx.cardview.widget.CardView
            android:id="@+id/cv_pause_resume"
            android:layout_width="44dp"
            android:layout_height="44dp"
            android:layout_marginEnd="8dp"
            app:cardCornerRadius="22dp"
            app:cardElevation="3dp"
            app:cardBackgroundColor="@color/egyfilm_gray"
            app:layout_constraintBottom_toBottomOf="@+id/cv_file_icon"
            app:layout_constraintEnd_toStartOf="@+id/cv_cancel"
            app:layout_constraintTop_toTopOf="@+id/cv_file_icon">

            <ImageButton
                android:id="@+id/btn_pause_resume"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:contentDescription="@string/pause_resume_button_desc"
                android:src="@drawable/ic_pause"
                app:tint="@color/white" />

        </androidx.cardview.widget.CardView>

        <androidx.cardview.widget.CardView
            android:id="@+id/cv_cancel"
            android:layout_width="44dp"
            android:layout_height="44dp"
            app:cardCornerRadius="22dp"
            app:cardElevation="3dp"
            app:cardBackgroundColor="@color/egyfilm_red"
            app:layout_constraintBottom_toBottomOf="@+id/cv_file_icon"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@+id/cv_file_icon">

            <ImageButton
                android:id="@+id/btn_cancel"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:contentDescription="@string/cancel_button_desc"
                android:src="@drawable/ic_cancel"
                app:tint="@color/white" />

        </androidx.cardview.widget.CardView>

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.cardview.widget.CardView>
