<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#000000">

    <ImageView
        android:id="@+id/splashLogo"
        android:layout_width="150dp"
        android:layout_height="150dp"
        android:src="@mipmap/ic_launcher"
        android:elevation="8dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.4" />

    <com.google.android.material.progressindicator.CircularProgressIndicator
        android:id="@+id/loadingProgress"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="48dp"
        android:indeterminate="true"
        android:progress="0"
        app:indicatorColor="#E50914"
        app:indicatorDirectionCircular="clockwise"
        app:indicatorSize="64dp"
        app:trackColor="#22E50914"
        app:trackThickness="3dp"
        app:indicatorTrackGapSize="4dp"
        app:showAnimationBehavior="outward"
        app:hideAnimationBehavior="inward"
        android:indeterminateBehavior="repeat"
        android:indeterminateDuration="1000"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/splashLogo" />

    <TextView
        android:id="@+id/noInternetText"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:text="No Internet Connection"
        android:textColor="#E50914"
        android:textSize="18sp"
        android:visibility="gone"
        android:textStyle="bold"
        android:elevation="4dp"
        android:background="@drawable/no_internet_background"
        android:paddingStart="20dp"
        android:paddingEnd="20dp"
        android:paddingTop="12dp"
        android:paddingBottom="12dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/loadingProgress" />

    <Button
        android:id="@+id/retryButton"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:text="Retry"
        android:visibility="gone"
        android:backgroundTint="#E50914"
        android:textColor="#FFFFFF"
        android:paddingStart="32dp"
        android:paddingEnd="32dp"
        android:elevation="4dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/noInternetText" />

    <!-- Update Dialog Views -->
    <LinearLayout
        android:id="@+id/updateDialog"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="32dp"
        android:layout_marginEnd="32dp"
        android:background="@drawable/custom_design"
        android:elevation="8dp"
        android:orientation="vertical"
        android:padding="24dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/updateTitle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="12dp"
            android:gravity="center"
            android:text="New Update Available"
            android:textColor="#E50914"
            android:textSize="20sp"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/updateMessage"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="20dp"
            android:gravity="center"
            android:text="A new update is available for the app"
            android:textColor="#FFFFFF"
            android:textSize="16sp" />

        <com.google.android.material.progressindicator.LinearProgressIndicator
            android:id="@+id/downloadProgress"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="12dp"
            android:visibility="gone"
            app:indicatorColor="#E50914"
            app:trackColor="#33E50914"
            app:trackThickness="4dp" />

        <TextView
            android:id="@+id/downloadStatus"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="20dp"
            android:gravity="center"
            android:text=""
            android:textColor="#CCCCCC"
            android:textSize="14sp"
            android:visibility="gone" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center">

            <Button
                android:id="@+id/updateButton"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:backgroundTint="#E50914"
                android:text="Update Now"
                android:textColor="#FFFFFF"
                android:paddingStart="32dp"
                android:paddingEnd="32dp" />

        </LinearLayout>

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
