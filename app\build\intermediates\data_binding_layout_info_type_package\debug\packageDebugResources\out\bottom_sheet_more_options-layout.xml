<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="bottom_sheet_more_options" modulePackage="com.elewashy.egyfilm" filePath="app\src\main\res\layout\bottom_sheet_more_options.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/bottom_sheet_more_options_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="114" endOffset="14"/></Target><Target id="@+id/backBtn" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="22" startOffset="8" endLine="36" endOffset="40"/></Target><Target id="@+id/forwardBtn" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="38" startOffset="8" endLine="52" endOffset="40"/></Target><Target id="@+id/downloadsBtn" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="55" startOffset="8" endLine="69" endOffset="40"/></Target><Target id="@+id/fullscreenBtn" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="80" startOffset="8" endLine="94" endOffset="40"/></Target><Target id="@+id/settingsBtn" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="96" startOffset="8" endLine="110" endOffset="40"/></Target></Targets></Layout>