<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.motion.widget.MotionLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/egyfilm_black"
    app:layoutDescription="@xml/activity_main_scene"
    tools:context=".activity.MainActivity">

    <LinearLayout
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:background="@color/egyfilm_dark_gray"
        android:elevation="4dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:gravity="center_vertical"
            android:paddingHorizontal="10dp"
            android:orientation="horizontal">

            <ImageButton
                android:id="@+id/backButton"
                android:layout_width="34dp"
                android:layout_height="34dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:contentDescription="@string/back"
                android:src="@drawable/ic_back"
                android:padding="7dp"
                app:tint="@color/white" />

            <Space
                android:layout_width="10dp"
                android:layout_height="wrap_content" />

            <com.google.android.material.imageview.ShapeableImageView
                android:id="@+id/webIcon"
                android:layout_width="34dp"
                android:layout_height="34dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:padding="7dp"
                android:src="@drawable/ic_search" />

            <Space
                android:layout_width="10dp"
                android:layout_height="wrap_content" />

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/topSearchBar"
                android:layout_width="0dp"
                android:layout_height="34dp"
                android:layout_weight="1"
                android:background="@drawable/search_bar_background"
                android:hint="@string/enter_url"
                android:paddingHorizontal="16dp"
                android:singleLine="true"
                android:textColor="@color/white"
                android:textColorHint="@color/egyfilm_gray" />

            <!-- Removed tabsBtn -->
            <Space
                android:layout_width="10dp"
                android:layout_height="wrap_content" />

            <ImageButton
                android:id="@+id/refreshBtn"
                android:layout_width="34dp"
                android:layout_height="34dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:contentDescription="@string/refresh_btn"
                android:src="@drawable/ic_refresh"
                android:padding="7dp"
                app:tint="@color/white" />

            <Space
                android:layout_width="10dp"
                android:layout_height="wrap_content" />

            <ImageButton
                android:id="@+id/goBtn"
                android:layout_width="34dp"
                android:layout_height="34dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:contentDescription="@string/home_btn"
                android:src="@drawable/ic_home"
                android:padding="7dp"
                app:tint="@color/white" />

            <Space
                android:layout_width="10dp"
                android:layout_height="wrap_content" />

            <ImageButton
                android:id="@+id/moreOptionsBtn"
                android:layout_width="34dp"
                android:layout_height="34dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:contentDescription="@string/more_options"
                android:src="@drawable/ic_more_vert"
                android:padding="7dp"
                app:tint="@color/white" />

        </LinearLayout>

        <com.google.android.material.progressindicator.LinearProgressIndicator
            android:id="@+id/progressBar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:scaleY="0.5"
            app:indicatorColor="@color/egyfilm_red"
            android:visibility="gone" />

    </LinearLayout>

    <!-- Replaced ViewPager2 with FrameLayout -->
    <FrameLayout
        android:id="@+id/fragmentContainer"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:background="@color/egyfilm_black"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/toolbar" />

    <!-- Removed FloatingActionButton settingBtn -->

</androidx.constraintlayout.motion.widget.MotionLayout>
