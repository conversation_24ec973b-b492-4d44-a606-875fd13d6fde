  Manifest android  POST_NOTIFICATIONS android.Manifest.permission  READ_EXTERNAL_STORAGE android.Manifest.permission  WRITE_EXTERNAL_STORAGE android.Manifest.permission  home android.R.id  SuppressLint android.annotation  ACTION_CANCEL_DOWNLOAD android.app  ACTION_CHECK_FAILED_DOWNLOADS android.app  ACTION_DOWNLOAD_UPDATE android.app  ACTION_PAUSE_DOWNLOAD android.app  ACTION_RESUME_DOWNLOAD android.app  ACTION_START_DOWNLOAD android.app  AUTO_RETRY_INTERVAL_MS android.app  Activity android.app  AlarmManager android.app  
AtomicInteger android.app  
AtomicLong android.app  Binder android.app  Boolean android.app  Build android.app  	ByteArray android.app  CancellationException android.app  ConcurrentHashMap android.app  ConnectivityManager android.app  Context android.app  CoroutineScope android.app  Dispatchers android.app  DownloadActivity android.app  DownloadItem android.app  DownloadService android.app  DownloadStatus android.app  
EXTRA_COOKIES android.app  EXTRA_DOWNLOAD_ID android.app  EXTRA_DOWNLOAD_ID_UPDATE android.app  EXTRA_DOWNLOAD_LIST_CHANGED android.app  EXTRA_FILE_NAME android.app  EXTRA_MIME_TYPE android.app  EXTRA_ORIGIN android.app  
EXTRA_REFERER android.app  EXTRA_SOURCE android.app  	EXTRA_URL android.app  EXTRA_USER_AGENT android.app  Environment android.app  	Exception android.app  File android.app  FileOutputStream android.app  FileProvider android.app  Gson android.app  HttpURLConnection android.app  IBinder android.app  IOException android.app  IllegalArgumentException android.app  InputStream android.app  Int android.app  Intent android.app  Job android.app  KEY_DOWNLOAD_ITEMS android.app  KEY_LAST_DOWNLOAD_ID android.app  List android.app  LocalBroadcastManager android.app  Log android.app  Long android.app  MAX_CONCURRENT_JSON_DOWNLOADS android.app  MAX_FAILURE_COUNT android.app  MimeTypeMap android.app  Network android.app  NetworkCapabilities android.app  NetworkRequest android.app  Notification android.app  NotificationChannel android.app  NotificationCompat android.app  NotificationManager android.app  
PREFS_NAME android.app  
PendingIntent android.app  R android.app  Regex android.app  START_STICKY android.app  STOP_FOREGROUND_REMOVE android.app  Service android.app  SharedPreferences android.app  StandardCharsets android.app  String android.app  
SupervisorJob android.app  Suppress android.app  Synchronized android.app  System android.app  TAG android.app  	TypeToken android.app  URL android.app  
URLDecoder android.app  UnsupportedEncodingException android.app  Uri android.app  any android.app  apply android.app  
coerceAtLeast android.app  	compareBy android.app  contains android.app  createControlIntent android.app  
downloadItems android.app  downloadJobs android.app  endsWith android.app  failedDownloads android.app  filter android.app  forEach android.app  format android.app  getCleanFileName android.app  isActive android.app  isBlank android.app  isEmpty android.app  isNetworkAvailable android.app  
isNotEmpty android.app  java android.app  lastIndexOf android.app  launch android.app  let android.app  	lowercase android.app  map android.app  minByOrNull android.app  
plusAssign android.app  removeSurrounding android.app  replace android.app  
retryAttempts android.app  retryFailedDownloads android.app  saveDownloadState android.app  serviceScope android.app  set android.app  
sortedWith android.app  split android.app  startOrQueueDownload android.app  
startsWith android.app  	substring android.app  substringBeforeLast android.app  take android.app  thenByDescending android.app  toList android.app  toLongOrNull android.app  trim android.app  updateDownloadProgress android.app  updateDownloadStatus android.app  updateNotification android.app  ActivityDownloadBinding android.app.Activity  ActivityFilterUpdatesBinding android.app.Activity  ActivityMainBinding android.app.Activity  ActivityNotFoundException android.app.Activity  ActivityResultContracts android.app.Activity  ActivitySettingsBinding android.app.Activity  	AdBlocker android.app.Activity  AlertDialog android.app.Activity  BroadcastReceiver android.app.Activity  BrowseFragment android.app.Activity  Build android.app.Activity  BuildConfig android.app.Activity  CONNECTIVITY_SERVICE android.app.Activity  
ComponentName android.app.Activity  ConnectivityManager android.app.Activity  Context android.app.Activity  Date android.app.Activity  Dispatchers android.app.Activity  DownloadActivity android.app.Activity  DownloadAdapter android.app.Activity  DownloadService android.app.Activity  DownloadStatus android.app.Activity  EgyFilmTheme android.app.Activity  Environment android.app.Activity  	Exception android.app.Activity  File android.app.Activity  FileProvider android.app.Activity  FilterUpdatesActivity android.app.Activity  FirebaseMessaging android.app.Activity  Gson android.app.Activity  Handler android.app.Activity  HttpURLConnection android.app.Activity  IBinder android.app.Activity  IOException android.app.Activity  IllegalArgumentException android.app.Activity  Intent android.app.Activity  IntentFilter android.app.Activity  LinearLayoutManager android.app.Activity  LocalBroadcastManager android.app.Activity  Locale android.app.Activity  Log android.app.Activity  Looper android.app.Activity  MODE_PRIVATE android.app.Activity  MainActivity android.app.Activity  MoreOptionsBottomSheetFragment android.app.Activity  NetworkCapabilities android.app.Activity  OVERRIDE_TRANSITION_OPEN android.app.Activity  OnBackPressedCallback android.app.Activity  OpenLinkValidator android.app.Activity  PermissionManager android.app.Activity  R android.app.Activity  ServiceConnection android.app.Activity  Settings android.app.Activity  SimpleDateFormat android.app.Activity  Snackbar android.app.Activity  SplashScreen android.app.Activity  Suppress android.app.Activity  System android.app.Activity  Toast android.app.Activity  UPDATE_CHECK_URL android.app.Activity  URL android.app.Activity  UpdateResponse android.app.Activity  Uri android.app.Activity  ValidLinkChecker android.app.Activity  View android.app.Activity  WindowCompat android.app.Activity  WindowInsetsCompat android.app.Activity  WindowInsetsControllerCompat android.app.Activity  
WindowManager android.app.Activity  	adBlocker android.app.Activity  also android.app.Activity  android android.app.Activity  apply android.app.Activity  binding android.app.Activity  browseFragment android.app.Activity  bufferedReader android.app.Activity  clearAdBlockerTimeConstraints android.app.Activity  createControlIntent android.app.Activity  createStartIntent android.app.Activity  delay android.app.Activity  downloadAdapter android.app.Activity  downloadService android.app.Activity  	emptyList android.app.Activity  endsWith android.app.Activity  finish android.app.Activity  getInstance android.app.Activity  	getString android.app.Activity  getSystemService android.app.Activity  getValue android.app.Activity  intent android.app.Activity  isBound android.app.Activity  isEmpty android.app.Activity  isFullscreen android.app.Activity  
isInitialized android.app.Activity  isManualFullscreen android.app.Activity  
isNotEmpty android.app.Activity  	isVisible android.app.Activity  java android.app.Activity  launch android.app.Activity  layoutInflater android.app.Activity  let android.app.Activity  lifecycleScope android.app.Activity  
loadDownloads android.app.Activity  loadLastUpdatedTimes android.app.Activity  map android.app.Activity  maxOf android.app.Activity  mutableFloatStateOf android.app.Activity  mutableStateOf android.app.Activity  onActivityResult android.app.Activity  onBackPressedDispatcher android.app.Activity  onCreate android.app.Activity  onNewIntent android.app.Activity  onOptionsItemSelected android.app.Activity  openLinkValidator android.app.Activity  overrideActivityTransition android.app.Activity  overridePendingTransition android.app.Activity  provideDelegate android.app.Activity  readText android.app.Activity  requestedOrientation android.app.Activity  
runOnUiThread android.app.Activity  saveUpdateTime android.app.Activity  
setContent android.app.Activity  	setIntent android.app.Activity  setValue android.app.Activity  split android.app.Activity  
startActivity android.app.Activity  startService android.app.Activity  thread android.app.Activity  toIntOrNull android.app.Activity  until android.app.Activity  use android.app.Activity  validLinkChecker android.app.Activity  window android.app.Activity  with android.app.Activity  withContext android.app.Activity  DownloadBinder $android.app.Activity.DownloadService  
RTC_WAKEUP android.app.AlarmManager  set android.app.AlarmManager  setAndAllowWhileIdle android.app.AlarmManager  NetworkCallback android.app.ConnectivityManager  show android.app.Dialog  window android.app.Dialog  CHANNEL_DESCRIPTION android.app.NotificationChannel  apply android.app.NotificationChannel  description android.app.NotificationChannel  enableLights android.app.NotificationChannel  enableVibration android.app.NotificationChannel  IMPORTANCE_HIGH android.app.NotificationManager  IMPORTANCE_LOW android.app.NotificationManager  cancel android.app.NotificationManager  	cancelAll android.app.NotificationManager  createNotificationChannel android.app.NotificationManager  notify android.app.NotificationManager  FLAG_IMMUTABLE android.app.PendingIntent  
FLAG_ONE_SHOT android.app.PendingIntent  FLAG_UPDATE_CURRENT android.app.PendingIntent  getActivity android.app.PendingIntent  
getService android.app.PendingIntent  ACTION_CANCEL_DOWNLOAD android.app.Service  ACTION_CHECK_FAILED_DOWNLOADS android.app.Service  ACTION_DOWNLOAD_UPDATE android.app.Service  ACTION_PAUSE_DOWNLOAD android.app.Service  ACTION_RESUME_DOWNLOAD android.app.Service  ACTION_START_DOWNLOAD android.app.Service  AUTO_RETRY_INTERVAL_MS android.app.Service  AlarmManager android.app.Service  
AtomicInteger android.app.Service  
AtomicLong android.app.Service  Build android.app.Service  	ByteArray android.app.Service  CHANNEL_DESCRIPTION android.app.Service  
CHANNEL_ID android.app.Service  CHANNEL_NAME android.app.Service  CancellationException android.app.Service  ConcurrentHashMap android.app.Service  ConnectivityManager android.app.Service  Context android.app.Service  CoroutineScope android.app.Service  Dispatchers android.app.Service  DownloadActivity android.app.Service  DownloadItem android.app.Service  DownloadService android.app.Service  DownloadStatus android.app.Service  
EXTRA_COOKIES android.app.Service  EXTRA_DOWNLOAD_ID android.app.Service  EXTRA_DOWNLOAD_ID_UPDATE android.app.Service  EXTRA_DOWNLOAD_LIST_CHANGED android.app.Service  EXTRA_FILE_NAME android.app.Service  EXTRA_MIME_TYPE android.app.Service  EXTRA_ORIGIN android.app.Service  
EXTRA_REFERER android.app.Service  EXTRA_SOURCE android.app.Service  	EXTRA_URL android.app.Service  EXTRA_USER_AGENT android.app.Service  Environment android.app.Service  	Exception android.app.Service  File android.app.Service  FileOutputStream android.app.Service  FileProvider android.app.Service  Gson android.app.Service  HttpURLConnection android.app.Service  IOException android.app.Service  IllegalArgumentException android.app.Service  InputStream android.app.Service  Int android.app.Service  Intent android.app.Service  Job android.app.Service  KEY_DOWNLOAD_ITEMS android.app.Service  KEY_LAST_DOWNLOAD_ID android.app.Service  List android.app.Service  LocalBroadcastManager android.app.Service  Log android.app.Service  Long android.app.Service  MAX_CONCURRENT_JSON_DOWNLOADS android.app.Service  MAX_FAILURE_COUNT android.app.Service  MainActivity android.app.Service  MimeTypeMap android.app.Service  Network android.app.Service  NetworkCapabilities android.app.Service  NetworkRequest android.app.Service  NotificationChannel android.app.Service  NotificationCompat android.app.Service  NotificationManager android.app.Service  
PREFS_NAME android.app.Service  
PendingIntent android.app.Service  R android.app.Service  Regex android.app.Service  START_STICKY android.app.Service  STOP_FOREGROUND_REMOVE android.app.Service  StandardCharsets android.app.Service  String android.app.Service  
SupervisorJob android.app.Service  Suppress android.app.Service  System android.app.Service  TAG android.app.Service  	TypeToken android.app.Service  URL android.app.Service  
URLDecoder android.app.Service  UnsupportedEncodingException android.app.Service  Uri android.app.Service  any android.app.Service  apply android.app.Service  
coerceAtLeast android.app.Service  	compareBy android.app.Service  contains android.app.Service  createControlIntent android.app.Service  
downloadItems android.app.Service  downloadJobs android.app.Service  endsWith android.app.Service  failedDownloads android.app.Service  filter android.app.Service  format android.app.Service  getCleanFileName android.app.Service  isActive android.app.Service  isBlank android.app.Service  isEmpty android.app.Service  isNetworkAvailable android.app.Service  
isNotEmpty android.app.Service  java android.app.Service  lastIndexOf android.app.Service  launch android.app.Service  let android.app.Service  	lowercase android.app.Service  map android.app.Service  minByOrNull android.app.Service  onCreate android.app.Service  	onDestroy android.app.Service  
plusAssign android.app.Service  removeSurrounding android.app.Service  replace android.app.Service  
retryAttempts android.app.Service  retryFailedDownloads android.app.Service  saveDownloadState android.app.Service  serviceScope android.app.Service  set android.app.Service  
sortedWith android.app.Service  split android.app.Service  startForeground android.app.Service  startOrQueueDownload android.app.Service  
startsWith android.app.Service  stopForeground android.app.Service  	substring android.app.Service  substringBeforeLast android.app.Service  take android.app.Service  thenByDescending android.app.Service  toList android.app.Service  toLongOrNull android.app.Service  trim android.app.Service  updateDownloadProgress android.app.Service  updateDownloadStatus android.app.Service  updateNotification android.app.Service  with android.app.Service  NetworkCallback 'android.app.Service.ConnectivityManager  ActivityDownloadBinding android.content  ActivityNotFoundException android.content  AlertDialog android.content  AppCompatActivity android.content  Boolean android.content  BroadcastReceiver android.content  Build android.content  Bundle android.content  
ComponentName android.content  
ContentValues android.content  Context android.content  Dispatchers android.content  DownloadAdapter android.content  DownloadItem android.content  DownloadService android.content  DownloadStatus android.content  Environment android.content  	Exception android.content  File android.content  FileProvider android.content  IBinder android.content  IllegalArgumentException android.content  Intent android.content  IntentFilter android.content  LinearLayoutManager android.content  List android.content  LocalBroadcastManager android.content  Long android.content  MenuItem android.content  PermissionManager android.content  ServiceConnection android.content  SharedPreferences android.content  String android.content  Toast android.content  Unit android.content  Uri android.content  also android.content  android android.content  apply android.content  createControlIntent android.content  createStartIntent android.content  delay android.content  downloadAdapter android.content  downloadService android.content  	emptyList android.content  endsWith android.content  isBound android.content  isEmpty android.content  
isNotEmpty android.content  java android.content  launch android.content  let android.content  lifecycleScope android.content  
loadDownloads android.content  startService android.content  Dispatchers !android.content.BroadcastReceiver  DownloadService !android.content.BroadcastReceiver  downloadService !android.content.BroadcastReceiver  isBound !android.content.BroadcastReceiver  launch !android.content.BroadcastReceiver  lifecycleScope !android.content.BroadcastReceiver  
loadDownloads !android.content.BroadcastReceiver  getType android.content.ContentResolver  insert android.content.ContentResolver  openOutputStream android.content.ContentResolver  update android.content.ContentResolver  Build android.content.ContentValues  Environment android.content.ContentValues  
MediaStore android.content.ContentValues  apply android.content.ContentValues  clear android.content.ContentValues  put android.content.ContentValues  ACTION_CANCEL_DOWNLOAD android.content.Context  ACTION_CHECK_FAILED_DOWNLOADS android.content.Context  ACTION_DOWNLOAD_UPDATE android.content.Context  ACTION_PAUSE_DOWNLOAD android.content.Context  ACTION_RESUME_DOWNLOAD android.content.Context  ACTION_START_DOWNLOAD android.content.Context  
ALARM_SERVICE android.content.Context  AUTO_RETRY_INTERVAL_MS android.content.Context  ActivityDownloadBinding android.content.Context  ActivityFilterUpdatesBinding android.content.Context  ActivityMainBinding android.content.Context  ActivityNotFoundException android.content.Context  ActivityResultContracts android.content.Context  ActivitySettingsBinding android.content.Context  	AdBlocker android.content.Context  AlarmManager android.content.Context  AlertDialog android.content.Context  
AtomicInteger android.content.Context  
AtomicLong android.content.Context  BIND_AUTO_CREATE android.content.Context  BroadcastReceiver android.content.Context  BrowseFragment android.content.Context  Build android.content.Context  BuildConfig android.content.Context  	ByteArray android.content.Context  CHANNEL_DESCRIPTION android.content.Context  
CHANNEL_ID android.content.Context  CHANNEL_NAME android.content.Context  CONNECTIVITY_SERVICE android.content.Context  CancellationException android.content.Context  
ComponentName android.content.Context  ConcurrentHashMap android.content.Context  ConnectivityManager android.content.Context  Context android.content.Context  CoroutineScope android.content.Context  Date android.content.Context  Dispatchers android.content.Context  DownloadActivity android.content.Context  DownloadAdapter android.content.Context  DownloadItem android.content.Context  DownloadService android.content.Context  DownloadStatus android.content.Context  
EXTRA_COOKIES android.content.Context  EXTRA_DOWNLOAD_ID android.content.Context  EXTRA_DOWNLOAD_ID_UPDATE android.content.Context  EXTRA_DOWNLOAD_LIST_CHANGED android.content.Context  EXTRA_FILE_NAME android.content.Context  EXTRA_MIME_TYPE android.content.Context  EXTRA_ORIGIN android.content.Context  
EXTRA_REFERER android.content.Context  EXTRA_SOURCE android.content.Context  	EXTRA_URL android.content.Context  EXTRA_USER_AGENT android.content.Context  EgyFilmTheme android.content.Context  Environment android.content.Context  	Exception android.content.Context  File android.content.Context  FileOutputStream android.content.Context  FileProvider android.content.Context  FilterUpdatesActivity android.content.Context  FirebaseMessaging android.content.Context  Gson android.content.Context  Handler android.content.Context  HttpURLConnection android.content.Context  IBinder android.content.Context  IOException android.content.Context  IllegalArgumentException android.content.Context  InputStream android.content.Context  Int android.content.Context  Intent android.content.Context  IntentFilter android.content.Context  Job android.content.Context  KEY_DOWNLOAD_ITEMS android.content.Context  KEY_LAST_DOWNLOAD_ID android.content.Context  LinearLayoutManager android.content.Context  List android.content.Context  LocalBroadcastManager android.content.Context  Locale android.content.Context  Log android.content.Context  Long android.content.Context  Looper android.content.Context  MAX_CONCURRENT_JSON_DOWNLOADS android.content.Context  MAX_FAILURE_COUNT android.content.Context  MODE_PRIVATE android.content.Context  MainActivity android.content.Context  MimeTypeMap android.content.Context  MoreOptionsBottomSheetFragment android.content.Context  NOTIFICATION_SERVICE android.content.Context  Network android.content.Context  NetworkCapabilities android.content.Context  NetworkRequest android.content.Context  NotificationChannel android.content.Context  NotificationCompat android.content.Context  NotificationManager android.content.Context  OVERRIDE_TRANSITION_OPEN android.content.Context  OnBackPressedCallback android.content.Context  OpenLinkValidator android.content.Context  
PREFS_NAME android.content.Context  
PendingIntent android.content.Context  PermissionManager android.content.Context  R android.content.Context  Regex android.content.Context  START_STICKY android.content.Context  STOP_FOREGROUND_REMOVE android.content.Context  ServiceConnection android.content.Context  Settings android.content.Context  SimpleDateFormat android.content.Context  Snackbar android.content.Context  SplashScreen android.content.Context  StandardCharsets android.content.Context  String android.content.Context  
SupervisorJob android.content.Context  Suppress android.content.Context  System android.content.Context  TAG android.content.Context  Toast android.content.Context  	TypeToken android.content.Context  UPDATE_CHECK_URL android.content.Context  URL android.content.Context  
URLDecoder android.content.Context  UnsupportedEncodingException android.content.Context  UpdateResponse android.content.Context  Uri android.content.Context  ValidLinkChecker android.content.Context  View android.content.Context  WindowCompat android.content.Context  WindowInsetsCompat android.content.Context  WindowInsetsControllerCompat android.content.Context  
WindowManager android.content.Context  	adBlocker android.content.Context  also android.content.Context  android android.content.Context  any android.content.Context  applicationContext android.content.Context  apply android.content.Context  binding android.content.Context  browseFragment android.content.Context  bufferedReader android.content.Context  clearAdBlockerTimeConstraints android.content.Context  
coerceAtLeast android.content.Context  	compareBy android.content.Context  contains android.content.Context  createControlIntent android.content.Context  createStartIntent android.content.Context  delay android.content.Context  downloadAdapter android.content.Context  
downloadItems android.content.Context  downloadJobs android.content.Context  downloadService android.content.Context  	emptyList android.content.Context  endsWith android.content.Context  failedDownloads android.content.Context  filter android.content.Context  format android.content.Context  getCleanFileName android.content.Context  getInstance android.content.Context  getSharedPreferences android.content.Context  	getString android.content.Context  getSystemService android.content.Context  getValue android.content.Context  isActive android.content.Context  isBlank android.content.Context  isBound android.content.Context  isEmpty android.content.Context  isFullscreen android.content.Context  
isInitialized android.content.Context  isManualFullscreen android.content.Context  isNetworkAvailable android.content.Context  
isNotEmpty android.content.Context  	isVisible android.content.Context  java android.content.Context  lastIndexOf android.content.Context  launch android.content.Context  let android.content.Context  lifecycleScope android.content.Context  
loadDownloads android.content.Context  loadLastUpdatedTimes android.content.Context  	lowercase android.content.Context  map android.content.Context  maxOf android.content.Context  minByOrNull android.content.Context  mutableFloatStateOf android.content.Context  mutableStateOf android.content.Context  onBackPressedDispatcher android.content.Context  openLinkValidator android.content.Context  packageName android.content.Context  
plusAssign android.content.Context  provideDelegate android.content.Context  readText android.content.Context  removeSurrounding android.content.Context  replace android.content.Context  
retryAttempts android.content.Context  retryFailedDownloads android.content.Context  saveDownloadState android.content.Context  saveUpdateTime android.content.Context  serviceScope android.content.Context  set android.content.Context  
setContent android.content.Context  setValue android.content.Context  
sortedWith android.content.Context  split android.content.Context  
startActivity android.content.Context  startOrQueueDownload android.content.Context  startService android.content.Context  
startsWith android.content.Context  	substring android.content.Context  substringBeforeLast android.content.Context  take android.content.Context  thenByDescending android.content.Context  thread android.content.Context  toIntOrNull android.content.Context  toList android.content.Context  toLongOrNull android.content.Context  trim android.content.Context  until android.content.Context  updateDownloadProgress android.content.Context  updateDownloadStatus android.content.Context  updateNotification android.content.Context  use android.content.Context  validLinkChecker android.content.Context  with android.content.Context  withContext android.content.Context  NetworkCallback +android.content.Context.ConnectivityManager  DownloadBinder 'android.content.Context.DownloadService  ACTION_CANCEL_DOWNLOAD android.content.ContextWrapper  ACTION_CHECK_FAILED_DOWNLOADS android.content.ContextWrapper  ACTION_DOWNLOAD_UPDATE android.content.ContextWrapper  ACTION_PAUSE_DOWNLOAD android.content.ContextWrapper  ACTION_RESUME_DOWNLOAD android.content.ContextWrapper  ACTION_START_DOWNLOAD android.content.ContextWrapper  AUTO_RETRY_INTERVAL_MS android.content.ContextWrapper  ActivityDownloadBinding android.content.ContextWrapper  ActivityFilterUpdatesBinding android.content.ContextWrapper  ActivityMainBinding android.content.ContextWrapper  ActivityNotFoundException android.content.ContextWrapper  ActivityResultContracts android.content.ContextWrapper  ActivitySettingsBinding android.content.ContextWrapper  	AdBlocker android.content.ContextWrapper  AlarmManager android.content.ContextWrapper  AlertDialog android.content.ContextWrapper  
AtomicInteger android.content.ContextWrapper  
AtomicLong android.content.ContextWrapper  BroadcastReceiver android.content.ContextWrapper  BrowseFragment android.content.ContextWrapper  Build android.content.ContextWrapper  BuildConfig android.content.ContextWrapper  	ByteArray android.content.ContextWrapper  CHANNEL_DESCRIPTION android.content.ContextWrapper  
CHANNEL_ID android.content.ContextWrapper  CHANNEL_NAME android.content.ContextWrapper  CONNECTIVITY_SERVICE android.content.ContextWrapper  CancellationException android.content.ContextWrapper  
ComponentName android.content.ContextWrapper  ConcurrentHashMap android.content.ContextWrapper  ConnectivityManager android.content.ContextWrapper  Context android.content.ContextWrapper  CoroutineScope android.content.ContextWrapper  Date android.content.ContextWrapper  Dispatchers android.content.ContextWrapper  DownloadActivity android.content.ContextWrapper  DownloadAdapter android.content.ContextWrapper  DownloadItem android.content.ContextWrapper  DownloadService android.content.ContextWrapper  DownloadStatus android.content.ContextWrapper  
EXTRA_COOKIES android.content.ContextWrapper  EXTRA_DOWNLOAD_ID android.content.ContextWrapper  EXTRA_DOWNLOAD_ID_UPDATE android.content.ContextWrapper  EXTRA_DOWNLOAD_LIST_CHANGED android.content.ContextWrapper  EXTRA_FILE_NAME android.content.ContextWrapper  EXTRA_MIME_TYPE android.content.ContextWrapper  EXTRA_ORIGIN android.content.ContextWrapper  
EXTRA_REFERER android.content.ContextWrapper  EXTRA_SOURCE android.content.ContextWrapper  	EXTRA_URL android.content.ContextWrapper  EXTRA_USER_AGENT android.content.ContextWrapper  EgyFilmTheme android.content.ContextWrapper  Environment android.content.ContextWrapper  	Exception android.content.ContextWrapper  File android.content.ContextWrapper  FileOutputStream android.content.ContextWrapper  FileProvider android.content.ContextWrapper  FilterUpdatesActivity android.content.ContextWrapper  FirebaseMessaging android.content.ContextWrapper  Gson android.content.ContextWrapper  Handler android.content.ContextWrapper  HttpURLConnection android.content.ContextWrapper  IBinder android.content.ContextWrapper  IOException android.content.ContextWrapper  IllegalArgumentException android.content.ContextWrapper  InputStream android.content.ContextWrapper  Int android.content.ContextWrapper  Intent android.content.ContextWrapper  IntentFilter android.content.ContextWrapper  Job android.content.ContextWrapper  KEY_DOWNLOAD_ITEMS android.content.ContextWrapper  KEY_LAST_DOWNLOAD_ID android.content.ContextWrapper  LinearLayoutManager android.content.ContextWrapper  List android.content.ContextWrapper  LocalBroadcastManager android.content.ContextWrapper  Locale android.content.ContextWrapper  Log android.content.ContextWrapper  Long android.content.ContextWrapper  Looper android.content.ContextWrapper  MAX_CONCURRENT_JSON_DOWNLOADS android.content.ContextWrapper  MAX_FAILURE_COUNT android.content.ContextWrapper  MODE_PRIVATE android.content.ContextWrapper  MainActivity android.content.ContextWrapper  MimeTypeMap android.content.ContextWrapper  MoreOptionsBottomSheetFragment android.content.ContextWrapper  Network android.content.ContextWrapper  NetworkCapabilities android.content.ContextWrapper  NetworkRequest android.content.ContextWrapper  NotificationChannel android.content.ContextWrapper  NotificationCompat android.content.ContextWrapper  NotificationManager android.content.ContextWrapper  OVERRIDE_TRANSITION_OPEN android.content.ContextWrapper  OnBackPressedCallback android.content.ContextWrapper  OpenLinkValidator android.content.ContextWrapper  
PREFS_NAME android.content.ContextWrapper  
PendingIntent android.content.ContextWrapper  PermissionManager android.content.ContextWrapper  R android.content.ContextWrapper  Regex android.content.ContextWrapper  START_STICKY android.content.ContextWrapper  STOP_FOREGROUND_REMOVE android.content.ContextWrapper  ServiceConnection android.content.ContextWrapper  Settings android.content.ContextWrapper  SimpleDateFormat android.content.ContextWrapper  Snackbar android.content.ContextWrapper  SplashScreen android.content.ContextWrapper  StandardCharsets android.content.ContextWrapper  String android.content.ContextWrapper  
SupervisorJob android.content.ContextWrapper  Suppress android.content.ContextWrapper  System android.content.ContextWrapper  TAG android.content.ContextWrapper  Toast android.content.ContextWrapper  	TypeToken android.content.ContextWrapper  UPDATE_CHECK_URL android.content.ContextWrapper  URL android.content.ContextWrapper  
URLDecoder android.content.ContextWrapper  UnsupportedEncodingException android.content.ContextWrapper  UpdateResponse android.content.ContextWrapper  Uri android.content.ContextWrapper  ValidLinkChecker android.content.ContextWrapper  View android.content.ContextWrapper  WindowCompat android.content.ContextWrapper  WindowInsetsCompat android.content.ContextWrapper  WindowInsetsControllerCompat android.content.ContextWrapper  
WindowManager android.content.ContextWrapper  	adBlocker android.content.ContextWrapper  also android.content.ContextWrapper  android android.content.ContextWrapper  any android.content.ContextWrapper  applicationContext android.content.ContextWrapper  apply android.content.ContextWrapper  bindService android.content.ContextWrapper  binding android.content.ContextWrapper  browseFragment android.content.ContextWrapper  bufferedReader android.content.ContextWrapper  clearAdBlockerTimeConstraints android.content.ContextWrapper  
coerceAtLeast android.content.ContextWrapper  	compareBy android.content.ContextWrapper  contains android.content.ContextWrapper  contentResolver android.content.ContextWrapper  createControlIntent android.content.ContextWrapper  createStartIntent android.content.ContextWrapper  delay android.content.ContextWrapper  downloadAdapter android.content.ContextWrapper  
downloadItems android.content.ContextWrapper  downloadJobs android.content.ContextWrapper  downloadService android.content.ContextWrapper  	emptyList android.content.ContextWrapper  endsWith android.content.ContextWrapper  failedDownloads android.content.ContextWrapper  filter android.content.ContextWrapper  format android.content.ContextWrapper  getCleanFileName android.content.ContextWrapper  getInstance android.content.ContextWrapper  getSharedPreferences android.content.ContextWrapper  	getString android.content.ContextWrapper  getSystemService android.content.ContextWrapper  getValue android.content.ContextWrapper  isActive android.content.ContextWrapper  isBlank android.content.ContextWrapper  isBound android.content.ContextWrapper  isEmpty android.content.ContextWrapper  isFullscreen android.content.ContextWrapper  
isInitialized android.content.ContextWrapper  isManualFullscreen android.content.ContextWrapper  isNetworkAvailable android.content.ContextWrapper  
isNotEmpty android.content.ContextWrapper  	isVisible android.content.ContextWrapper  java android.content.ContextWrapper  lastIndexOf android.content.ContextWrapper  launch android.content.ContextWrapper  let android.content.ContextWrapper  lifecycleScope android.content.ContextWrapper  
loadDownloads android.content.ContextWrapper  loadLastUpdatedTimes android.content.ContextWrapper  	lowercase android.content.ContextWrapper  map android.content.ContextWrapper  maxOf android.content.ContextWrapper  minByOrNull android.content.ContextWrapper  mutableFloatStateOf android.content.ContextWrapper  mutableStateOf android.content.ContextWrapper  onBackPressedDispatcher android.content.ContextWrapper  openLinkValidator android.content.ContextWrapper  packageManager android.content.ContextWrapper  packageName android.content.ContextWrapper  
plusAssign android.content.ContextWrapper  provideDelegate android.content.ContextWrapper  readText android.content.ContextWrapper  removeSurrounding android.content.ContextWrapper  replace android.content.ContextWrapper  
retryAttempts android.content.ContextWrapper  retryFailedDownloads android.content.ContextWrapper  saveDownloadState android.content.ContextWrapper  saveUpdateTime android.content.ContextWrapper  serviceScope android.content.ContextWrapper  set android.content.ContextWrapper  
setContent android.content.ContextWrapper  setValue android.content.ContextWrapper  
sortedWith android.content.ContextWrapper  split android.content.ContextWrapper  startOrQueueDownload android.content.ContextWrapper  startService android.content.ContextWrapper  
startsWith android.content.ContextWrapper  	substring android.content.ContextWrapper  substringBeforeLast android.content.ContextWrapper  take android.content.ContextWrapper  thenByDescending android.content.ContextWrapper  thread android.content.ContextWrapper  toIntOrNull android.content.ContextWrapper  toList android.content.ContextWrapper  toLongOrNull android.content.ContextWrapper  trim android.content.ContextWrapper  
unbindService android.content.ContextWrapper  until android.content.ContextWrapper  updateDownloadProgress android.content.ContextWrapper  updateDownloadStatus android.content.ContextWrapper  updateNotification android.content.ContextWrapper  use android.content.ContextWrapper  validLinkChecker android.content.ContextWrapper  with android.content.ContextWrapper  withContext android.content.ContextWrapper  NetworkCallback 2android.content.ContextWrapper.ConnectivityManager  DownloadBinder .android.content.ContextWrapper.DownloadService  OnClickListener android.content.DialogInterface  dismiss android.content.DialogInterface  <SAM-CONSTRUCTOR> /android.content.DialogInterface.OnClickListener  DownloadBinder android.content.DownloadService  ACTION_CHECK_FAILED_DOWNLOADS android.content.Intent  ACTION_START_DOWNLOAD android.content.Intent  ACTION_VIEW android.content.Intent  
EXTRA_COOKIES android.content.Intent  EXTRA_DOWNLOAD_ID android.content.Intent  EXTRA_DOWNLOAD_ID_UPDATE android.content.Intent  EXTRA_DOWNLOAD_LIST_CHANGED android.content.Intent  EXTRA_FILE_NAME android.content.Intent  EXTRA_MIME_TYPE android.content.Intent  EXTRA_ORIGIN android.content.Intent  
EXTRA_REFERER android.content.Intent  EXTRA_SOURCE android.content.Intent  	EXTRA_URL android.content.Intent  EXTRA_USER_AGENT android.content.Intent  FLAG_ACTIVITY_CLEAR_TOP android.content.Intent  FLAG_ACTIVITY_NEW_TASK android.content.Intent  FLAG_ACTIVITY_REORDER_TO_FRONT android.content.Intent  FLAG_GRANT_READ_URI_PERMISSION android.content.Intent  Intent android.content.Intent  action android.content.Intent  addFlags android.content.Intent  also android.content.Intent  apply android.content.Intent  data android.content.Intent  flags android.content.Intent  getBooleanExtra android.content.Intent  getLongExtra android.content.Intent  getStringExtra android.content.Intent  hasExtra android.content.Intent  let android.content.Intent  putExtra android.content.Intent  setClass android.content.Intent  setData android.content.Intent  setDataAndType android.content.Intent  edit !android.content.SharedPreferences  getLong !android.content.SharedPreferences  	getString !android.content.SharedPreferences  getStringSet !android.content.SharedPreferences  apply (android.content.SharedPreferences.Editor  putLong (android.content.SharedPreferences.Editor  	putString (android.content.SharedPreferences.Editor  putStringSet (android.content.SharedPreferences.Editor  ActivityInfo android.content.pm  PackageManager android.content.pm  #SCREEN_ORIENTATION_SENSOR_LANDSCAPE android.content.pm.ActivityInfo  SCREEN_ORIENTATION_UNSPECIFIED android.content.pm.ActivityInfo  PERMISSION_GRANTED !android.content.pm.PackageManager  canRequestPackageInstalls !android.content.pm.PackageManager  
Configuration android.content.res  	Resources android.content.res  ORIENTATION_LANDSCAPE !android.content.res.Configuration  ORIENTATION_PORTRAIT !android.content.res.Configuration  orientation !android.content.res.Configuration  
configuration android.content.res.Resources  displayMetrics android.content.res.Resources  	getSystem android.content.res.Resources  Bitmap android.graphics  
BitmapFactory android.graphics  Color android.graphics  CompressFormat android.graphics.Bitmap  compress android.graphics.Bitmap  JPEG &android.graphics.Bitmap.CompressFormat  decodeByteArray android.graphics.BitmapFactory  TRANSPARENT android.graphics.Color  
ColorDrawable android.graphics.drawable  ConnectivityManager android.net  Network android.net  NetworkCapabilities android.net  NetworkInfo android.net  NetworkRequest android.net  Uri android.net  NetworkCallback android.net.ConnectivityManager  
activeNetwork android.net.ConnectivityManager  activeNetworkInfo android.net.ConnectivityManager  getNetworkCapabilities android.net.ConnectivityManager  registerNetworkCallback android.net.ConnectivityManager  unregisterNetworkCallback android.net.ConnectivityManager  CancellationException /android.net.ConnectivityManager.NetworkCallback  DownloadStatus /android.net.ConnectivityManager.NetworkCallback  Log /android.net.ConnectivityManager.NetworkCallback  TAG /android.net.ConnectivityManager.NetworkCallback  
downloadItems /android.net.ConnectivityManager.NetworkCallback  downloadJobs /android.net.ConnectivityManager.NetworkCallback  failedDownloads /android.net.ConnectivityManager.NetworkCallback  filter /android.net.ConnectivityManager.NetworkCallback  isNetworkAvailable /android.net.ConnectivityManager.NetworkCallback  
isNotEmpty /android.net.ConnectivityManager.NetworkCallback  launch /android.net.ConnectivityManager.NetworkCallback  let /android.net.ConnectivityManager.NetworkCallback  
retryAttempts /android.net.ConnectivityManager.NetworkCallback  retryFailedDownloads /android.net.ConnectivityManager.NetworkCallback  serviceScope /android.net.ConnectivityManager.NetworkCallback  startOrQueueDownload /android.net.ConnectivityManager.NetworkCallback  updateDownloadStatus /android.net.ConnectivityManager.NetworkCallback  NET_CAPABILITY_INTERNET android.net.NetworkCapabilities  TRANSPORT_CELLULAR android.net.NetworkCapabilities  TRANSPORT_ETHERNET android.net.NetworkCapabilities  TRANSPORT_WIFI android.net.NetworkCapabilities  
hasCapability android.net.NetworkCapabilities  hasTransport android.net.NetworkCapabilities  isConnected android.net.NetworkInfo  Builder android.net.NetworkRequest  
addCapability "android.net.NetworkRequest.Builder  build "android.net.NetworkRequest.Builder  host android.net.Uri  let android.net.Uri  parse android.net.Uri  path android.net.Uri  query android.net.Uri  toString android.net.Uri  Binder 
android.os  Build 
android.os  Bundle 
android.os  Environment 
android.os  Handler 
android.os  IBinder 
android.os  Looper 
android.os  Message 
android.os  
getBoolean android.os.BaseBundle  	getString android.os.BaseBundle  
putBoolean android.os.BaseBundle  SDK_INT android.os.Build.VERSION  M android.os.Build.VERSION_CODES  N android.os.Build.VERSION_CODES  O android.os.Build.VERSION_CODES  P android.os.Build.VERSION_CODES  Q android.os.Build.VERSION_CODES  R android.os.Build.VERSION_CODES  S android.os.Build.VERSION_CODES  TIRAMISU android.os.Build.VERSION_CODES  UPSIDE_DOWN_CAKE android.os.Build.VERSION_CODES  
getBoolean android.os.Bundle  
putBoolean android.os.Bundle  DIRECTORY_DOWNLOADS android.os.Environment  DIRECTORY_PICTURES android.os.Environment  !getExternalStoragePublicDirectory android.os.Environment  isExternalStorageManager android.os.Environment  
obtainMessage android.os.Handler  post android.os.Handler  postDelayed android.os.Handler  
getMainLooper android.os.Looper  data android.os.Message  
MediaStore android.provider  Settings android.provider  DISPLAY_NAME (android.provider.MediaStore.Images.Media  EXTERNAL_CONTENT_URI (android.provider.MediaStore.Images.Media  
IS_PENDING (android.provider.MediaStore.Images.Media  	MIME_TYPE (android.provider.MediaStore.Images.Media  
RELATIVE_PATH (android.provider.MediaStore.Images.Media  DISPLAY_NAME (android.provider.MediaStore.MediaColumns  
IS_PENDING (android.provider.MediaStore.MediaColumns  	MIME_TYPE (android.provider.MediaStore.MediaColumns  
RELATIVE_PATH (android.provider.MediaStore.MediaColumns  #ACTION_APPLICATION_DETAILS_SETTINGS android.provider.Settings  )ACTION_MANAGE_ALL_FILES_ACCESS_PERMISSION android.provider.Settings  -ACTION_MANAGE_APP_ALL_FILES_ACCESS_PERMISSION android.provider.Settings  !ACTION_MANAGE_UNKNOWN_APP_SOURCES android.provider.Settings  ACTION_SETTINGS android.provider.Settings  Editable android.text  SpannableStringBuilder android.text  Base64 android.util  Log android.util  DEFAULT android.util.Base64  decode android.util.Base64  heightPixels android.util.DisplayMetrics  widthPixels android.util.DisplayMetrics  d android.util.Log  e android.util.Log  w android.util.Log  ActivityInfo android.view  	AdBlocker android.view  Base64 android.view  Bitmap android.view  
BitmapFactory android.view  Boolean android.view  Build android.view  Bundle android.view  ByteArrayInputStream android.view  Color android.view  
ColorDrawable android.view  
ContentValues android.view  ContextMenu android.view  
CookieManager android.view  CustomViewCallback android.view  
Deprecated android.view  DownloadService android.view  Environment android.view  	Exception android.view  Fragment android.view  FragmentBrowseBinding android.view  Gravity android.view  Handler android.view  Int android.view  Intent android.view  JsonDownloader android.view  LayoutInflater android.view  Looper android.view  MainActivity android.view  MaterialAlertDialogBuilder android.view  
MediaStore android.view  MenuItem android.view  MotionEvent android.view  OpenLinkValidator android.view  R android.view  
RegexPatterns android.view  	Resources android.view  ShapeableImageView android.view  ShareCompat android.view  Snackbar android.view  SpannableStringBuilder android.view  String android.view  Suppress android.view  SuppressLint android.view  System android.view  Toast android.view  
URLDecoder android.view  URLUtil android.view  Uri android.view  ValidLinkChecker android.view  View android.view  	ViewGroup android.view  WebChromeClient android.view  WebResourceRequest android.view  WebResourceResponse android.view  WebSettings android.view  WebView android.view  
WebViewClient android.view  Window android.view  
WindowManager android.view  any android.view  apply android.view  binding android.view  contains android.view  createStartIntent android.view  getInstance android.view  indexOf android.view  let android.view  listOf android.view  matches android.view  
mutableListOf android.view  replace android.view  requireActivity android.view  requireContext android.view  run android.view  
startsWith android.view  	substring android.view  toByteArray android.view  toRegex android.view  
trimIndent android.view  use android.view  webIcon android.view  ContextMenuInfo android.view.ContextMenu  add android.view.ContextMenu  ActivityDownloadBinding  android.view.ContextThemeWrapper  ActivityFilterUpdatesBinding  android.view.ContextThemeWrapper  ActivityMainBinding  android.view.ContextThemeWrapper  ActivityNotFoundException  android.view.ContextThemeWrapper  ActivityResultContracts  android.view.ContextThemeWrapper  ActivitySettingsBinding  android.view.ContextThemeWrapper  	AdBlocker  android.view.ContextThemeWrapper  AlertDialog  android.view.ContextThemeWrapper  BroadcastReceiver  android.view.ContextThemeWrapper  BrowseFragment  android.view.ContextThemeWrapper  Build  android.view.ContextThemeWrapper  BuildConfig  android.view.ContextThemeWrapper  CONNECTIVITY_SERVICE  android.view.ContextThemeWrapper  
ComponentName  android.view.ContextThemeWrapper  ConnectivityManager  android.view.ContextThemeWrapper  Context  android.view.ContextThemeWrapper  Date  android.view.ContextThemeWrapper  Dispatchers  android.view.ContextThemeWrapper  DownloadActivity  android.view.ContextThemeWrapper  DownloadAdapter  android.view.ContextThemeWrapper  DownloadService  android.view.ContextThemeWrapper  DownloadStatus  android.view.ContextThemeWrapper  EgyFilmTheme  android.view.ContextThemeWrapper  Environment  android.view.ContextThemeWrapper  	Exception  android.view.ContextThemeWrapper  File  android.view.ContextThemeWrapper  FileProvider  android.view.ContextThemeWrapper  FilterUpdatesActivity  android.view.ContextThemeWrapper  FirebaseMessaging  android.view.ContextThemeWrapper  Gson  android.view.ContextThemeWrapper  Handler  android.view.ContextThemeWrapper  HttpURLConnection  android.view.ContextThemeWrapper  IBinder  android.view.ContextThemeWrapper  IOException  android.view.ContextThemeWrapper  IllegalArgumentException  android.view.ContextThemeWrapper  Intent  android.view.ContextThemeWrapper  IntentFilter  android.view.ContextThemeWrapper  LinearLayoutManager  android.view.ContextThemeWrapper  LocalBroadcastManager  android.view.ContextThemeWrapper  Locale  android.view.ContextThemeWrapper  Log  android.view.ContextThemeWrapper  Looper  android.view.ContextThemeWrapper  MODE_PRIVATE  android.view.ContextThemeWrapper  MainActivity  android.view.ContextThemeWrapper  MoreOptionsBottomSheetFragment  android.view.ContextThemeWrapper  NetworkCapabilities  android.view.ContextThemeWrapper  OVERRIDE_TRANSITION_OPEN  android.view.ContextThemeWrapper  OnBackPressedCallback  android.view.ContextThemeWrapper  OpenLinkValidator  android.view.ContextThemeWrapper  PermissionManager  android.view.ContextThemeWrapper  R  android.view.ContextThemeWrapper  ServiceConnection  android.view.ContextThemeWrapper  Settings  android.view.ContextThemeWrapper  SimpleDateFormat  android.view.ContextThemeWrapper  Snackbar  android.view.ContextThemeWrapper  SplashScreen  android.view.ContextThemeWrapper  Suppress  android.view.ContextThemeWrapper  System  android.view.ContextThemeWrapper  Toast  android.view.ContextThemeWrapper  UPDATE_CHECK_URL  android.view.ContextThemeWrapper  URL  android.view.ContextThemeWrapper  UpdateResponse  android.view.ContextThemeWrapper  Uri  android.view.ContextThemeWrapper  ValidLinkChecker  android.view.ContextThemeWrapper  View  android.view.ContextThemeWrapper  WindowCompat  android.view.ContextThemeWrapper  WindowInsetsCompat  android.view.ContextThemeWrapper  WindowInsetsControllerCompat  android.view.ContextThemeWrapper  
WindowManager  android.view.ContextThemeWrapper  	adBlocker  android.view.ContextThemeWrapper  also  android.view.ContextThemeWrapper  android  android.view.ContextThemeWrapper  apply  android.view.ContextThemeWrapper  binding  android.view.ContextThemeWrapper  browseFragment  android.view.ContextThemeWrapper  bufferedReader  android.view.ContextThemeWrapper  clearAdBlockerTimeConstraints  android.view.ContextThemeWrapper  createControlIntent  android.view.ContextThemeWrapper  createStartIntent  android.view.ContextThemeWrapper  delay  android.view.ContextThemeWrapper  downloadAdapter  android.view.ContextThemeWrapper  downloadService  android.view.ContextThemeWrapper  	emptyList  android.view.ContextThemeWrapper  endsWith  android.view.ContextThemeWrapper  getInstance  android.view.ContextThemeWrapper  	getString  android.view.ContextThemeWrapper  getValue  android.view.ContextThemeWrapper  isBound  android.view.ContextThemeWrapper  isEmpty  android.view.ContextThemeWrapper  isFullscreen  android.view.ContextThemeWrapper  
isInitialized  android.view.ContextThemeWrapper  isManualFullscreen  android.view.ContextThemeWrapper  
isNotEmpty  android.view.ContextThemeWrapper  	isVisible  android.view.ContextThemeWrapper  java  android.view.ContextThemeWrapper  launch  android.view.ContextThemeWrapper  let  android.view.ContextThemeWrapper  lifecycleScope  android.view.ContextThemeWrapper  
loadDownloads  android.view.ContextThemeWrapper  loadLastUpdatedTimes  android.view.ContextThemeWrapper  map  android.view.ContextThemeWrapper  maxOf  android.view.ContextThemeWrapper  mutableFloatStateOf  android.view.ContextThemeWrapper  mutableStateOf  android.view.ContextThemeWrapper  onBackPressedDispatcher  android.view.ContextThemeWrapper  openLinkValidator  android.view.ContextThemeWrapper  provideDelegate  android.view.ContextThemeWrapper  readText  android.view.ContextThemeWrapper  saveUpdateTime  android.view.ContextThemeWrapper  
setContent  android.view.ContextThemeWrapper  setValue  android.view.ContextThemeWrapper  split  android.view.ContextThemeWrapper  startService  android.view.ContextThemeWrapper  thread  android.view.ContextThemeWrapper  toIntOrNull  android.view.ContextThemeWrapper  until  android.view.ContextThemeWrapper  use  android.view.ContextThemeWrapper  validLinkChecker  android.view.ContextThemeWrapper  with  android.view.ContextThemeWrapper  withContext  android.view.ContextThemeWrapper  DownloadBinder 0android.view.ContextThemeWrapper.DownloadService  from android.view.LayoutInflater  inflate android.view.LayoutInflater  add android.view.Menu  itemId android.view.MenuItem  title android.view.MenuItem  GONE android.view.View  LAYER_TYPE_HARDWARE android.view.View  OVER_SCROLL_NEVER android.view.View  OnClickListener android.view.View  OnTouchListener android.view.View  VISIBLE android.view.View  contentDescription android.view.View  context android.view.View  findViewById android.view.View  	isEnabled android.view.View  isHorizontalScrollBarEnabled android.view.View  isInEditMode android.view.View  isVerticalScrollBarEnabled android.view.View  layoutParams android.view.View  overScrollMode android.view.View  parent android.view.View  
requestLayout android.view.View  setOnClickListener android.view.View  setOnTouchListener android.view.View  
visibility android.view.View  <SAM-CONSTRUCTOR> !android.view.View.OnClickListener  <SAM-CONSTRUCTOR> !android.view.View.OnTouchListener  addView android.view.ViewGroup  context android.view.ViewGroup  removeAllViews android.view.ViewGroup  height #android.view.ViewGroup.LayoutParams  width #android.view.ViewGroup.LayoutParams  "requestDisallowInterceptTouchEvent android.view.ViewParent  addFlags android.view.Window  
attributes android.view.Window  navigationBarColor android.view.Window  setBackgroundDrawable android.view.Window  statusBarColor android.view.Window  FLAG_KEEP_SCREEN_ON 'android.view.WindowManager.LayoutParams  )LAYOUT_IN_DISPLAY_CUTOUT_MODE_SHORT_EDGES 'android.view.WindowManager.LayoutParams  layoutInDisplayCutoutMode 'android.view.WindowManager.LayoutParams  ActivityInfo android.webkit  	AdBlocker android.webkit  Base64 android.webkit  Bitmap android.webkit  
BitmapFactory android.webkit  Boolean android.webkit  Build android.webkit  Bundle android.webkit  ByteArrayInputStream android.webkit  Color android.webkit  
ColorDrawable android.webkit  
ContentValues android.webkit  ContextMenu android.webkit  
CookieManager android.webkit  CustomViewCallback android.webkit  
Deprecated android.webkit  DownloadListener android.webkit  DownloadService android.webkit  Environment android.webkit  	Exception android.webkit  Fragment android.webkit  FragmentBrowseBinding android.webkit  Handler android.webkit  Int android.webkit  Intent android.webkit  JsonDownloader android.webkit  LayoutInflater android.webkit  Looper android.webkit  MainActivity android.webkit  MaterialAlertDialogBuilder android.webkit  
MediaStore android.webkit  MenuItem android.webkit  MimeTypeMap android.webkit  MotionEvent android.webkit  OpenLinkValidator android.webkit  R android.webkit  
RegexPatterns android.webkit  	Resources android.webkit  ShapeableImageView android.webkit  ShareCompat android.webkit  Snackbar android.webkit  SpannableStringBuilder android.webkit  String android.webkit  Suppress android.webkit  SuppressLint android.webkit  System android.webkit  Toast android.webkit  
URLDecoder android.webkit  URLUtil android.webkit  Uri android.webkit  ValidLinkChecker android.webkit  View android.webkit  	ViewGroup android.webkit  WebBackForwardList android.webkit  WebChromeClient android.webkit  WebResourceRequest android.webkit  WebResourceResponse android.webkit  WebSettings android.webkit  WebView android.webkit  
WebViewClient android.webkit  any android.webkit  apply android.webkit  binding android.webkit  contains android.webkit  createStartIntent android.webkit  getInstance android.webkit  indexOf android.webkit  let android.webkit  listOf android.webkit  matches android.webkit  
mutableListOf android.webkit  replace android.webkit  requireActivity android.webkit  requireContext android.webkit  run android.webkit  
startsWith android.webkit  	substring android.webkit  toByteArray android.webkit  toRegex android.webkit  
trimIndent android.webkit  use android.webkit  webIcon android.webkit  ContextMenuInfo android.webkit.ContextMenu  	getCookie android.webkit.CookieManager  getInstance android.webkit.CookieManager  <SAM-CONSTRUCTOR> android.webkit.DownloadListener  getExtensionFromMimeType android.webkit.MimeTypeMap  getSingleton android.webkit.MimeTypeMap  
guessFileName android.webkit.URLUtil  
isValidUrl android.webkit.URLUtil  OnTouchListener android.webkit.View  ActivityInfo android.webkit.WebChromeClient  CustomViewCallback android.webkit.WebChromeClient  	Exception android.webkit.WebChromeClient  View android.webkit.WebChromeClient  binding android.webkit.WebChromeClient  onProgressChanged android.webkit.WebChromeClient  onReceivedIcon android.webkit.WebChromeClient  requireActivity android.webkit.WebChromeClient  webIcon android.webkit.WebChromeClient  onCustomViewHidden 1android.webkit.WebChromeClient.CustomViewCallback  url !android.webkit.WebResourceRequest  Build android.webkit.WebSettings  LOAD_DEFAULT android.webkit.WebSettings  MIXED_CONTENT_NEVER_ALLOW android.webkit.WebSettings  WebSettings android.webkit.WebSettings  allowFileAccess android.webkit.WebSettings  allowFileAccessFromFileURLs android.webkit.WebSettings   allowUniversalAccessFromFileURLs android.webkit.WebSettings  apply android.webkit.WebSettings  blockNetworkImage android.webkit.WebSettings  builtInZoomControls android.webkit.WebSettings  	cacheMode android.webkit.WebSettings  displayZoomControls android.webkit.WebSettings  domStorageEnabled android.webkit.WebSettings  %javaScriptCanOpenWindowsAutomatically android.webkit.WebSettings  javaScriptEnabled android.webkit.WebSettings  loadWithOverviewMode android.webkit.WebSettings  loadsImagesAutomatically android.webkit.WebSettings   mediaPlaybackRequiresUserGesture android.webkit.WebSettings  mixedContentMode android.webkit.WebSettings  setSupportZoom android.webkit.WebSettings  useWideViewPort android.webkit.WebSettings  userAgentString android.webkit.WebSettings  ActivityInfo android.webkit.WebView  	AdBlocker android.webkit.WebView  Build android.webkit.WebView  ByteArrayInputStream android.webkit.WebView  
CookieManager android.webkit.WebView  DownloadService android.webkit.WebView  
HitTestResult android.webkit.WebView  Intent android.webkit.WebView  JsonDownloader android.webkit.WebView  OpenLinkValidator android.webkit.WebView  
RegexPatterns android.webkit.WebView  SpannableStringBuilder android.webkit.WebView  Toast android.webkit.WebView  
URLDecoder android.webkit.WebView  URLUtil android.webkit.WebView  Uri android.webkit.WebView  ValidLinkChecker android.webkit.WebView  View android.webkit.WebView  WebResourceResponse android.webkit.WebView  WebSettings android.webkit.WebView  any android.webkit.WebView  apply android.webkit.WebView  binding android.webkit.WebView  	canGoBack android.webkit.WebView  canGoForward android.webkit.WebView  clearHistory android.webkit.WebView  contains android.webkit.WebView  context android.webkit.WebView  createStartIntent android.webkit.WebView  evaluateJavascript android.webkit.WebView  getInstance android.webkit.WebView  goBack android.webkit.WebView  	goForward android.webkit.WebView  
hitTestResult android.webkit.WebView  isHorizontalScrollBarEnabled android.webkit.WebView  isVerticalScrollBarEnabled android.webkit.WebView  listOf android.webkit.WebView  loadUrl android.webkit.WebView  matches android.webkit.WebView  
mutableListOf android.webkit.WebView  overScrollMode android.webkit.WebView  reload android.webkit.WebView  replace android.webkit.WebView  requestFocusNodeHref android.webkit.WebView  requireActivity android.webkit.WebView  requireContext android.webkit.WebView  restoreState android.webkit.WebView  	saveState android.webkit.WebView  setDownloadListener android.webkit.WebView  setLayerType android.webkit.WebView  setOnTouchListener android.webkit.WebView  settings android.webkit.WebView  
startsWith android.webkit.WebView  toByteArray android.webkit.WebView  toRegex android.webkit.WebView  
trimIndent android.webkit.WebView  url android.webkit.WebView  
visibility android.webkit.WebView  webChromeClient android.webkit.WebView  webIcon android.webkit.WebView  
webViewClient android.webkit.WebView  zoomOut android.webkit.WebView  ANCHOR_TYPE $android.webkit.WebView.HitTestResult  EDIT_TEXT_TYPE $android.webkit.WebView.HitTestResult  
IMAGE_TYPE $android.webkit.WebView.HitTestResult  SRC_ANCHOR_TYPE $android.webkit.WebView.HitTestResult  UNKNOWN_TYPE $android.webkit.WebView.HitTestResult  type $android.webkit.WebView.HitTestResult  	AdBlocker android.webkit.WebViewClient  ByteArrayInputStream android.webkit.WebViewClient  
CookieManager android.webkit.WebViewClient  DownloadService android.webkit.WebViewClient  	Exception android.webkit.WebViewClient  Intent android.webkit.WebViewClient  JsonDownloader android.webkit.WebViewClient  OpenLinkValidator android.webkit.WebViewClient  
RegexPatterns android.webkit.WebViewClient  SpannableStringBuilder android.webkit.WebViewClient  Toast android.webkit.WebViewClient  
URLDecoder android.webkit.WebViewClient  URLUtil android.webkit.WebViewClient  Uri android.webkit.WebViewClient  ValidLinkChecker android.webkit.WebViewClient  View android.webkit.WebViewClient  WebResourceResponse android.webkit.WebViewClient  any android.webkit.WebViewClient  binding android.webkit.WebViewClient  contains android.webkit.WebViewClient  createStartIntent android.webkit.WebViewClient  doUpdateVisitedHistory android.webkit.WebViewClient  getInstance android.webkit.WebViewClient  listOf android.webkit.WebViewClient  matches android.webkit.WebViewClient  
mutableListOf android.webkit.WebViewClient  onPageFinished android.webkit.WebViewClient  
onPageStarted android.webkit.WebViewClient  requireContext android.webkit.WebViewClient  shouldInterceptRequest android.webkit.WebViewClient  shouldOverrideUrlLoading android.webkit.WebViewClient  
startsWith android.webkit.WebViewClient  toByteArray android.webkit.WebViewClient  toRegex android.webkit.WebViewClient  
trimIndent android.webkit.WebViewClient  FrameLayout android.widget  ImageButton android.widget  	ImageView android.widget  LinearLayout android.widget  ProgressBar android.widget  TextView android.widget  Toast android.widget  addView android.widget.FrameLayout  removeAllViews android.widget.FrameLayout  
visibility android.widget.FrameLayout  contentDescription android.widget.ImageButton  setImageResource android.widget.ImageButton  setOnClickListener android.widget.ImageButton  
visibility android.widget.ImageButton  setImageResource android.widget.ImageView  	isVisible android.widget.LinearLayout  setOnClickListener android.widget.LinearLayout  isIndeterminate android.widget.ProgressBar  progress android.widget.ProgressBar  
visibility android.widget.ProgressBar  text android.widget.TextView  
visibility android.widget.TextView  LENGTH_LONG android.widget.Toast  LENGTH_SHORT android.widget.Toast  makeText android.widget.Toast  show android.widget.Toast  ComponentActivity androidx.activity  OnBackPressedCallback androidx.activity  OnBackPressedDispatcher androidx.activity  ActivityDownloadBinding #androidx.activity.ComponentActivity  ActivityFilterUpdatesBinding #androidx.activity.ComponentActivity  ActivityMainBinding #androidx.activity.ComponentActivity  ActivityNotFoundException #androidx.activity.ComponentActivity  ActivityResultContracts #androidx.activity.ComponentActivity  ActivityResultLauncher #androidx.activity.ComponentActivity  ActivitySettingsBinding #androidx.activity.ComponentActivity  	AdBlocker #androidx.activity.ComponentActivity  AlertDialog #androidx.activity.ComponentActivity  Boolean #androidx.activity.ComponentActivity  BroadcastReceiver #androidx.activity.ComponentActivity  BrowseFragment #androidx.activity.ComponentActivity  Build #androidx.activity.ComponentActivity  BuildConfig #androidx.activity.ComponentActivity  Bundle #androidx.activity.ComponentActivity  CONNECTIVITY_SERVICE #androidx.activity.ComponentActivity  	Companion #androidx.activity.ComponentActivity  
ComponentName #androidx.activity.ComponentActivity  ConnectivityManager #androidx.activity.ComponentActivity  Context #androidx.activity.ComponentActivity  Date #androidx.activity.ComponentActivity  
Deprecated #androidx.activity.ComponentActivity  Dispatchers #androidx.activity.ComponentActivity  DownloadActivity #androidx.activity.ComponentActivity  DownloadAdapter #androidx.activity.ComponentActivity  DownloadItem #androidx.activity.ComponentActivity  DownloadService #androidx.activity.ComponentActivity  DownloadStatus #androidx.activity.ComponentActivity  EgyFilmTheme #androidx.activity.ComponentActivity  Environment #androidx.activity.ComponentActivity  	Exception #androidx.activity.ComponentActivity  File #androidx.activity.ComponentActivity  FileProvider #androidx.activity.ComponentActivity  FilterUpdatesActivity #androidx.activity.ComponentActivity  FirebaseMessaging #androidx.activity.ComponentActivity  Gson #androidx.activity.ComponentActivity  Handler #androidx.activity.ComponentActivity  HttpURLConnection #androidx.activity.ComponentActivity  IBinder #androidx.activity.ComponentActivity  IOException #androidx.activity.ComponentActivity  IllegalArgumentException #androidx.activity.ComponentActivity  Int #androidx.activity.ComponentActivity  Intent #androidx.activity.ComponentActivity  IntentFilter #androidx.activity.ComponentActivity  LinearLayoutManager #androidx.activity.ComponentActivity  List #androidx.activity.ComponentActivity  LocalBroadcastManager #androidx.activity.ComponentActivity  Locale #androidx.activity.ComponentActivity  Log #androidx.activity.ComponentActivity  Long #androidx.activity.ComponentActivity  Looper #androidx.activity.ComponentActivity  MODE_PRIVATE #androidx.activity.ComponentActivity  MainActivity #androidx.activity.ComponentActivity  MenuItem #androidx.activity.ComponentActivity  MoreOptionsBottomSheetFragment #androidx.activity.ComponentActivity  NetworkCapabilities #androidx.activity.ComponentActivity  OVERRIDE_TRANSITION_OPEN #androidx.activity.ComponentActivity  OnBackPressedCallback #androidx.activity.ComponentActivity  OpenLinkValidator #androidx.activity.ComponentActivity  PermissionManager #androidx.activity.ComponentActivity  R #androidx.activity.ComponentActivity  ServiceConnection #androidx.activity.ComponentActivity  Settings #androidx.activity.ComponentActivity  SimpleDateFormat #androidx.activity.ComponentActivity  Snackbar #androidx.activity.ComponentActivity  SplashScreen #androidx.activity.ComponentActivity  String #androidx.activity.ComponentActivity  Suppress #androidx.activity.ComponentActivity  System #androidx.activity.ComponentActivity  Toast #androidx.activity.ComponentActivity  UPDATE_CHECK_URL #androidx.activity.ComponentActivity  URL #androidx.activity.ComponentActivity  Unit #androidx.activity.ComponentActivity  UpdateResponse #androidx.activity.ComponentActivity  Uri #androidx.activity.ComponentActivity  ValidLinkChecker #androidx.activity.ComponentActivity  View #androidx.activity.ComponentActivity  WindowCompat #androidx.activity.ComponentActivity  WindowInsetsCompat #androidx.activity.ComponentActivity  WindowInsetsControllerCompat #androidx.activity.ComponentActivity  
WindowManager #androidx.activity.ComponentActivity  	adBlocker #androidx.activity.ComponentActivity  also #androidx.activity.ComponentActivity  android #androidx.activity.ComponentActivity  apply #androidx.activity.ComponentActivity  binding #androidx.activity.ComponentActivity  browseFragment #androidx.activity.ComponentActivity  bufferedReader #androidx.activity.ComponentActivity  clearAdBlockerTimeConstraints #androidx.activity.ComponentActivity  createControlIntent #androidx.activity.ComponentActivity  createStartIntent #androidx.activity.ComponentActivity  delay #androidx.activity.ComponentActivity  downloadAdapter #androidx.activity.ComponentActivity  downloadService #androidx.activity.ComponentActivity  	emptyList #androidx.activity.ComponentActivity  endsWith #androidx.activity.ComponentActivity  getInstance #androidx.activity.ComponentActivity  	getString #androidx.activity.ComponentActivity  getValue #androidx.activity.ComponentActivity  isBound #androidx.activity.ComponentActivity  isEmpty #androidx.activity.ComponentActivity  isFullscreen #androidx.activity.ComponentActivity  
isInitialized #androidx.activity.ComponentActivity  isManualFullscreen #androidx.activity.ComponentActivity  
isNotEmpty #androidx.activity.ComponentActivity  	isVisible #androidx.activity.ComponentActivity  java #androidx.activity.ComponentActivity  launch #androidx.activity.ComponentActivity  let #androidx.activity.ComponentActivity  lifecycleScope #androidx.activity.ComponentActivity  
loadDownloads #androidx.activity.ComponentActivity  loadLastUpdatedTimes #androidx.activity.ComponentActivity  map #androidx.activity.ComponentActivity  maxOf #androidx.activity.ComponentActivity  mutableFloatStateOf #androidx.activity.ComponentActivity  mutableStateOf #androidx.activity.ComponentActivity  onActivityResult #androidx.activity.ComponentActivity  onBackPressedDispatcher #androidx.activity.ComponentActivity  onCreate #androidx.activity.ComponentActivity  onNewIntent #androidx.activity.ComponentActivity  openLinkValidator #androidx.activity.ComponentActivity  provideDelegate #androidx.activity.ComponentActivity  readText #androidx.activity.ComponentActivity  registerForActivityResult #androidx.activity.ComponentActivity  saveUpdateTime #androidx.activity.ComponentActivity  
setContent #androidx.activity.ComponentActivity  setValue #androidx.activity.ComponentActivity  split #androidx.activity.ComponentActivity  startService #androidx.activity.ComponentActivity  thread #androidx.activity.ComponentActivity  toIntOrNull #androidx.activity.ComponentActivity  until #androidx.activity.ComponentActivity  use #androidx.activity.ComponentActivity  validLinkChecker #androidx.activity.ComponentActivity  with #androidx.activity.ComponentActivity  withContext #androidx.activity.ComponentActivity  ActivityDownloadBinding -androidx.activity.ComponentActivity.Companion  ActivityFilterUpdatesBinding -androidx.activity.ComponentActivity.Companion  ActivityMainBinding -androidx.activity.ComponentActivity.Companion  ActivityResultContracts -androidx.activity.ComponentActivity.Companion  ActivitySettingsBinding -androidx.activity.ComponentActivity.Companion  	AdBlocker -androidx.activity.ComponentActivity.Companion  AlertDialog -androidx.activity.ComponentActivity.Companion  BrowseFragment -androidx.activity.ComponentActivity.Companion  Build -androidx.activity.ComponentActivity.Companion  BuildConfig -androidx.activity.ComponentActivity.Companion  CONNECTIVITY_SERVICE -androidx.activity.ComponentActivity.Companion  Context -androidx.activity.ComponentActivity.Companion  Date -androidx.activity.ComponentActivity.Companion  Dispatchers -androidx.activity.ComponentActivity.Companion  DownloadActivity -androidx.activity.ComponentActivity.Companion  DownloadAdapter -androidx.activity.ComponentActivity.Companion  DownloadService -androidx.activity.ComponentActivity.Companion  DownloadStatus -androidx.activity.ComponentActivity.Companion  EgyFilmTheme -androidx.activity.ComponentActivity.Companion  Environment -androidx.activity.ComponentActivity.Companion  File -androidx.activity.ComponentActivity.Companion  FileProvider -androidx.activity.ComponentActivity.Companion  FilterUpdatesActivity -androidx.activity.ComponentActivity.Companion  FirebaseMessaging -androidx.activity.ComponentActivity.Companion  Gson -androidx.activity.ComponentActivity.Companion  Handler -androidx.activity.ComponentActivity.Companion  HttpURLConnection -androidx.activity.ComponentActivity.Companion  Intent -androidx.activity.ComponentActivity.Companion  IntentFilter -androidx.activity.ComponentActivity.Companion  LinearLayoutManager -androidx.activity.ComponentActivity.Companion  LocalBroadcastManager -androidx.activity.ComponentActivity.Companion  Locale -androidx.activity.ComponentActivity.Companion  Log -androidx.activity.ComponentActivity.Companion  Looper -androidx.activity.ComponentActivity.Companion  MODE_PRIVATE -androidx.activity.ComponentActivity.Companion  MainActivity -androidx.activity.ComponentActivity.Companion  MoreOptionsBottomSheetFragment -androidx.activity.ComponentActivity.Companion  NetworkCapabilities -androidx.activity.ComponentActivity.Companion  OVERRIDE_TRANSITION_OPEN -androidx.activity.ComponentActivity.Companion  OpenLinkValidator -androidx.activity.ComponentActivity.Companion  PermissionManager -androidx.activity.ComponentActivity.Companion  R -androidx.activity.ComponentActivity.Companion  Settings -androidx.activity.ComponentActivity.Companion  SimpleDateFormat -androidx.activity.ComponentActivity.Companion  Snackbar -androidx.activity.ComponentActivity.Companion  SplashScreen -androidx.activity.ComponentActivity.Companion  System -androidx.activity.ComponentActivity.Companion  Toast -androidx.activity.ComponentActivity.Companion  UPDATE_CHECK_URL -androidx.activity.ComponentActivity.Companion  URL -androidx.activity.ComponentActivity.Companion  UpdateResponse -androidx.activity.ComponentActivity.Companion  Uri -androidx.activity.ComponentActivity.Companion  ValidLinkChecker -androidx.activity.ComponentActivity.Companion  View -androidx.activity.ComponentActivity.Companion  WindowCompat -androidx.activity.ComponentActivity.Companion  WindowInsetsCompat -androidx.activity.ComponentActivity.Companion  WindowInsetsControllerCompat -androidx.activity.ComponentActivity.Companion  
WindowManager -androidx.activity.ComponentActivity.Companion  	adBlocker -androidx.activity.ComponentActivity.Companion  also -androidx.activity.ComponentActivity.Companion  android -androidx.activity.ComponentActivity.Companion  apply -androidx.activity.ComponentActivity.Companion  binding -androidx.activity.ComponentActivity.Companion  browseFragment -androidx.activity.ComponentActivity.Companion  bufferedReader -androidx.activity.ComponentActivity.Companion  clearAdBlockerTimeConstraints -androidx.activity.ComponentActivity.Companion  createControlIntent -androidx.activity.ComponentActivity.Companion  createStartIntent -androidx.activity.ComponentActivity.Companion  delay -androidx.activity.ComponentActivity.Companion  downloadAdapter -androidx.activity.ComponentActivity.Companion  downloadService -androidx.activity.ComponentActivity.Companion  	emptyList -androidx.activity.ComponentActivity.Companion  endsWith -androidx.activity.ComponentActivity.Companion  getInstance -androidx.activity.ComponentActivity.Companion  	getString -androidx.activity.ComponentActivity.Companion  getValue -androidx.activity.ComponentActivity.Companion  isBound -androidx.activity.ComponentActivity.Companion  isEmpty -androidx.activity.ComponentActivity.Companion  isFullscreen -androidx.activity.ComponentActivity.Companion  
isInitialized -androidx.activity.ComponentActivity.Companion  isManualFullscreen -androidx.activity.ComponentActivity.Companion  
isNotEmpty -androidx.activity.ComponentActivity.Companion  	isVisible -androidx.activity.ComponentActivity.Companion  java -androidx.activity.ComponentActivity.Companion  launch -androidx.activity.ComponentActivity.Companion  let -androidx.activity.ComponentActivity.Companion  lifecycleScope -androidx.activity.ComponentActivity.Companion  
loadDownloads -androidx.activity.ComponentActivity.Companion  loadLastUpdatedTimes -androidx.activity.ComponentActivity.Companion  map -androidx.activity.ComponentActivity.Companion  maxOf -androidx.activity.ComponentActivity.Companion  mutableFloatStateOf -androidx.activity.ComponentActivity.Companion  mutableStateOf -androidx.activity.ComponentActivity.Companion  onBackPressedDispatcher -androidx.activity.ComponentActivity.Companion  openLinkValidator -androidx.activity.ComponentActivity.Companion  provideDelegate -androidx.activity.ComponentActivity.Companion  readText -androidx.activity.ComponentActivity.Companion  saveUpdateTime -androidx.activity.ComponentActivity.Companion  
setContent -androidx.activity.ComponentActivity.Companion  setValue -androidx.activity.ComponentActivity.Companion  split -androidx.activity.ComponentActivity.Companion  startService -androidx.activity.ComponentActivity.Companion  thread -androidx.activity.ComponentActivity.Companion  toIntOrNull -androidx.activity.ComponentActivity.Companion  until -androidx.activity.ComponentActivity.Companion  use -androidx.activity.ComponentActivity.Companion  validLinkChecker -androidx.activity.ComponentActivity.Companion  with -androidx.activity.ComponentActivity.Companion  withContext -androidx.activity.ComponentActivity.Companion  DownloadBinder 3androidx.activity.ComponentActivity.DownloadService  content +androidx.activity.ComponentActivity.android  res 3androidx.activity.ComponentActivity.android.content  
Configuration 7androidx.activity.ComponentActivity.android.content.res  	isEnabled 'androidx.activity.OnBackPressedCallback  addCallback )androidx.activity.OnBackPressedDispatcher  
onBackPressed )androidx.activity.OnBackPressedDispatcher  
setContent androidx.activity.compose  ActivityResult androidx.activity.result  ActivityResultCallback androidx.activity.result  ActivityResultLauncher androidx.activity.result  <SAM-CONSTRUCTOR> /androidx.activity.result.ActivityResultCallback  launch /androidx.activity.result.ActivityResultLauncher  ActivityResultContracts !androidx.activity.result.contract  RequestMultiplePermissions 9androidx.activity.result.contract.ActivityResultContracts  RequestPermission 9androidx.activity.result.contract.ActivityResultContracts  StartActivityForResult 9androidx.activity.result.contract.ActivityResultContracts  	ActionBar androidx.appcompat.app  AlertDialog androidx.appcompat.app  AppCompatActivity androidx.appcompat.app  setDisplayHomeAsUpEnabled  androidx.appcompat.app.ActionBar  title  androidx.appcompat.app.ActionBar  Builder "androidx.appcompat.app.AlertDialog  show "androidx.appcompat.app.AlertDialog  window "androidx.appcompat.app.AlertDialog  
setCancelable *androidx.appcompat.app.AlertDialog.Builder  
setMessage *androidx.appcompat.app.AlertDialog.Builder  setNegativeButton *androidx.appcompat.app.AlertDialog.Builder  setPositiveButton *androidx.appcompat.app.AlertDialog.Builder  setTitle *androidx.appcompat.app.AlertDialog.Builder  setView *androidx.appcompat.app.AlertDialog.Builder  show *androidx.appcompat.app.AlertDialog.Builder  ActivityDownloadBinding (androidx.appcompat.app.AppCompatActivity  ActivityFilterUpdatesBinding (androidx.appcompat.app.AppCompatActivity  ActivityMainBinding (androidx.appcompat.app.AppCompatActivity  ActivityNotFoundException (androidx.appcompat.app.AppCompatActivity  ActivitySettingsBinding (androidx.appcompat.app.AppCompatActivity  	AdBlocker (androidx.appcompat.app.AppCompatActivity  AlertDialog (androidx.appcompat.app.AppCompatActivity  BroadcastReceiver (androidx.appcompat.app.AppCompatActivity  BrowseFragment (androidx.appcompat.app.AppCompatActivity  Build (androidx.appcompat.app.AppCompatActivity  
ComponentName (androidx.appcompat.app.AppCompatActivity  Context (androidx.appcompat.app.AppCompatActivity  Date (androidx.appcompat.app.AppCompatActivity  Dispatchers (androidx.appcompat.app.AppCompatActivity  DownloadActivity (androidx.appcompat.app.AppCompatActivity  DownloadAdapter (androidx.appcompat.app.AppCompatActivity  DownloadService (androidx.appcompat.app.AppCompatActivity  DownloadStatus (androidx.appcompat.app.AppCompatActivity  Environment (androidx.appcompat.app.AppCompatActivity  	Exception (androidx.appcompat.app.AppCompatActivity  File (androidx.appcompat.app.AppCompatActivity  FileProvider (androidx.appcompat.app.AppCompatActivity  FilterUpdatesActivity (androidx.appcompat.app.AppCompatActivity  FirebaseMessaging (androidx.appcompat.app.AppCompatActivity  IBinder (androidx.appcompat.app.AppCompatActivity  IllegalArgumentException (androidx.appcompat.app.AppCompatActivity  Intent (androidx.appcompat.app.AppCompatActivity  IntentFilter (androidx.appcompat.app.AppCompatActivity  LinearLayoutManager (androidx.appcompat.app.AppCompatActivity  LocalBroadcastManager (androidx.appcompat.app.AppCompatActivity  Locale (androidx.appcompat.app.AppCompatActivity  Log (androidx.appcompat.app.AppCompatActivity  MODE_PRIVATE (androidx.appcompat.app.AppCompatActivity  MoreOptionsBottomSheetFragment (androidx.appcompat.app.AppCompatActivity  OnBackPressedCallback (androidx.appcompat.app.AppCompatActivity  OpenLinkValidator (androidx.appcompat.app.AppCompatActivity  PermissionManager (androidx.appcompat.app.AppCompatActivity  R (androidx.appcompat.app.AppCompatActivity  ServiceConnection (androidx.appcompat.app.AppCompatActivity  SimpleDateFormat (androidx.appcompat.app.AppCompatActivity  Snackbar (androidx.appcompat.app.AppCompatActivity  System (androidx.appcompat.app.AppCompatActivity  Toast (androidx.appcompat.app.AppCompatActivity  Uri (androidx.appcompat.app.AppCompatActivity  ValidLinkChecker (androidx.appcompat.app.AppCompatActivity  View (androidx.appcompat.app.AppCompatActivity  WindowCompat (androidx.appcompat.app.AppCompatActivity  WindowInsetsCompat (androidx.appcompat.app.AppCompatActivity  WindowInsetsControllerCompat (androidx.appcompat.app.AppCompatActivity  
WindowManager (androidx.appcompat.app.AppCompatActivity  	adBlocker (androidx.appcompat.app.AppCompatActivity  also (androidx.appcompat.app.AppCompatActivity  android (androidx.appcompat.app.AppCompatActivity  apply (androidx.appcompat.app.AppCompatActivity  binding (androidx.appcompat.app.AppCompatActivity  browseFragment (androidx.appcompat.app.AppCompatActivity  clearAdBlockerTimeConstraints (androidx.appcompat.app.AppCompatActivity  createControlIntent (androidx.appcompat.app.AppCompatActivity  createStartIntent (androidx.appcompat.app.AppCompatActivity  delay (androidx.appcompat.app.AppCompatActivity  downloadAdapter (androidx.appcompat.app.AppCompatActivity  downloadService (androidx.appcompat.app.AppCompatActivity  	emptyList (androidx.appcompat.app.AppCompatActivity  endsWith (androidx.appcompat.app.AppCompatActivity  getInstance (androidx.appcompat.app.AppCompatActivity  	getString (androidx.appcompat.app.AppCompatActivity  isBound (androidx.appcompat.app.AppCompatActivity  isEmpty (androidx.appcompat.app.AppCompatActivity  isFullscreen (androidx.appcompat.app.AppCompatActivity  
isInitialized (androidx.appcompat.app.AppCompatActivity  isManualFullscreen (androidx.appcompat.app.AppCompatActivity  
isNotEmpty (androidx.appcompat.app.AppCompatActivity  	isVisible (androidx.appcompat.app.AppCompatActivity  java (androidx.appcompat.app.AppCompatActivity  launch (androidx.appcompat.app.AppCompatActivity  let (androidx.appcompat.app.AppCompatActivity  lifecycleScope (androidx.appcompat.app.AppCompatActivity  
loadDownloads (androidx.appcompat.app.AppCompatActivity  loadLastUpdatedTimes (androidx.appcompat.app.AppCompatActivity  maxOf (androidx.appcompat.app.AppCompatActivity  onBackPressedDispatcher (androidx.appcompat.app.AppCompatActivity  onConfigurationChanged (androidx.appcompat.app.AppCompatActivity  onCreate (androidx.appcompat.app.AppCompatActivity  onNewIntent (androidx.appcompat.app.AppCompatActivity  onOptionsItemSelected (androidx.appcompat.app.AppCompatActivity  onPause (androidx.appcompat.app.AppCompatActivity  onResume (androidx.appcompat.app.AppCompatActivity  onStart (androidx.appcompat.app.AppCompatActivity  onStop (androidx.appcompat.app.AppCompatActivity  openLinkValidator (androidx.appcompat.app.AppCompatActivity  packageManager (androidx.appcompat.app.AppCompatActivity  packageName (androidx.appcompat.app.AppCompatActivity  registerForActivityResult (androidx.appcompat.app.AppCompatActivity  	resources (androidx.appcompat.app.AppCompatActivity  saveUpdateTime (androidx.appcompat.app.AppCompatActivity  setContentView (androidx.appcompat.app.AppCompatActivity  setSupportActionBar (androidx.appcompat.app.AppCompatActivity  
startActivity (androidx.appcompat.app.AppCompatActivity  startService (androidx.appcompat.app.AppCompatActivity  supportActionBar (androidx.appcompat.app.AppCompatActivity  validLinkChecker (androidx.appcompat.app.AppCompatActivity  with (androidx.appcompat.app.AppCompatActivity  withContext (androidx.appcompat.app.AppCompatActivity  DownloadBinder 8androidx.appcompat.app.AppCompatActivity.DownloadService  DownloadActivity .androidx.appcompat.app.AppCompatDialogFragment  Intent .androidx.appcompat.app.AppCompatDialogFragment  MainActivity .androidx.appcompat.app.AppCompatDialogFragment  MaterialButton .androidx.appcompat.app.AppCompatDialogFragment  R .androidx.appcompat.app.AppCompatDialogFragment  SettingsActivity .androidx.appcompat.app.AppCompatDialogFragment  java .androidx.appcompat.app.AppCompatDialogFragment  Toolbar androidx.appcompat.widget  text +androidx.appcompat.widget.AppCompatEditText  setImageBitmap ,androidx.appcompat.widget.AppCompatImageView  CardView androidx.cardview.widget  setOnClickListener !androidx.cardview.widget.CardView  
Animatable androidx.compose.animation.core  AnimationResult androidx.compose.animation.core  AnimationVector1D androidx.compose.animation.core  Easing androidx.compose.animation.core  FastOutSlowInEasing androidx.compose.animation.core  	TweenSpec androidx.compose.animation.core  tween androidx.compose.animation.core  	animateTo *androidx.compose.animation.core.Animatable  value *androidx.compose.animation.core.Animatable  Image androidx.compose.foundation  
background androidx.compose.foundation  isSystemInDarkTheme androidx.compose.foundation  Arrangement "androidx.compose.foundation.layout  Box "androidx.compose.foundation.layout  BoxScope "androidx.compose.foundation.layout  Column "androidx.compose.foundation.layout  ColumnScope "androidx.compose.foundation.layout  RowScope "androidx.compose.foundation.layout  Spacer "androidx.compose.foundation.layout  fillMaxSize "androidx.compose.foundation.layout  height "androidx.compose.foundation.layout  padding "androidx.compose.foundation.layout  size "androidx.compose.foundation.layout  width "androidx.compose.foundation.layout  Center .androidx.compose.foundation.layout.Arrangement  HorizontalOrVertical .androidx.compose.foundation.layout.Arrangement  	Alignment +androidx.compose.foundation.layout.BoxScope  Arrangement +androidx.compose.foundation.layout.BoxScope  Button +androidx.compose.foundation.layout.BoxScope  ButtonDefaults +androidx.compose.foundation.layout.BoxScope  CircularProgressIndicator +androidx.compose.foundation.layout.BoxScope  Color +androidx.compose.foundation.layout.BoxScope  Column +androidx.compose.foundation.layout.BoxScope  
EgyFilmRed +androidx.compose.foundation.layout.BoxScope  EgyFilmRedTransparent +androidx.compose.foundation.layout.BoxScope  
FontWeight +androidx.compose.foundation.layout.BoxScope  Image +androidx.compose.foundation.layout.BoxScope  LinearProgressIndicator +androidx.compose.foundation.layout.BoxScope  Modifier +androidx.compose.foundation.layout.BoxScope  R +androidx.compose.foundation.layout.BoxScope  RoundedCornerShape +androidx.compose.foundation.layout.BoxScope  Spacer +androidx.compose.foundation.layout.BoxScope  Text +androidx.compose.foundation.layout.BoxScope  	TextAlign +androidx.compose.foundation.layout.BoxScope  alpha +androidx.compose.foundation.layout.BoxScope  buttonColors +androidx.compose.foundation.layout.BoxScope  dp +androidx.compose.foundation.layout.BoxScope  height +androidx.compose.foundation.layout.BoxScope  padding +androidx.compose.foundation.layout.BoxScope  painterResource +androidx.compose.foundation.layout.BoxScope  scale +androidx.compose.foundation.layout.BoxScope  size +androidx.compose.foundation.layout.BoxScope  sp +androidx.compose.foundation.layout.BoxScope  width +androidx.compose.foundation.layout.BoxScope  	Alignment .androidx.compose.foundation.layout.ColumnScope  Button .androidx.compose.foundation.layout.ColumnScope  ButtonDefaults .androidx.compose.foundation.layout.ColumnScope  CircularProgressIndicator .androidx.compose.foundation.layout.ColumnScope  Color .androidx.compose.foundation.layout.ColumnScope  Column .androidx.compose.foundation.layout.ColumnScope  
EgyFilmRed .androidx.compose.foundation.layout.ColumnScope  EgyFilmRedTransparent .androidx.compose.foundation.layout.ColumnScope  
FontWeight .androidx.compose.foundation.layout.ColumnScope  Image .androidx.compose.foundation.layout.ColumnScope  LinearProgressIndicator .androidx.compose.foundation.layout.ColumnScope  
MaterialTheme .androidx.compose.foundation.layout.ColumnScope  Modifier .androidx.compose.foundation.layout.ColumnScope  R .androidx.compose.foundation.layout.ColumnScope  RoundedCornerShape .androidx.compose.foundation.layout.ColumnScope  Spacer .androidx.compose.foundation.layout.ColumnScope  Text .androidx.compose.foundation.layout.ColumnScope  	TextAlign .androidx.compose.foundation.layout.ColumnScope  alpha .androidx.compose.foundation.layout.ColumnScope  buttonColors .androidx.compose.foundation.layout.ColumnScope  dp .androidx.compose.foundation.layout.ColumnScope  height .androidx.compose.foundation.layout.ColumnScope  padding .androidx.compose.foundation.layout.ColumnScope  painterResource .androidx.compose.foundation.layout.ColumnScope  scale .androidx.compose.foundation.layout.ColumnScope  size .androidx.compose.foundation.layout.ColumnScope  sp .androidx.compose.foundation.layout.ColumnScope  width .androidx.compose.foundation.layout.ColumnScope  Color +androidx.compose.foundation.layout.RowScope  
FontWeight +androidx.compose.foundation.layout.RowScope  Text +androidx.compose.foundation.layout.RowScope  RoundedCornerShape !androidx.compose.foundation.shape  Button androidx.compose.material3  ButtonColors androidx.compose.material3  ButtonDefaults androidx.compose.material3  Card androidx.compose.material3  
CardColors androidx.compose.material3  CardDefaults androidx.compose.material3  CircularProgressIndicator androidx.compose.material3  ColorScheme androidx.compose.material3  LinearProgressIndicator androidx.compose.material3  
MaterialTheme androidx.compose.material3  Text androidx.compose.material3  
Typography androidx.compose.material3  darkColorScheme androidx.compose.material3  dynamicDarkColorScheme androidx.compose.material3  dynamicLightColorScheme androidx.compose.material3  lightColorScheme androidx.compose.material3  buttonColors )androidx.compose.material3.ButtonDefaults  
cardColors 'androidx.compose.material3.CardDefaults  
background &androidx.compose.material3.ColorScheme  	onSurface &androidx.compose.material3.ColorScheme  onSurfaceVariant &androidx.compose.material3.ColorScheme  surface &androidx.compose.material3.ColorScheme  colorScheme (androidx.compose.material3.MaterialTheme  
typography (androidx.compose.material3.MaterialTheme  
bodyMedium %androidx.compose.material3.Typography  titleMedium %androidx.compose.material3.Typography  
Composable androidx.compose.runtime  LaunchedEffect androidx.compose.runtime  MutableFloatState androidx.compose.runtime  MutableState androidx.compose.runtime  ProvidableCompositionLocal androidx.compose.runtime  
SideEffect androidx.compose.runtime  getValue androidx.compose.runtime  mutableFloatStateOf androidx.compose.runtime  mutableStateOf androidx.compose.runtime  remember androidx.compose.runtime  setValue androidx.compose.runtime  
getCurrent )androidx.compose.runtime.CompositionLocal  getValue *androidx.compose.runtime.MutableFloatState  provideDelegate *androidx.compose.runtime.MutableFloatState  setValue *androidx.compose.runtime.MutableFloatState  getValue %androidx.compose.runtime.MutableState  provideDelegate %androidx.compose.runtime.MutableState  setValue %androidx.compose.runtime.MutableState  current 3androidx.compose.runtime.ProvidableCompositionLocal  ComposableFunction0 !androidx.compose.runtime.internal  ComposableFunction1 !androidx.compose.runtime.internal  	Alignment androidx.compose.ui  Modifier androidx.compose.ui  Center androidx.compose.ui.Alignment  CenterHorizontally androidx.compose.ui.Alignment  	Companion androidx.compose.ui.Alignment  
Horizontal androidx.compose.ui.Alignment  Center 'androidx.compose.ui.Alignment.Companion  CenterHorizontally 'androidx.compose.ui.Alignment.Companion  	Companion androidx.compose.ui.Modifier  alpha androidx.compose.ui.Modifier  
background androidx.compose.ui.Modifier  fillMaxSize androidx.compose.ui.Modifier  height androidx.compose.ui.Modifier  padding androidx.compose.ui.Modifier  scale androidx.compose.ui.Modifier  size androidx.compose.ui.Modifier  width androidx.compose.ui.Modifier  fillMaxSize &androidx.compose.ui.Modifier.Companion  height &androidx.compose.ui.Modifier.Companion  padding &androidx.compose.ui.Modifier.Companion  size &androidx.compose.ui.Modifier.Companion  width &androidx.compose.ui.Modifier.Companion  alpha androidx.compose.ui.draw  scale androidx.compose.ui.draw  Color androidx.compose.ui.graphics  toArgb androidx.compose.ui.graphics  Black "androidx.compose.ui.graphics.Color  	Companion "androidx.compose.ui.graphics.Color  White "androidx.compose.ui.graphics.Color  toArgb "androidx.compose.ui.graphics.Color  Black ,androidx.compose.ui.graphics.Color.Companion  White ,androidx.compose.ui.graphics.Color.Companion  Painter $androidx.compose.ui.graphics.painter  LocalContext androidx.compose.ui.platform  	LocalView androidx.compose.ui.platform  painterResource androidx.compose.ui.res  	TextStyle androidx.compose.ui.text  
FontFamily androidx.compose.ui.text.font  
FontWeight androidx.compose.ui.text.font  SystemFontFamily androidx.compose.ui.text.font  	Companion (androidx.compose.ui.text.font.FontFamily  Default (androidx.compose.ui.text.font.FontFamily  Default 2androidx.compose.ui.text.font.FontFamily.Companion  Bold (androidx.compose.ui.text.font.FontWeight  	Companion (androidx.compose.ui.text.font.FontWeight  Medium (androidx.compose.ui.text.font.FontWeight  Normal (androidx.compose.ui.text.font.FontWeight  SemiBold (androidx.compose.ui.text.font.FontWeight  Bold 2androidx.compose.ui.text.font.FontWeight.Companion  Medium 2androidx.compose.ui.text.font.FontWeight.Companion  Normal 2androidx.compose.ui.text.font.FontWeight.Companion  SemiBold 2androidx.compose.ui.text.font.FontWeight.Companion  	TextAlign androidx.compose.ui.text.style  Center (androidx.compose.ui.text.style.TextAlign  	Companion (androidx.compose.ui.text.style.TextAlign  Center 2androidx.compose.ui.text.style.TextAlign.Companion  Dp androidx.compose.ui.unit  TextUnit androidx.compose.ui.unit  dp androidx.compose.ui.unit  getDp androidx.compose.ui.unit  sp androidx.compose.ui.unit  Dialog androidx.compose.ui.window  MotionLayout 'androidx.constraintlayout.motion.widget  onTouchEvent 4androidx.constraintlayout.motion.widget.MotionLayout  progress 4androidx.constraintlayout.motion.widget.MotionLayout  transitionToEnd 4androidx.constraintlayout.motion.widget.MotionLayout  transitionToStart 4androidx.constraintlayout.motion.widget.MotionLayout  CoordinatorLayout !androidx.coordinatorlayout.widget  ActivityCompat androidx.core.app  NotificationCompat androidx.core.app  ShareCompat androidx.core.app  $shouldShowRequestPermissionRationale  androidx.core.app.ActivityCompat  ActivityDownloadBinding #androidx.core.app.ComponentActivity  ActivityFilterUpdatesBinding #androidx.core.app.ComponentActivity  ActivityMainBinding #androidx.core.app.ComponentActivity  ActivityNotFoundException #androidx.core.app.ComponentActivity  ActivityResultContracts #androidx.core.app.ComponentActivity  ActivityResultLauncher #androidx.core.app.ComponentActivity  ActivitySettingsBinding #androidx.core.app.ComponentActivity  	AdBlocker #androidx.core.app.ComponentActivity  AlertDialog #androidx.core.app.ComponentActivity  Boolean #androidx.core.app.ComponentActivity  BroadcastReceiver #androidx.core.app.ComponentActivity  BrowseFragment #androidx.core.app.ComponentActivity  Build #androidx.core.app.ComponentActivity  BuildConfig #androidx.core.app.ComponentActivity  Bundle #androidx.core.app.ComponentActivity  CONNECTIVITY_SERVICE #androidx.core.app.ComponentActivity  
ComponentName #androidx.core.app.ComponentActivity  ConnectivityManager #androidx.core.app.ComponentActivity  Context #androidx.core.app.ComponentActivity  Date #androidx.core.app.ComponentActivity  
Deprecated #androidx.core.app.ComponentActivity  Dispatchers #androidx.core.app.ComponentActivity  DownloadActivity #androidx.core.app.ComponentActivity  DownloadAdapter #androidx.core.app.ComponentActivity  DownloadItem #androidx.core.app.ComponentActivity  DownloadService #androidx.core.app.ComponentActivity  DownloadStatus #androidx.core.app.ComponentActivity  EgyFilmTheme #androidx.core.app.ComponentActivity  Environment #androidx.core.app.ComponentActivity  	Exception #androidx.core.app.ComponentActivity  File #androidx.core.app.ComponentActivity  FileProvider #androidx.core.app.ComponentActivity  FilterUpdatesActivity #androidx.core.app.ComponentActivity  FirebaseMessaging #androidx.core.app.ComponentActivity  Gson #androidx.core.app.ComponentActivity  Handler #androidx.core.app.ComponentActivity  HttpURLConnection #androidx.core.app.ComponentActivity  IBinder #androidx.core.app.ComponentActivity  IOException #androidx.core.app.ComponentActivity  IllegalArgumentException #androidx.core.app.ComponentActivity  Int #androidx.core.app.ComponentActivity  Intent #androidx.core.app.ComponentActivity  IntentFilter #androidx.core.app.ComponentActivity  LinearLayoutManager #androidx.core.app.ComponentActivity  List #androidx.core.app.ComponentActivity  LocalBroadcastManager #androidx.core.app.ComponentActivity  Locale #androidx.core.app.ComponentActivity  Log #androidx.core.app.ComponentActivity  Long #androidx.core.app.ComponentActivity  Looper #androidx.core.app.ComponentActivity  MODE_PRIVATE #androidx.core.app.ComponentActivity  MainActivity #androidx.core.app.ComponentActivity  MenuItem #androidx.core.app.ComponentActivity  MoreOptionsBottomSheetFragment #androidx.core.app.ComponentActivity  NetworkCapabilities #androidx.core.app.ComponentActivity  OVERRIDE_TRANSITION_OPEN #androidx.core.app.ComponentActivity  OnBackPressedCallback #androidx.core.app.ComponentActivity  OpenLinkValidator #androidx.core.app.ComponentActivity  PermissionManager #androidx.core.app.ComponentActivity  R #androidx.core.app.ComponentActivity  ServiceConnection #androidx.core.app.ComponentActivity  Settings #androidx.core.app.ComponentActivity  SimpleDateFormat #androidx.core.app.ComponentActivity  Snackbar #androidx.core.app.ComponentActivity  SplashScreen #androidx.core.app.ComponentActivity  String #androidx.core.app.ComponentActivity  Suppress #androidx.core.app.ComponentActivity  System #androidx.core.app.ComponentActivity  Toast #androidx.core.app.ComponentActivity  UPDATE_CHECK_URL #androidx.core.app.ComponentActivity  URL #androidx.core.app.ComponentActivity  Unit #androidx.core.app.ComponentActivity  UpdateResponse #androidx.core.app.ComponentActivity  Uri #androidx.core.app.ComponentActivity  ValidLinkChecker #androidx.core.app.ComponentActivity  View #androidx.core.app.ComponentActivity  WindowCompat #androidx.core.app.ComponentActivity  WindowInsetsCompat #androidx.core.app.ComponentActivity  WindowInsetsControllerCompat #androidx.core.app.ComponentActivity  
WindowManager #androidx.core.app.ComponentActivity  	adBlocker #androidx.core.app.ComponentActivity  also #androidx.core.app.ComponentActivity  android #androidx.core.app.ComponentActivity  apply #androidx.core.app.ComponentActivity  binding #androidx.core.app.ComponentActivity  browseFragment #androidx.core.app.ComponentActivity  bufferedReader #androidx.core.app.ComponentActivity  clearAdBlockerTimeConstraints #androidx.core.app.ComponentActivity  createControlIntent #androidx.core.app.ComponentActivity  createStartIntent #androidx.core.app.ComponentActivity  delay #androidx.core.app.ComponentActivity  downloadAdapter #androidx.core.app.ComponentActivity  downloadService #androidx.core.app.ComponentActivity  	emptyList #androidx.core.app.ComponentActivity  endsWith #androidx.core.app.ComponentActivity  getInstance #androidx.core.app.ComponentActivity  	getString #androidx.core.app.ComponentActivity  getValue #androidx.core.app.ComponentActivity  isBound #androidx.core.app.ComponentActivity  isEmpty #androidx.core.app.ComponentActivity  isFullscreen #androidx.core.app.ComponentActivity  
isInitialized #androidx.core.app.ComponentActivity  isManualFullscreen #androidx.core.app.ComponentActivity  
isNotEmpty #androidx.core.app.ComponentActivity  	isVisible #androidx.core.app.ComponentActivity  java #androidx.core.app.ComponentActivity  launch #androidx.core.app.ComponentActivity  let #androidx.core.app.ComponentActivity  lifecycleScope #androidx.core.app.ComponentActivity  
loadDownloads #androidx.core.app.ComponentActivity  loadLastUpdatedTimes #androidx.core.app.ComponentActivity  map #androidx.core.app.ComponentActivity  maxOf #androidx.core.app.ComponentActivity  mutableFloatStateOf #androidx.core.app.ComponentActivity  mutableStateOf #androidx.core.app.ComponentActivity  onBackPressedDispatcher #androidx.core.app.ComponentActivity  openLinkValidator #androidx.core.app.ComponentActivity  provideDelegate #androidx.core.app.ComponentActivity  readText #androidx.core.app.ComponentActivity  saveUpdateTime #androidx.core.app.ComponentActivity  
setContent #androidx.core.app.ComponentActivity  setValue #androidx.core.app.ComponentActivity  split #androidx.core.app.ComponentActivity  startService #androidx.core.app.ComponentActivity  thread #androidx.core.app.ComponentActivity  toIntOrNull #androidx.core.app.ComponentActivity  until #androidx.core.app.ComponentActivity  use #androidx.core.app.ComponentActivity  validLinkChecker #androidx.core.app.ComponentActivity  with #androidx.core.app.ComponentActivity  withContext #androidx.core.app.ComponentActivity  DownloadBinder 3androidx.core.app.ComponentActivity.DownloadService  content +androidx.core.app.ComponentActivity.android  res 3androidx.core.app.ComponentActivity.android.content  
Configuration 7androidx.core.app.ComponentActivity.android.content.res  BigTextStyle $androidx.core.app.NotificationCompat  Builder $androidx.core.app.NotificationCompat  DEFAULT_ALL $androidx.core.app.NotificationCompat  PRIORITY_DEFAULT $androidx.core.app.NotificationCompat  
PRIORITY_HIGH $androidx.core.app.NotificationCompat  PRIORITY_LOW $androidx.core.app.NotificationCompat  bigText 1androidx.core.app.NotificationCompat.BigTextStyle  	addAction ,androidx.core.app.NotificationCompat.Builder  build ,androidx.core.app.NotificationCompat.Builder  
setAutoCancel ,androidx.core.app.NotificationCompat.Builder  setContentIntent ,androidx.core.app.NotificationCompat.Builder  setContentText ,androidx.core.app.NotificationCompat.Builder  setContentTitle ,androidx.core.app.NotificationCompat.Builder  setDefaults ,androidx.core.app.NotificationCompat.Builder  
setOngoing ,androidx.core.app.NotificationCompat.Builder  setOnlyAlertOnce ,androidx.core.app.NotificationCompat.Builder  setPriority ,androidx.core.app.NotificationCompat.Builder  setProgress ,androidx.core.app.NotificationCompat.Builder  setSmallIcon ,androidx.core.app.NotificationCompat.Builder  setStyle ,androidx.core.app.NotificationCompat.Builder  
IntentBuilder androidx.core.app.ShareCompat  setChooserTitle +androidx.core.app.ShareCompat.IntentBuilder  	setStream +androidx.core.app.ShareCompat.IntentBuilder  setText +androidx.core.app.ShareCompat.IntentBuilder  setType +androidx.core.app.ShareCompat.IntentBuilder  startChooser +androidx.core.app.ShareCompat.IntentBuilder  
ContextCompat androidx.core.content  FileProvider androidx.core.content  checkSelfPermission #androidx.core.content.ContextCompat  
getUriForFile "androidx.core.content.FileProvider  WindowCompat androidx.core.view  WindowInsetsCompat androidx.core.view  WindowInsetsControllerCompat androidx.core.view  	isVisible androidx.core.view  
setVisible androidx.core.view  getInsetsController androidx.core.view.WindowCompat  setDecorFitsSystemWindows androidx.core.view.WindowCompat  
systemBars *androidx.core.view.WindowInsetsCompat.Type  %BEHAVIOR_SHOW_TRANSIENT_BARS_BY_SWIPE /androidx.core.view.WindowInsetsControllerCompat  hide /androidx.core.view.WindowInsetsControllerCompat  isAppearanceLightNavigationBars /androidx.core.view.WindowInsetsControllerCompat  isAppearanceLightStatusBars /androidx.core.view.WindowInsetsControllerCompat  let /androidx.core.view.WindowInsetsControllerCompat  show /androidx.core.view.WindowInsetsControllerCompat  systemBarsBehavior /androidx.core.view.WindowInsetsControllerCompat  Fragment androidx.fragment.app  FragmentActivity androidx.fragment.app  FragmentManager androidx.fragment.app  FragmentTransaction androidx.fragment.app  DownloadActivity $androidx.fragment.app.DialogFragment  Intent $androidx.fragment.app.DialogFragment  MainActivity $androidx.fragment.app.DialogFragment  MaterialButton $androidx.fragment.app.DialogFragment  R $androidx.fragment.app.DialogFragment  SettingsActivity $androidx.fragment.app.DialogFragment  java $androidx.fragment.app.DialogFragment  show $androidx.fragment.app.DialogFragment  ActivityInfo androidx.fragment.app.Fragment  	AdBlocker androidx.fragment.app.Fragment  Base64 androidx.fragment.app.Fragment  Bitmap androidx.fragment.app.Fragment  
BitmapFactory androidx.fragment.app.Fragment  Boolean androidx.fragment.app.Fragment  Build androidx.fragment.app.Fragment  ByteArrayInputStream androidx.fragment.app.Fragment  Color androidx.fragment.app.Fragment  
ColorDrawable androidx.fragment.app.Fragment  
ContentValues androidx.fragment.app.Fragment  
CookieManager androidx.fragment.app.Fragment  CustomViewCallback androidx.fragment.app.Fragment  
Deprecated androidx.fragment.app.Fragment  DownloadActivity androidx.fragment.app.Fragment  DownloadService androidx.fragment.app.Fragment  Environment androidx.fragment.app.Fragment  	Exception androidx.fragment.app.Fragment  FragmentBrowseBinding androidx.fragment.app.Fragment  Handler androidx.fragment.app.Fragment  Int androidx.fragment.app.Fragment  Intent androidx.fragment.app.Fragment  JsonDownloader androidx.fragment.app.Fragment  Looper androidx.fragment.app.Fragment  MainActivity androidx.fragment.app.Fragment  MaterialAlertDialogBuilder androidx.fragment.app.Fragment  MaterialButton androidx.fragment.app.Fragment  
MediaStore androidx.fragment.app.Fragment  MotionEvent androidx.fragment.app.Fragment  OpenLinkValidator androidx.fragment.app.Fragment  R androidx.fragment.app.Fragment  
RegexPatterns androidx.fragment.app.Fragment  	Resources androidx.fragment.app.Fragment  SettingsActivity androidx.fragment.app.Fragment  ShapeableImageView androidx.fragment.app.Fragment  ShareCompat androidx.fragment.app.Fragment  Snackbar androidx.fragment.app.Fragment  SpannableStringBuilder androidx.fragment.app.Fragment  String androidx.fragment.app.Fragment  Suppress androidx.fragment.app.Fragment  SuppressLint androidx.fragment.app.Fragment  System androidx.fragment.app.Fragment  Toast androidx.fragment.app.Fragment  
URLDecoder androidx.fragment.app.Fragment  URLUtil androidx.fragment.app.Fragment  Uri androidx.fragment.app.Fragment  ValidLinkChecker androidx.fragment.app.Fragment  View androidx.fragment.app.Fragment  WebChromeClient androidx.fragment.app.Fragment  WebResourceRequest androidx.fragment.app.Fragment  WebResourceResponse androidx.fragment.app.Fragment  WebSettings androidx.fragment.app.Fragment  WebView androidx.fragment.app.Fragment  
WebViewClient androidx.fragment.app.Fragment  activity androidx.fragment.app.Fragment  any androidx.fragment.app.Fragment  apply androidx.fragment.app.Fragment  binding androidx.fragment.app.Fragment  contains androidx.fragment.app.Fragment  createStartIntent androidx.fragment.app.Fragment  getInstance androidx.fragment.app.Fragment  indexOf androidx.fragment.app.Fragment  java androidx.fragment.app.Fragment  let androidx.fragment.app.Fragment  listOf androidx.fragment.app.Fragment  matches androidx.fragment.app.Fragment  
mutableListOf androidx.fragment.app.Fragment  onContextItemSelected androidx.fragment.app.Fragment  onCreateContextMenu androidx.fragment.app.Fragment  
onDestroyView androidx.fragment.app.Fragment  onPause androidx.fragment.app.Fragment  onResume androidx.fragment.app.Fragment  onSaveInstanceState androidx.fragment.app.Fragment  registerForContextMenu androidx.fragment.app.Fragment  replace androidx.fragment.app.Fragment  requireActivity androidx.fragment.app.Fragment  requireContext androidx.fragment.app.Fragment  run androidx.fragment.app.Fragment  
startActivity androidx.fragment.app.Fragment  
startsWith androidx.fragment.app.Fragment  	substring androidx.fragment.app.Fragment  toByteArray androidx.fragment.app.Fragment  toRegex androidx.fragment.app.Fragment  
trimIndent androidx.fragment.app.Fragment  use androidx.fragment.app.Fragment  webIcon androidx.fragment.app.Fragment  OnTouchListener #androidx.fragment.app.Fragment.View  ActivityDownloadBinding &androidx.fragment.app.FragmentActivity  ActivityFilterUpdatesBinding &androidx.fragment.app.FragmentActivity  ActivityMainBinding &androidx.fragment.app.FragmentActivity  ActivityNotFoundException &androidx.fragment.app.FragmentActivity  ActivitySettingsBinding &androidx.fragment.app.FragmentActivity  	AdBlocker &androidx.fragment.app.FragmentActivity  AlertDialog &androidx.fragment.app.FragmentActivity  BroadcastReceiver &androidx.fragment.app.FragmentActivity  BrowseFragment &androidx.fragment.app.FragmentActivity  Build &androidx.fragment.app.FragmentActivity  
ComponentName &androidx.fragment.app.FragmentActivity  Context &androidx.fragment.app.FragmentActivity  Date &androidx.fragment.app.FragmentActivity  Dispatchers &androidx.fragment.app.FragmentActivity  DownloadActivity &androidx.fragment.app.FragmentActivity  DownloadAdapter &androidx.fragment.app.FragmentActivity  DownloadService &androidx.fragment.app.FragmentActivity  DownloadStatus &androidx.fragment.app.FragmentActivity  Environment &androidx.fragment.app.FragmentActivity  	Exception &androidx.fragment.app.FragmentActivity  File &androidx.fragment.app.FragmentActivity  FileProvider &androidx.fragment.app.FragmentActivity  FilterUpdatesActivity &androidx.fragment.app.FragmentActivity  FirebaseMessaging &androidx.fragment.app.FragmentActivity  IBinder &androidx.fragment.app.FragmentActivity  IllegalArgumentException &androidx.fragment.app.FragmentActivity  Intent &androidx.fragment.app.FragmentActivity  IntentFilter &androidx.fragment.app.FragmentActivity  LinearLayoutManager &androidx.fragment.app.FragmentActivity  LocalBroadcastManager &androidx.fragment.app.FragmentActivity  Locale &androidx.fragment.app.FragmentActivity  Log &androidx.fragment.app.FragmentActivity  MODE_PRIVATE &androidx.fragment.app.FragmentActivity  MoreOptionsBottomSheetFragment &androidx.fragment.app.FragmentActivity  OnBackPressedCallback &androidx.fragment.app.FragmentActivity  OpenLinkValidator &androidx.fragment.app.FragmentActivity  PermissionManager &androidx.fragment.app.FragmentActivity  R &androidx.fragment.app.FragmentActivity  ServiceConnection &androidx.fragment.app.FragmentActivity  SimpleDateFormat &androidx.fragment.app.FragmentActivity  Snackbar &androidx.fragment.app.FragmentActivity  System &androidx.fragment.app.FragmentActivity  Toast &androidx.fragment.app.FragmentActivity  Uri &androidx.fragment.app.FragmentActivity  ValidLinkChecker &androidx.fragment.app.FragmentActivity  View &androidx.fragment.app.FragmentActivity  WindowCompat &androidx.fragment.app.FragmentActivity  WindowInsetsCompat &androidx.fragment.app.FragmentActivity  WindowInsetsControllerCompat &androidx.fragment.app.FragmentActivity  
WindowManager &androidx.fragment.app.FragmentActivity  	adBlocker &androidx.fragment.app.FragmentActivity  also &androidx.fragment.app.FragmentActivity  android &androidx.fragment.app.FragmentActivity  apply &androidx.fragment.app.FragmentActivity  binding &androidx.fragment.app.FragmentActivity  browseFragment &androidx.fragment.app.FragmentActivity  clearAdBlockerTimeConstraints &androidx.fragment.app.FragmentActivity  contentResolver &androidx.fragment.app.FragmentActivity  createControlIntent &androidx.fragment.app.FragmentActivity  createStartIntent &androidx.fragment.app.FragmentActivity  delay &androidx.fragment.app.FragmentActivity  downloadAdapter &androidx.fragment.app.FragmentActivity  downloadService &androidx.fragment.app.FragmentActivity  	emptyList &androidx.fragment.app.FragmentActivity  endsWith &androidx.fragment.app.FragmentActivity  getInstance &androidx.fragment.app.FragmentActivity  	getString &androidx.fragment.app.FragmentActivity  isBound &androidx.fragment.app.FragmentActivity  isEmpty &androidx.fragment.app.FragmentActivity  isFullscreen &androidx.fragment.app.FragmentActivity  
isInitialized &androidx.fragment.app.FragmentActivity  isManualFullscreen &androidx.fragment.app.FragmentActivity  
isNotEmpty &androidx.fragment.app.FragmentActivity  	isVisible &androidx.fragment.app.FragmentActivity  java &androidx.fragment.app.FragmentActivity  launch &androidx.fragment.app.FragmentActivity  let &androidx.fragment.app.FragmentActivity  lifecycleScope &androidx.fragment.app.FragmentActivity  
loadDownloads &androidx.fragment.app.FragmentActivity  loadLastUpdatedTimes &androidx.fragment.app.FragmentActivity  maxOf &androidx.fragment.app.FragmentActivity  onBackPressedDispatcher &androidx.fragment.app.FragmentActivity  onCreate &androidx.fragment.app.FragmentActivity  onPause &androidx.fragment.app.FragmentActivity  onResume &androidx.fragment.app.FragmentActivity  openLinkValidator &androidx.fragment.app.FragmentActivity  requestedOrientation &androidx.fragment.app.FragmentActivity  saveUpdateTime &androidx.fragment.app.FragmentActivity  startService &androidx.fragment.app.FragmentActivity  supportFragmentManager &androidx.fragment.app.FragmentActivity  validLinkChecker &androidx.fragment.app.FragmentActivity  with &androidx.fragment.app.FragmentActivity  withContext &androidx.fragment.app.FragmentActivity  DownloadBinder 6androidx.fragment.app.FragmentActivity.DownloadService  beginTransaction %androidx.fragment.app.FragmentManager  commit )androidx.fragment.app.FragmentTransaction  replace )androidx.fragment.app.FragmentTransaction  LifecycleCoroutineScope androidx.lifecycle  lifecycleScope androidx.lifecycle  launch *androidx.lifecycle.LifecycleCoroutineScope  LocalBroadcastManager &androidx.localbroadcastmanager.content  getInstance <androidx.localbroadcastmanager.content.LocalBroadcastManager  registerReceiver <androidx.localbroadcastmanager.content.LocalBroadcastManager  
sendBroadcast <androidx.localbroadcastmanager.content.LocalBroadcastManager  unregisterReceiver <androidx.localbroadcastmanager.content.LocalBroadcastManager  DiffUtil androidx.recyclerview.widget  LinearLayoutManager androidx.recyclerview.widget  ListAdapter androidx.recyclerview.widget  RecyclerView androidx.recyclerview.widget  ItemCallback %androidx.recyclerview.widget.DiffUtil  DownloadStatus (androidx.recyclerview.widget.ListAdapter  ItemDownloadBinding (androidx.recyclerview.widget.ListAdapter  LayoutInflater (androidx.recyclerview.widget.ListAdapter  R (androidx.recyclerview.widget.ListAdapter  String (androidx.recyclerview.widget.ListAdapter  View (androidx.recyclerview.widget.ListAdapter  context (androidx.recyclerview.widget.ListAdapter  format (androidx.recyclerview.widget.ListAdapter  
onCancelClick (androidx.recyclerview.widget.ListAdapter  
onDeleteClick (androidx.recyclerview.widget.ListAdapter  onItemClick (androidx.recyclerview.widget.ListAdapter  onOpenClick (androidx.recyclerview.widget.ListAdapter  onPauseClick (androidx.recyclerview.widget.ListAdapter  
onResumeClick (androidx.recyclerview.widget.ListAdapter  onRetryClick (androidx.recyclerview.widget.ListAdapter  
LayoutManager )androidx.recyclerview.widget.RecyclerView  LinearLayoutManager )androidx.recyclerview.widget.RecyclerView  
ViewHolder )androidx.recyclerview.widget.RecyclerView  adapter )androidx.recyclerview.widget.RecyclerView  apply )androidx.recyclerview.widget.RecyclerView  downloadAdapter )androidx.recyclerview.widget.RecyclerView  	isVisible )androidx.recyclerview.widget.RecyclerView  
layoutManager )androidx.recyclerview.widget.RecyclerView  setHasFixedSize )androidx.recyclerview.widget.RecyclerView  DownloadStatus 1androidx.recyclerview.widget.RecyclerView.Adapter  ItemDownloadBinding 1androidx.recyclerview.widget.RecyclerView.Adapter  LayoutInflater 1androidx.recyclerview.widget.RecyclerView.Adapter  R 1androidx.recyclerview.widget.RecyclerView.Adapter  String 1androidx.recyclerview.widget.RecyclerView.Adapter  View 1androidx.recyclerview.widget.RecyclerView.Adapter  context 1androidx.recyclerview.widget.RecyclerView.Adapter  format 1androidx.recyclerview.widget.RecyclerView.Adapter  
onCancelClick 1androidx.recyclerview.widget.RecyclerView.Adapter  
onDeleteClick 1androidx.recyclerview.widget.RecyclerView.Adapter  onItemClick 1androidx.recyclerview.widget.RecyclerView.Adapter  onOpenClick 1androidx.recyclerview.widget.RecyclerView.Adapter  onPauseClick 1androidx.recyclerview.widget.RecyclerView.Adapter  
onResumeClick 1androidx.recyclerview.widget.RecyclerView.Adapter  onRetryClick 1androidx.recyclerview.widget.RecyclerView.Adapter  DownloadStatus 4androidx.recyclerview.widget.RecyclerView.ViewHolder  R 4androidx.recyclerview.widget.RecyclerView.ViewHolder  String 4androidx.recyclerview.widget.RecyclerView.ViewHolder  View 4androidx.recyclerview.widget.RecyclerView.ViewHolder  context 4androidx.recyclerview.widget.RecyclerView.ViewHolder  format 4androidx.recyclerview.widget.RecyclerView.ViewHolder  
onCancelClick 4androidx.recyclerview.widget.RecyclerView.ViewHolder  
onDeleteClick 4androidx.recyclerview.widget.RecyclerView.ViewHolder  onItemClick 4androidx.recyclerview.widget.RecyclerView.ViewHolder  onOpenClick 4androidx.recyclerview.widget.RecyclerView.ViewHolder  onPauseClick 4androidx.recyclerview.widget.RecyclerView.ViewHolder  
onResumeClick 4androidx.recyclerview.widget.RecyclerView.ViewHolder  onRetryClick 4androidx.recyclerview.widget.RecyclerView.ViewHolder  SwipeRefreshLayout "androidx.swiperefreshlayout.widget  OnRefreshListener 5androidx.swiperefreshlayout.widget.SwipeRefreshLayout  R 5androidx.swiperefreshlayout.widget.SwipeRefreshLayout  apply 5androidx.swiperefreshlayout.widget.SwipeRefreshLayout  binding 5androidx.swiperefreshlayout.widget.SwipeRefreshLayout  isRefreshing 5androidx.swiperefreshlayout.widget.SwipeRefreshLayout  requireActivity 5androidx.swiperefreshlayout.widget.SwipeRefreshLayout  setColorSchemeResources 5androidx.swiperefreshlayout.widget.SwipeRefreshLayout  setOnRefreshListener 5androidx.swiperefreshlayout.widget.SwipeRefreshLayout  (setProgressBackgroundColorSchemeResource 5androidx.swiperefreshlayout.widget.SwipeRefreshLayout  <SAM-CONSTRUCTOR> Gandroidx.swiperefreshlayout.widget.SwipeRefreshLayout.OnRefreshListener  BuildConfig com.elewashy.egyfilm  R com.elewashy.egyfilm  VERSION_NAME  com.elewashy.egyfilm.BuildConfig  activity_fade_out_down com.elewashy.egyfilm.R.anim  activity_slide_in_up com.elewashy.egyfilm.R.anim  egyfilm_dark_gray com.elewashy.egyfilm.R.color  egyfilm_red com.elewashy.egyfilm.R.color  egyfilm_red_dark com.elewashy.egyfilm.R.color  	ic_cancel com.elewashy.egyfilm.R.drawable  	ic_delete com.elewashy.egyfilm.R.drawable  ic_download com.elewashy.egyfilm.R.drawable  ic_download_file com.elewashy.egyfilm.R.drawable  ic_file_complete com.elewashy.egyfilm.R.drawable  ic_open_file com.elewashy.egyfilm.R.drawable  ic_pause com.elewashy.egyfilm.R.drawable  
ic_play_arrow com.elewashy.egyfilm.R.drawable  
ic_refresh com.elewashy.egyfilm.R.drawable  backBtn com.elewashy.egyfilm.R.id  downloadsBtn com.elewashy.egyfilm.R.id  
forwardBtn com.elewashy.egyfilm.R.id  fragmentContainer com.elewashy.egyfilm.R.id  
fullscreenBtn com.elewashy.egyfilm.R.id  settingsBtn com.elewashy.egyfilm.R.id  bottom_sheet_more_options com.elewashy.egyfilm.R.layout  fragment_browse com.elewashy.egyfilm.R.layout  ic_launcher com.elewashy.egyfilm.R.mipmap  app_name com.elewashy.egyfilm.R.string  cancel_button_desc com.elewashy.egyfilm.R.string  delete_button_desc com.elewashy.egyfilm.R.string  last_updated com.elewashy.egyfilm.R.string  
never_updated com.elewashy.egyfilm.R.string  open_file_button_desc com.elewashy.egyfilm.R.string  pause_resume_button_desc com.elewashy.egyfilm.R.string  retry_button_desc com.elewashy.egyfilm.R.string  update_all_filters com.elewashy.egyfilm.R.string  
update_failed com.elewashy.egyfilm.R.string  update_success com.elewashy.egyfilm.R.string  updating com.elewashy.egyfilm.R.string  ActivityDownloadBinding com.elewashy.egyfilm.activity  ActivityFilterUpdatesBinding com.elewashy.egyfilm.activity  ActivityMainBinding com.elewashy.egyfilm.activity  ActivityNotFoundException com.elewashy.egyfilm.activity  ActivityResultContracts com.elewashy.egyfilm.activity  ActivityResultLauncher com.elewashy.egyfilm.activity  ActivitySettingsBinding com.elewashy.egyfilm.activity  	AdBlocker com.elewashy.egyfilm.activity  AlertDialog com.elewashy.egyfilm.activity  AppCompatActivity com.elewashy.egyfilm.activity  Boolean com.elewashy.egyfilm.activity  BroadcastReceiver com.elewashy.egyfilm.activity  BrowseFragment com.elewashy.egyfilm.activity  Build com.elewashy.egyfilm.activity  BuildConfig com.elewashy.egyfilm.activity  Bundle com.elewashy.egyfilm.activity  CONNECTIVITY_SERVICE com.elewashy.egyfilm.activity  ComponentActivity com.elewashy.egyfilm.activity  
ComponentName com.elewashy.egyfilm.activity  ConnectivityManager com.elewashy.egyfilm.activity  Context com.elewashy.egyfilm.activity  Date com.elewashy.egyfilm.activity  
Deprecated com.elewashy.egyfilm.activity  Dispatchers com.elewashy.egyfilm.activity  DownloadActivity com.elewashy.egyfilm.activity  DownloadAdapter com.elewashy.egyfilm.activity  DownloadItem com.elewashy.egyfilm.activity  DownloadService com.elewashy.egyfilm.activity  DownloadStatus com.elewashy.egyfilm.activity  EgyFilmTheme com.elewashy.egyfilm.activity  Environment com.elewashy.egyfilm.activity  	Exception com.elewashy.egyfilm.activity  File com.elewashy.egyfilm.activity  FileProvider com.elewashy.egyfilm.activity  FilterUpdatesActivity com.elewashy.egyfilm.activity  FirebaseMessaging com.elewashy.egyfilm.activity  Gson com.elewashy.egyfilm.activity  Handler com.elewashy.egyfilm.activity  HttpURLConnection com.elewashy.egyfilm.activity  IBinder com.elewashy.egyfilm.activity  IOException com.elewashy.egyfilm.activity  IllegalArgumentException com.elewashy.egyfilm.activity  Int com.elewashy.egyfilm.activity  Intent com.elewashy.egyfilm.activity  IntentFilter com.elewashy.egyfilm.activity  LinearLayoutManager com.elewashy.egyfilm.activity  List com.elewashy.egyfilm.activity  LocalBroadcastManager com.elewashy.egyfilm.activity  Locale com.elewashy.egyfilm.activity  Log com.elewashy.egyfilm.activity  Long com.elewashy.egyfilm.activity  Looper com.elewashy.egyfilm.activity  MODE_PRIVATE com.elewashy.egyfilm.activity  MainActivity com.elewashy.egyfilm.activity  MenuItem com.elewashy.egyfilm.activity  MoreOptionsBottomSheetFragment com.elewashy.egyfilm.activity  NetworkCapabilities com.elewashy.egyfilm.activity  OVERRIDE_TRANSITION_OPEN com.elewashy.egyfilm.activity  OnBackPressedCallback com.elewashy.egyfilm.activity  OpenLinkValidator com.elewashy.egyfilm.activity  PermissionManager com.elewashy.egyfilm.activity  R com.elewashy.egyfilm.activity  SecurityException com.elewashy.egyfilm.activity  ServiceConnection com.elewashy.egyfilm.activity  Settings com.elewashy.egyfilm.activity  SettingsActivity com.elewashy.egyfilm.activity  SimpleDateFormat com.elewashy.egyfilm.activity  Snackbar com.elewashy.egyfilm.activity  SplashActivity com.elewashy.egyfilm.activity  SplashScreen com.elewashy.egyfilm.activity  String com.elewashy.egyfilm.activity  Suppress com.elewashy.egyfilm.activity  System com.elewashy.egyfilm.activity  Toast com.elewashy.egyfilm.activity  UPDATE_CHECK_URL com.elewashy.egyfilm.activity  URL com.elewashy.egyfilm.activity  Unit com.elewashy.egyfilm.activity  UpdateResponse com.elewashy.egyfilm.activity  Uri com.elewashy.egyfilm.activity  ValidLinkChecker com.elewashy.egyfilm.activity  View com.elewashy.egyfilm.activity  WindowCompat com.elewashy.egyfilm.activity  WindowInsetsCompat com.elewashy.egyfilm.activity  WindowInsetsControllerCompat com.elewashy.egyfilm.activity  
WindowManager com.elewashy.egyfilm.activity  	adBlocker com.elewashy.egyfilm.activity  also com.elewashy.egyfilm.activity  android com.elewashy.egyfilm.activity  apply com.elewashy.egyfilm.activity  binding com.elewashy.egyfilm.activity  browseFragment com.elewashy.egyfilm.activity  bufferedReader com.elewashy.egyfilm.activity  checkForInternet com.elewashy.egyfilm.activity  clearAdBlockerTimeConstraints com.elewashy.egyfilm.activity  createControlIntent com.elewashy.egyfilm.activity  createStartIntent com.elewashy.egyfilm.activity  delay com.elewashy.egyfilm.activity  downloadAdapter com.elewashy.egyfilm.activity  downloadService com.elewashy.egyfilm.activity  	emptyList com.elewashy.egyfilm.activity  endsWith com.elewashy.egyfilm.activity  getInstance com.elewashy.egyfilm.activity  	getString com.elewashy.egyfilm.activity  getValue com.elewashy.egyfilm.activity  isBound com.elewashy.egyfilm.activity  isEmpty com.elewashy.egyfilm.activity  isFullscreen com.elewashy.egyfilm.activity  
isInitialized com.elewashy.egyfilm.activity  isManualFullscreen com.elewashy.egyfilm.activity  
isNotEmpty com.elewashy.egyfilm.activity  java com.elewashy.egyfilm.activity  launch com.elewashy.egyfilm.activity  let com.elewashy.egyfilm.activity  lifecycleScope com.elewashy.egyfilm.activity  
loadDownloads com.elewashy.egyfilm.activity  loadLastUpdatedTimes com.elewashy.egyfilm.activity  map com.elewashy.egyfilm.activity  maxOf com.elewashy.egyfilm.activity  mutableFloatStateOf com.elewashy.egyfilm.activity  mutableStateOf com.elewashy.egyfilm.activity  onBackPressedDispatcher com.elewashy.egyfilm.activity  openLinkValidator com.elewashy.egyfilm.activity  provideDelegate com.elewashy.egyfilm.activity  readText com.elewashy.egyfilm.activity  saveUpdateTime com.elewashy.egyfilm.activity  setValue com.elewashy.egyfilm.activity  split com.elewashy.egyfilm.activity  startService com.elewashy.egyfilm.activity  thread com.elewashy.egyfilm.activity  toIntOrNull com.elewashy.egyfilm.activity  until com.elewashy.egyfilm.activity  use com.elewashy.egyfilm.activity  validLinkChecker com.elewashy.egyfilm.activity  with com.elewashy.egyfilm.activity  withContext com.elewashy.egyfilm.activity  ActivityDownloadBinding .com.elewashy.egyfilm.activity.DownloadActivity  AlertDialog .com.elewashy.egyfilm.activity.DownloadActivity  Build .com.elewashy.egyfilm.activity.DownloadActivity  Context .com.elewashy.egyfilm.activity.DownloadActivity  Dispatchers .com.elewashy.egyfilm.activity.DownloadActivity  DownloadAdapter .com.elewashy.egyfilm.activity.DownloadActivity  DownloadService .com.elewashy.egyfilm.activity.DownloadActivity  DownloadStatus .com.elewashy.egyfilm.activity.DownloadActivity  Environment .com.elewashy.egyfilm.activity.DownloadActivity  File .com.elewashy.egyfilm.activity.DownloadActivity  FileProvider .com.elewashy.egyfilm.activity.DownloadActivity  Intent .com.elewashy.egyfilm.activity.DownloadActivity  IntentFilter .com.elewashy.egyfilm.activity.DownloadActivity  LinearLayoutManager .com.elewashy.egyfilm.activity.DownloadActivity  LocalBroadcastManager .com.elewashy.egyfilm.activity.DownloadActivity  PermissionManager .com.elewashy.egyfilm.activity.DownloadActivity  Toast .com.elewashy.egyfilm.activity.DownloadActivity  also .com.elewashy.egyfilm.activity.DownloadActivity  android .com.elewashy.egyfilm.activity.DownloadActivity  applicationContext .com.elewashy.egyfilm.activity.DownloadActivity  apply .com.elewashy.egyfilm.activity.DownloadActivity  bindService .com.elewashy.egyfilm.activity.DownloadActivity  binding .com.elewashy.egyfilm.activity.DownloadActivity  
connection .com.elewashy.egyfilm.activity.DownloadActivity  contentResolver .com.elewashy.egyfilm.activity.DownloadActivity  createControlIntent .com.elewashy.egyfilm.activity.DownloadActivity  createStartIntent .com.elewashy.egyfilm.activity.DownloadActivity  delay .com.elewashy.egyfilm.activity.DownloadActivity  deleteDownloadedFile .com.elewashy.egyfilm.activity.DownloadActivity  deleteFileFromDevice .com.elewashy.egyfilm.activity.DownloadActivity  downloadAdapter .com.elewashy.egyfilm.activity.DownloadActivity  downloadService .com.elewashy.egyfilm.activity.DownloadActivity  downloadUpdateReceiver .com.elewashy.egyfilm.activity.DownloadActivity  	emptyList .com.elewashy.egyfilm.activity.DownloadActivity  endsWith .com.elewashy.egyfilm.activity.DownloadActivity  finish .com.elewashy.egyfilm.activity.DownloadActivity  installApkFile .com.elewashy.egyfilm.activity.DownloadActivity  isBound .com.elewashy.egyfilm.activity.DownloadActivity  isEmpty .com.elewashy.egyfilm.activity.DownloadActivity  
isNotEmpty .com.elewashy.egyfilm.activity.DownloadActivity  	isVisible .com.elewashy.egyfilm.activity.DownloadActivity  java .com.elewashy.egyfilm.activity.DownloadActivity  launch .com.elewashy.egyfilm.activity.DownloadActivity  layoutInflater .com.elewashy.egyfilm.activity.DownloadActivity  let .com.elewashy.egyfilm.activity.DownloadActivity  lifecycleScope .com.elewashy.egyfilm.activity.DownloadActivity  
loadDownloads .com.elewashy.egyfilm.activity.DownloadActivity  onDeleteClicked .com.elewashy.egyfilm.activity.DownloadActivity  onDownloadItemClicked .com.elewashy.egyfilm.activity.DownloadActivity  
onOpenClicked .com.elewashy.egyfilm.activity.DownloadActivity  openDownloadedFile .com.elewashy.egyfilm.activity.DownloadActivity  permissionManager .com.elewashy.egyfilm.activity.DownloadActivity  sendControlIntent .com.elewashy.egyfilm.activity.DownloadActivity  setContentView .com.elewashy.egyfilm.activity.DownloadActivity  setSupportActionBar .com.elewashy.egyfilm.activity.DownloadActivity  setupRecyclerView .com.elewashy.egyfilm.activity.DownloadActivity  
startActivity .com.elewashy.egyfilm.activity.DownloadActivity  startService .com.elewashy.egyfilm.activity.DownloadActivity  supportActionBar .com.elewashy.egyfilm.activity.DownloadActivity  
unbindService .com.elewashy.egyfilm.activity.DownloadActivity  updateDownloadList .com.elewashy.egyfilm.activity.DownloadActivity  DownloadBinder -com.elewashy.egyfilm.activity.DownloadService  ActivityFilterUpdatesBinding 3com.elewashy.egyfilm.activity.FilterUpdatesActivity  	AdBlocker 3com.elewashy.egyfilm.activity.FilterUpdatesActivity  Date 3com.elewashy.egyfilm.activity.FilterUpdatesActivity  Dispatchers 3com.elewashy.egyfilm.activity.FilterUpdatesActivity  Locale 3com.elewashy.egyfilm.activity.FilterUpdatesActivity  MODE_PRIVATE 3com.elewashy.egyfilm.activity.FilterUpdatesActivity  OpenLinkValidator 3com.elewashy.egyfilm.activity.FilterUpdatesActivity  R 3com.elewashy.egyfilm.activity.FilterUpdatesActivity  SimpleDateFormat 3com.elewashy.egyfilm.activity.FilterUpdatesActivity  System 3com.elewashy.egyfilm.activity.FilterUpdatesActivity  Toast 3com.elewashy.egyfilm.activity.FilterUpdatesActivity  ValidLinkChecker 3com.elewashy.egyfilm.activity.FilterUpdatesActivity  View 3com.elewashy.egyfilm.activity.FilterUpdatesActivity  	adBlocker 3com.elewashy.egyfilm.activity.FilterUpdatesActivity  apply 3com.elewashy.egyfilm.activity.FilterUpdatesActivity  binding 3com.elewashy.egyfilm.activity.FilterUpdatesActivity  clearAdBlockerTimeConstraints 3com.elewashy.egyfilm.activity.FilterUpdatesActivity  finish 3com.elewashy.egyfilm.activity.FilterUpdatesActivity  getInstance 3com.elewashy.egyfilm.activity.FilterUpdatesActivity  getSharedPreferences 3com.elewashy.egyfilm.activity.FilterUpdatesActivity  	getString 3com.elewashy.egyfilm.activity.FilterUpdatesActivity  launch 3com.elewashy.egyfilm.activity.FilterUpdatesActivity  layoutInflater 3com.elewashy.egyfilm.activity.FilterUpdatesActivity  lifecycleScope 3com.elewashy.egyfilm.activity.FilterUpdatesActivity  loadLastUpdatedTimes 3com.elewashy.egyfilm.activity.FilterUpdatesActivity  maxOf 3com.elewashy.egyfilm.activity.FilterUpdatesActivity  openLinkValidator 3com.elewashy.egyfilm.activity.FilterUpdatesActivity  saveUpdateTime 3com.elewashy.egyfilm.activity.FilterUpdatesActivity  setContentView 3com.elewashy.egyfilm.activity.FilterUpdatesActivity  setupUI 3com.elewashy.egyfilm.activity.FilterUpdatesActivity  updateAdBlockerFilter 3com.elewashy.egyfilm.activity.FilterUpdatesActivity  updateAllFilters 3com.elewashy.egyfilm.activity.FilterUpdatesActivity  updateOpenLinksFilter 3com.elewashy.egyfilm.activity.FilterUpdatesActivity  updateValidLinksFilter 3com.elewashy.egyfilm.activity.FilterUpdatesActivity  validLinkChecker 3com.elewashy.egyfilm.activity.FilterUpdatesActivity  withContext 3com.elewashy.egyfilm.activity.FilterUpdatesActivity  ActivityMainBinding *com.elewashy.egyfilm.activity.MainActivity  Boolean *com.elewashy.egyfilm.activity.MainActivity  BrowseFragment *com.elewashy.egyfilm.activity.MainActivity  Build *com.elewashy.egyfilm.activity.MainActivity  Bundle *com.elewashy.egyfilm.activity.MainActivity  	Companion *com.elewashy.egyfilm.activity.MainActivity  Context *com.elewashy.egyfilm.activity.MainActivity  Dispatchers *com.elewashy.egyfilm.activity.MainActivity  DownloadActivity *com.elewashy.egyfilm.activity.MainActivity  DownloadService *com.elewashy.egyfilm.activity.MainActivity  	Exception *com.elewashy.egyfilm.activity.MainActivity  FirebaseMessaging *com.elewashy.egyfilm.activity.MainActivity  Intent *com.elewashy.egyfilm.activity.MainActivity  Log *com.elewashy.egyfilm.activity.MainActivity  Long *com.elewashy.egyfilm.activity.MainActivity  MoreOptionsBottomSheetFragment *com.elewashy.egyfilm.activity.MainActivity  OnBackPressedCallback *com.elewashy.egyfilm.activity.MainActivity  PermissionManager *com.elewashy.egyfilm.activity.MainActivity  R *com.elewashy.egyfilm.activity.MainActivity  Snackbar *com.elewashy.egyfilm.activity.MainActivity  System *com.elewashy.egyfilm.activity.MainActivity  THREE_HOURS_MS *com.elewashy.egyfilm.activity.MainActivity  WindowCompat *com.elewashy.egyfilm.activity.MainActivity  WindowInsetsCompat *com.elewashy.egyfilm.activity.MainActivity  WindowInsetsControllerCompat *com.elewashy.egyfilm.activity.MainActivity  
WindowManager *com.elewashy.egyfilm.activity.MainActivity  android *com.elewashy.egyfilm.activity.MainActivity  backgroundTime *com.elewashy.egyfilm.activity.MainActivity  binding *com.elewashy.egyfilm.activity.MainActivity  browseFragment *com.elewashy.egyfilm.activity.MainActivity  changeFullscreen *com.elewashy.egyfilm.activity.MainActivity  getSharedPreferences *com.elewashy.egyfilm.activity.MainActivity  goBack *com.elewashy.egyfilm.activity.MainActivity  	goForward *com.elewashy.egyfilm.activity.MainActivity  handleIntent *com.elewashy.egyfilm.activity.MainActivity  initializeFirebaseMessaging *com.elewashy.egyfilm.activity.MainActivity  initializeView *com.elewashy.egyfilm.activity.MainActivity  intent *com.elewashy.egyfilm.activity.MainActivity  isFullscreen *com.elewashy.egyfilm.activity.MainActivity  
isInitialized *com.elewashy.egyfilm.activity.MainActivity  isManualFullscreen *com.elewashy.egyfilm.activity.MainActivity  java *com.elewashy.egyfilm.activity.MainActivity  launch *com.elewashy.egyfilm.activity.MainActivity  layoutInflater *com.elewashy.egyfilm.activity.MainActivity  let *com.elewashy.egyfilm.activity.MainActivity  lifecycleScope *com.elewashy.egyfilm.activity.MainActivity  onBackPressedDispatcher *com.elewashy.egyfilm.activity.MainActivity  permissionManager *com.elewashy.egyfilm.activity.MainActivity  requestAllPermissions *com.elewashy.egyfilm.activity.MainActivity  	resources *com.elewashy.egyfilm.activity.MainActivity  setContentView *com.elewashy.egyfilm.activity.MainActivity  	setIntent *com.elewashy.egyfilm.activity.MainActivity  setupBackPressHandling *com.elewashy.egyfilm.activity.MainActivity  
startActivity *com.elewashy.egyfilm.activity.MainActivity  supportFragmentManager *com.elewashy.egyfilm.activity.MainActivity  toggleFullscreen *com.elewashy.egyfilm.activity.MainActivity  window *com.elewashy.egyfilm.activity.MainActivity  with *com.elewashy.egyfilm.activity.MainActivity  withContext *com.elewashy.egyfilm.activity.MainActivity  ActivityMainBinding 4com.elewashy.egyfilm.activity.MainActivity.Companion  BrowseFragment 4com.elewashy.egyfilm.activity.MainActivity.Companion  Build 4com.elewashy.egyfilm.activity.MainActivity.Companion  Context 4com.elewashy.egyfilm.activity.MainActivity.Companion  Dispatchers 4com.elewashy.egyfilm.activity.MainActivity.Companion  DownloadActivity 4com.elewashy.egyfilm.activity.MainActivity.Companion  DownloadService 4com.elewashy.egyfilm.activity.MainActivity.Companion  FirebaseMessaging 4com.elewashy.egyfilm.activity.MainActivity.Companion  Intent 4com.elewashy.egyfilm.activity.MainActivity.Companion  Log 4com.elewashy.egyfilm.activity.MainActivity.Companion  MoreOptionsBottomSheetFragment 4com.elewashy.egyfilm.activity.MainActivity.Companion  PermissionManager 4com.elewashy.egyfilm.activity.MainActivity.Companion  R 4com.elewashy.egyfilm.activity.MainActivity.Companion  Snackbar 4com.elewashy.egyfilm.activity.MainActivity.Companion  System 4com.elewashy.egyfilm.activity.MainActivity.Companion  WindowCompat 4com.elewashy.egyfilm.activity.MainActivity.Companion  WindowInsetsCompat 4com.elewashy.egyfilm.activity.MainActivity.Companion  WindowInsetsControllerCompat 4com.elewashy.egyfilm.activity.MainActivity.Companion  
WindowManager 4com.elewashy.egyfilm.activity.MainActivity.Companion  android 4com.elewashy.egyfilm.activity.MainActivity.Companion  browseFragment 4com.elewashy.egyfilm.activity.MainActivity.Companion  isFullscreen 4com.elewashy.egyfilm.activity.MainActivity.Companion  
isInitialized 4com.elewashy.egyfilm.activity.MainActivity.Companion  isManualFullscreen 4com.elewashy.egyfilm.activity.MainActivity.Companion  java 4com.elewashy.egyfilm.activity.MainActivity.Companion  launch 4com.elewashy.egyfilm.activity.MainActivity.Companion  let 4com.elewashy.egyfilm.activity.MainActivity.Companion  lifecycleScope 4com.elewashy.egyfilm.activity.MainActivity.Companion  onBackPressedDispatcher 4com.elewashy.egyfilm.activity.MainActivity.Companion  with 4com.elewashy.egyfilm.activity.MainActivity.Companion  withContext 4com.elewashy.egyfilm.activity.MainActivity.Companion  content 2com.elewashy.egyfilm.activity.MainActivity.android  res :com.elewashy.egyfilm.activity.MainActivity.android.content  
Configuration >com.elewashy.egyfilm.activity.MainActivity.android.content.res  ActivitySettingsBinding .com.elewashy.egyfilm.activity.SettingsActivity  FilterUpdatesActivity .com.elewashy.egyfilm.activity.SettingsActivity  Intent .com.elewashy.egyfilm.activity.SettingsActivity  binding .com.elewashy.egyfilm.activity.SettingsActivity  finish .com.elewashy.egyfilm.activity.SettingsActivity  java .com.elewashy.egyfilm.activity.SettingsActivity  layoutInflater .com.elewashy.egyfilm.activity.SettingsActivity  setContentView .com.elewashy.egyfilm.activity.SettingsActivity  setupUI .com.elewashy.egyfilm.activity.SettingsActivity  
startActivity .com.elewashy.egyfilm.activity.SettingsActivity  ActivityResultContracts ,com.elewashy.egyfilm.activity.SplashActivity  ActivityResultLauncher ,com.elewashy.egyfilm.activity.SplashActivity  	AdBlocker ,com.elewashy.egyfilm.activity.SplashActivity  Boolean ,com.elewashy.egyfilm.activity.SplashActivity  Build ,com.elewashy.egyfilm.activity.SplashActivity  BuildConfig ,com.elewashy.egyfilm.activity.SplashActivity  Bundle ,com.elewashy.egyfilm.activity.SplashActivity  CONNECTIVITY_SERVICE ,com.elewashy.egyfilm.activity.SplashActivity  ConnectivityManager ,com.elewashy.egyfilm.activity.SplashActivity  
Deprecated ,com.elewashy.egyfilm.activity.SplashActivity  DownloadActivity ,com.elewashy.egyfilm.activity.SplashActivity  DownloadService ,com.elewashy.egyfilm.activity.SplashActivity  EgyFilmTheme ,com.elewashy.egyfilm.activity.SplashActivity  Gson ,com.elewashy.egyfilm.activity.SplashActivity  Handler ,com.elewashy.egyfilm.activity.SplashActivity  HttpURLConnection ,com.elewashy.egyfilm.activity.SplashActivity  IOException ,com.elewashy.egyfilm.activity.SplashActivity  Int ,com.elewashy.egyfilm.activity.SplashActivity  Intent ,com.elewashy.egyfilm.activity.SplashActivity  Looper ,com.elewashy.egyfilm.activity.SplashActivity  MainActivity ,com.elewashy.egyfilm.activity.SplashActivity  NetworkCapabilities ,com.elewashy.egyfilm.activity.SplashActivity  OVERRIDE_TRANSITION_OPEN ,com.elewashy.egyfilm.activity.SplashActivity  OpenLinkValidator ,com.elewashy.egyfilm.activity.SplashActivity  R ,com.elewashy.egyfilm.activity.SplashActivity  Settings ,com.elewashy.egyfilm.activity.SplashActivity  SplashScreen ,com.elewashy.egyfilm.activity.SplashActivity  String ,com.elewashy.egyfilm.activity.SplashActivity  Suppress ,com.elewashy.egyfilm.activity.SplashActivity  Toast ,com.elewashy.egyfilm.activity.SplashActivity  UPDATE_CHECK_URL ,com.elewashy.egyfilm.activity.SplashActivity  URL ,com.elewashy.egyfilm.activity.SplashActivity  UpdateResponse ,com.elewashy.egyfilm.activity.SplashActivity  Uri ,com.elewashy.egyfilm.activity.SplashActivity  ValidLinkChecker ,com.elewashy.egyfilm.activity.SplashActivity  WindowCompat ,com.elewashy.egyfilm.activity.SplashActivity  bufferedReader ,com.elewashy.egyfilm.activity.SplashActivity  canInstallApks ,com.elewashy.egyfilm.activity.SplashActivity  canInstallApps ,com.elewashy.egyfilm.activity.SplashActivity  checkForUpdates ,com.elewashy.egyfilm.activity.SplashActivity  checkInternetAndProceed ,com.elewashy.egyfilm.activity.SplashActivity  compareVersions ,com.elewashy.egyfilm.activity.SplashActivity  createStartIntent ,com.elewashy.egyfilm.activity.SplashActivity  downloadProgress ,com.elewashy.egyfilm.activity.SplashActivity  downloadStatus ,com.elewashy.egyfilm.activity.SplashActivity  finish ,com.elewashy.egyfilm.activity.SplashActivity  getInstance ,com.elewashy.egyfilm.activity.SplashActivity  getSystemService ,com.elewashy.egyfilm.activity.SplashActivity  getValue ,com.elewashy.egyfilm.activity.SplashActivity  handler ,com.elewashy.egyfilm.activity.SplashActivity  hideUpdateDialog ,com.elewashy.egyfilm.activity.SplashActivity  initializeBackgroundServices ,com.elewashy.egyfilm.activity.SplashActivity  installPermissionLauncher ,com.elewashy.egyfilm.activity.SplashActivity  isInternetAvailable ,com.elewashy.egyfilm.activity.SplashActivity  isUpdateAvailable ,com.elewashy.egyfilm.activity.SplashActivity  java ,com.elewashy.egyfilm.activity.SplashActivity  let ,com.elewashy.egyfilm.activity.SplashActivity  map ,com.elewashy.egyfilm.activity.SplashActivity  maxOf ,com.elewashy.egyfilm.activity.SplashActivity  mutableFloatStateOf ,com.elewashy.egyfilm.activity.SplashActivity  mutableStateOf ,com.elewashy.egyfilm.activity.SplashActivity  onRetryClicked ,com.elewashy.egyfilm.activity.SplashActivity  onUpdateClicked ,com.elewashy.egyfilm.activity.SplashActivity  overrideActivityTransition ,com.elewashy.egyfilm.activity.SplashActivity  overridePendingTransition ,com.elewashy.egyfilm.activity.SplashActivity  packageManager ,com.elewashy.egyfilm.activity.SplashActivity  packageName ,com.elewashy.egyfilm.activity.SplashActivity  proceedToMainActivity ,com.elewashy.egyfilm.activity.SplashActivity  provideDelegate ,com.elewashy.egyfilm.activity.SplashActivity  readText ,com.elewashy.egyfilm.activity.SplashActivity  registerForActivityResult ,com.elewashy.egyfilm.activity.SplashActivity  requestInstallPermission ,com.elewashy.egyfilm.activity.SplashActivity  
runOnUiThread ,com.elewashy.egyfilm.activity.SplashActivity  
setContent ,com.elewashy.egyfilm.activity.SplashActivity  setValue ,com.elewashy.egyfilm.activity.SplashActivity  setupActivityResultLaunchers ,com.elewashy.egyfilm.activity.SplashActivity  showDownloadProgress ,com.elewashy.egyfilm.activity.SplashActivity  showNoInternetUI ,com.elewashy.egyfilm.activity.SplashActivity  showRetryButton ,com.elewashy.egyfilm.activity.SplashActivity  showUpdateDialog ,com.elewashy.egyfilm.activity.SplashActivity  split ,com.elewashy.egyfilm.activity.SplashActivity  
startActivity ,com.elewashy.egyfilm.activity.SplashActivity  
startDownload ,com.elewashy.egyfilm.activity.SplashActivity  startService ,com.elewashy.egyfilm.activity.SplashActivity  thread ,com.elewashy.egyfilm.activity.SplashActivity  toIntOrNull ,com.elewashy.egyfilm.activity.SplashActivity  until ,com.elewashy.egyfilm.activity.SplashActivity  
updateMessage ,com.elewashy.egyfilm.activity.SplashActivity  updateResponse ,com.elewashy.egyfilm.activity.SplashActivity  updateTitle ,com.elewashy.egyfilm.activity.SplashActivity  use ,com.elewashy.egyfilm.activity.SplashActivity  window ,com.elewashy.egyfilm.activity.SplashActivity  ActivityResultContracts 6com.elewashy.egyfilm.activity.SplashActivity.Companion  	AdBlocker 6com.elewashy.egyfilm.activity.SplashActivity.Companion  Build 6com.elewashy.egyfilm.activity.SplashActivity.Companion  BuildConfig 6com.elewashy.egyfilm.activity.SplashActivity.Companion  CONNECTIVITY_SERVICE 6com.elewashy.egyfilm.activity.SplashActivity.Companion  DownloadActivity 6com.elewashy.egyfilm.activity.SplashActivity.Companion  DownloadService 6com.elewashy.egyfilm.activity.SplashActivity.Companion  EgyFilmTheme 6com.elewashy.egyfilm.activity.SplashActivity.Companion  Gson 6com.elewashy.egyfilm.activity.SplashActivity.Companion  Handler 6com.elewashy.egyfilm.activity.SplashActivity.Companion  HttpURLConnection 6com.elewashy.egyfilm.activity.SplashActivity.Companion  Intent 6com.elewashy.egyfilm.activity.SplashActivity.Companion  Looper 6com.elewashy.egyfilm.activity.SplashActivity.Companion  MainActivity 6com.elewashy.egyfilm.activity.SplashActivity.Companion  NetworkCapabilities 6com.elewashy.egyfilm.activity.SplashActivity.Companion  OVERRIDE_TRANSITION_OPEN 6com.elewashy.egyfilm.activity.SplashActivity.Companion  OpenLinkValidator 6com.elewashy.egyfilm.activity.SplashActivity.Companion  R 6com.elewashy.egyfilm.activity.SplashActivity.Companion  Settings 6com.elewashy.egyfilm.activity.SplashActivity.Companion  SplashScreen 6com.elewashy.egyfilm.activity.SplashActivity.Companion  Toast 6com.elewashy.egyfilm.activity.SplashActivity.Companion  UPDATE_CHECK_URL 6com.elewashy.egyfilm.activity.SplashActivity.Companion  URL 6com.elewashy.egyfilm.activity.SplashActivity.Companion  UpdateResponse 6com.elewashy.egyfilm.activity.SplashActivity.Companion  Uri 6com.elewashy.egyfilm.activity.SplashActivity.Companion  ValidLinkChecker 6com.elewashy.egyfilm.activity.SplashActivity.Companion  WindowCompat 6com.elewashy.egyfilm.activity.SplashActivity.Companion  bufferedReader 6com.elewashy.egyfilm.activity.SplashActivity.Companion  createStartIntent 6com.elewashy.egyfilm.activity.SplashActivity.Companion  getInstance 6com.elewashy.egyfilm.activity.SplashActivity.Companion  getValue 6com.elewashy.egyfilm.activity.SplashActivity.Companion  java 6com.elewashy.egyfilm.activity.SplashActivity.Companion  let 6com.elewashy.egyfilm.activity.SplashActivity.Companion  map 6com.elewashy.egyfilm.activity.SplashActivity.Companion  maxOf 6com.elewashy.egyfilm.activity.SplashActivity.Companion  mutableFloatStateOf 6com.elewashy.egyfilm.activity.SplashActivity.Companion  mutableStateOf 6com.elewashy.egyfilm.activity.SplashActivity.Companion  provideDelegate 6com.elewashy.egyfilm.activity.SplashActivity.Companion  readText 6com.elewashy.egyfilm.activity.SplashActivity.Companion  
setContent 6com.elewashy.egyfilm.activity.SplashActivity.Companion  setValue 6com.elewashy.egyfilm.activity.SplashActivity.Companion  split 6com.elewashy.egyfilm.activity.SplashActivity.Companion  thread 6com.elewashy.egyfilm.activity.SplashActivity.Companion  toIntOrNull 6com.elewashy.egyfilm.activity.SplashActivity.Companion  until 6com.elewashy.egyfilm.activity.SplashActivity.Companion  use 6com.elewashy.egyfilm.activity.SplashActivity.Companion  content %com.elewashy.egyfilm.activity.android  res -com.elewashy.egyfilm.activity.android.content  
Configuration 1com.elewashy.egyfilm.activity.android.content.res  Boolean com.elewashy.egyfilm.adapter  Context com.elewashy.egyfilm.adapter  DiffUtil com.elewashy.egyfilm.adapter  DownloadAdapter com.elewashy.egyfilm.adapter  DownloadItem com.elewashy.egyfilm.adapter  DownloadStatus com.elewashy.egyfilm.adapter  DownloadViewHolder com.elewashy.egyfilm.adapter  Int com.elewashy.egyfilm.adapter  ItemDownloadBinding com.elewashy.egyfilm.adapter  LayoutInflater com.elewashy.egyfilm.adapter  ListAdapter com.elewashy.egyfilm.adapter  R com.elewashy.egyfilm.adapter  RecyclerView com.elewashy.egyfilm.adapter  String com.elewashy.egyfilm.adapter  Unit com.elewashy.egyfilm.adapter  View com.elewashy.egyfilm.adapter  	ViewGroup com.elewashy.egyfilm.adapter  context com.elewashy.egyfilm.adapter  format com.elewashy.egyfilm.adapter  
onCancelClick com.elewashy.egyfilm.adapter  
onDeleteClick com.elewashy.egyfilm.adapter  onItemClick com.elewashy.egyfilm.adapter  onOpenClick com.elewashy.egyfilm.adapter  onPauseClick com.elewashy.egyfilm.adapter  
onResumeClick com.elewashy.egyfilm.adapter  onRetryClick com.elewashy.egyfilm.adapter  ItemCallback %com.elewashy.egyfilm.adapter.DiffUtil  Boolean ,com.elewashy.egyfilm.adapter.DownloadAdapter  Context ,com.elewashy.egyfilm.adapter.DownloadAdapter  DiffUtil ,com.elewashy.egyfilm.adapter.DownloadAdapter  DownloadDiffCallback ,com.elewashy.egyfilm.adapter.DownloadAdapter  DownloadItem ,com.elewashy.egyfilm.adapter.DownloadAdapter  DownloadStatus ,com.elewashy.egyfilm.adapter.DownloadAdapter  DownloadViewHolder ,com.elewashy.egyfilm.adapter.DownloadAdapter  Int ,com.elewashy.egyfilm.adapter.DownloadAdapter  ItemDownloadBinding ,com.elewashy.egyfilm.adapter.DownloadAdapter  LayoutInflater ,com.elewashy.egyfilm.adapter.DownloadAdapter  R ,com.elewashy.egyfilm.adapter.DownloadAdapter  RecyclerView ,com.elewashy.egyfilm.adapter.DownloadAdapter  String ,com.elewashy.egyfilm.adapter.DownloadAdapter  Unit ,com.elewashy.egyfilm.adapter.DownloadAdapter  View ,com.elewashy.egyfilm.adapter.DownloadAdapter  	ViewGroup ,com.elewashy.egyfilm.adapter.DownloadAdapter  context ,com.elewashy.egyfilm.adapter.DownloadAdapter  format ,com.elewashy.egyfilm.adapter.DownloadAdapter  getItem ,com.elewashy.egyfilm.adapter.DownloadAdapter  
onCancelClick ,com.elewashy.egyfilm.adapter.DownloadAdapter  
onDeleteClick ,com.elewashy.egyfilm.adapter.DownloadAdapter  onItemClick ,com.elewashy.egyfilm.adapter.DownloadAdapter  onOpenClick ,com.elewashy.egyfilm.adapter.DownloadAdapter  onPauseClick ,com.elewashy.egyfilm.adapter.DownloadAdapter  
onResumeClick ,com.elewashy.egyfilm.adapter.DownloadAdapter  onRetryClick ,com.elewashy.egyfilm.adapter.DownloadAdapter  
submitList ,com.elewashy.egyfilm.adapter.DownloadAdapter  ItemCallback 5com.elewashy.egyfilm.adapter.DownloadAdapter.DiffUtil  DownloadStatus ?com.elewashy.egyfilm.adapter.DownloadAdapter.DownloadViewHolder  R ?com.elewashy.egyfilm.adapter.DownloadAdapter.DownloadViewHolder  String ?com.elewashy.egyfilm.adapter.DownloadAdapter.DownloadViewHolder  View ?com.elewashy.egyfilm.adapter.DownloadAdapter.DownloadViewHolder  bind ?com.elewashy.egyfilm.adapter.DownloadAdapter.DownloadViewHolder  binding ?com.elewashy.egyfilm.adapter.DownloadAdapter.DownloadViewHolder  context ?com.elewashy.egyfilm.adapter.DownloadAdapter.DownloadViewHolder  format ?com.elewashy.egyfilm.adapter.DownloadAdapter.DownloadViewHolder  
onCancelClick ?com.elewashy.egyfilm.adapter.DownloadAdapter.DownloadViewHolder  
onDeleteClick ?com.elewashy.egyfilm.adapter.DownloadAdapter.DownloadViewHolder  onItemClick ?com.elewashy.egyfilm.adapter.DownloadAdapter.DownloadViewHolder  onOpenClick ?com.elewashy.egyfilm.adapter.DownloadAdapter.DownloadViewHolder  onPauseClick ?com.elewashy.egyfilm.adapter.DownloadAdapter.DownloadViewHolder  
onResumeClick ?com.elewashy.egyfilm.adapter.DownloadAdapter.DownloadViewHolder  onRetryClick ?com.elewashy.egyfilm.adapter.DownloadAdapter.DownloadViewHolder  
ViewHolder 9com.elewashy.egyfilm.adapter.DownloadAdapter.RecyclerView  
ViewHolder )com.elewashy.egyfilm.adapter.RecyclerView  ActivityDownloadBinding  com.elewashy.egyfilm.databinding  ActivityFilterUpdatesBinding  com.elewashy.egyfilm.databinding  ActivityMainBinding  com.elewashy.egyfilm.databinding  ActivitySettingsBinding  com.elewashy.egyfilm.databinding  FragmentBrowseBinding  com.elewashy.egyfilm.databinding  ItemDownloadBinding  com.elewashy.egyfilm.databinding  inflate 8com.elewashy.egyfilm.databinding.ActivityDownloadBinding  layoutNoDownloads 8com.elewashy.egyfilm.databinding.ActivityDownloadBinding  root 8com.elewashy.egyfilm.databinding.ActivityDownloadBinding  rvDownloads 8com.elewashy.egyfilm.databinding.ActivityDownloadBinding  toolbarDownloads 8com.elewashy.egyfilm.databinding.ActivityDownloadBinding  adBlockerLastUpdate =com.elewashy.egyfilm.databinding.ActivityFilterUpdatesBinding  adBlockerProgress =com.elewashy.egyfilm.databinding.ActivityFilterUpdatesBinding  adBlockerStatus =com.elewashy.egyfilm.databinding.ActivityFilterUpdatesBinding  
backButton =com.elewashy.egyfilm.databinding.ActivityFilterUpdatesBinding  inflate =com.elewashy.egyfilm.databinding.ActivityFilterUpdatesBinding  openLinksLastUpdate =com.elewashy.egyfilm.databinding.ActivityFilterUpdatesBinding  openLinksProgress =com.elewashy.egyfilm.databinding.ActivityFilterUpdatesBinding  openLinksStatus =com.elewashy.egyfilm.databinding.ActivityFilterUpdatesBinding  root =com.elewashy.egyfilm.databinding.ActivityFilterUpdatesBinding  updateAdBlockerBtn =com.elewashy.egyfilm.databinding.ActivityFilterUpdatesBinding  updateAllFiltersBtn =com.elewashy.egyfilm.databinding.ActivityFilterUpdatesBinding  updateOpenLinksBtn =com.elewashy.egyfilm.databinding.ActivityFilterUpdatesBinding  updateValidLinksBtn =com.elewashy.egyfilm.databinding.ActivityFilterUpdatesBinding  validLinksLastUpdate =com.elewashy.egyfilm.databinding.ActivityFilterUpdatesBinding  validLinksProgress =com.elewashy.egyfilm.databinding.ActivityFilterUpdatesBinding  validLinksStatus =com.elewashy.egyfilm.databinding.ActivityFilterUpdatesBinding  
backButton 4com.elewashy.egyfilm.databinding.ActivityMainBinding  goBtn 4com.elewashy.egyfilm.databinding.ActivityMainBinding  inflate 4com.elewashy.egyfilm.databinding.ActivityMainBinding  moreOptionsBtn 4com.elewashy.egyfilm.databinding.ActivityMainBinding  progressBar 4com.elewashy.egyfilm.databinding.ActivityMainBinding  
refreshBtn 4com.elewashy.egyfilm.databinding.ActivityMainBinding  root 4com.elewashy.egyfilm.databinding.ActivityMainBinding  topSearchBar 4com.elewashy.egyfilm.databinding.ActivityMainBinding  webIcon 4com.elewashy.egyfilm.databinding.ActivityMainBinding  
backButton 8com.elewashy.egyfilm.databinding.ActivitySettingsBinding  filterUpdatesCard 8com.elewashy.egyfilm.databinding.ActivitySettingsBinding  inflate 8com.elewashy.egyfilm.databinding.ActivitySettingsBinding  root 8com.elewashy.egyfilm.databinding.ActivitySettingsBinding  bind 6com.elewashy.egyfilm.databinding.FragmentBrowseBinding  
customView 6com.elewashy.egyfilm.databinding.FragmentBrowseBinding  root 6com.elewashy.egyfilm.databinding.FragmentBrowseBinding  swipeRefreshLayout 6com.elewashy.egyfilm.databinding.FragmentBrowseBinding  webView 6com.elewashy.egyfilm.databinding.FragmentBrowseBinding  	btnCancel 4com.elewashy.egyfilm.databinding.ItemDownloadBinding  btnPauseResume 4com.elewashy.egyfilm.databinding.ItemDownloadBinding  inflate 4com.elewashy.egyfilm.databinding.ItemDownloadBinding  
ivFileIcon 4com.elewashy.egyfilm.databinding.ItemDownloadBinding  pbDownloadProgress 4com.elewashy.egyfilm.databinding.ItemDownloadBinding  root 4com.elewashy.egyfilm.databinding.ItemDownloadBinding  tvDownloadSpeed 4com.elewashy.egyfilm.databinding.ItemDownloadBinding  tvDownloadStatus 4com.elewashy.egyfilm.databinding.ItemDownloadBinding  
tvFileName 4com.elewashy.egyfilm.databinding.ItemDownloadBinding  ActivityInfo com.elewashy.egyfilm.fragment  	AdBlocker com.elewashy.egyfilm.fragment  
AtomicInteger com.elewashy.egyfilm.fragment  Base64 com.elewashy.egyfilm.fragment  Bitmap com.elewashy.egyfilm.fragment  
BitmapFactory com.elewashy.egyfilm.fragment  Boolean com.elewashy.egyfilm.fragment  BottomSheetDialogFragment com.elewashy.egyfilm.fragment  BrowseFragment com.elewashy.egyfilm.fragment  Build com.elewashy.egyfilm.fragment  Bundle com.elewashy.egyfilm.fragment  ByteArrayInputStream com.elewashy.egyfilm.fragment  Callable com.elewashy.egyfilm.fragment  Color com.elewashy.egyfilm.fragment  
ColorDrawable com.elewashy.egyfilm.fragment  
ContentValues com.elewashy.egyfilm.fragment  Context com.elewashy.egyfilm.fragment  ContextMenu com.elewashy.egyfilm.fragment  
CookieManager com.elewashy.egyfilm.fragment  CoroutineScope com.elewashy.egyfilm.fragment  CustomViewCallback com.elewashy.egyfilm.fragment  
Deprecated com.elewashy.egyfilm.fragment  Dispatchers com.elewashy.egyfilm.fragment  DownloadActivity com.elewashy.egyfilm.fragment  DownloadService com.elewashy.egyfilm.fragment  Environment com.elewashy.egyfilm.fragment  	Exception com.elewashy.egyfilm.fragment  	Executors com.elewashy.egyfilm.fragment  File com.elewashy.egyfilm.fragment  Fragment com.elewashy.egyfilm.fragment  FragmentBrowseBinding com.elewashy.egyfilm.fragment  Handler com.elewashy.egyfilm.fragment  HttpURLConnection com.elewashy.egyfilm.fragment  Int com.elewashy.egyfilm.fragment  Intent com.elewashy.egyfilm.fragment  InterruptedException com.elewashy.egyfilm.fragment  
JSONObject com.elewashy.egyfilm.fragment  Job com.elewashy.egyfilm.fragment  JsonDownloader com.elewashy.egyfilm.fragment  LayoutInflater com.elewashy.egyfilm.fragment  ListMetadata com.elewashy.egyfilm.fragment  Looper com.elewashy.egyfilm.fragment  MainActivity com.elewashy.egyfilm.fragment  MaterialAlertDialogBuilder com.elewashy.egyfilm.fragment  MaterialButton com.elewashy.egyfilm.fragment  
MediaStore com.elewashy.egyfilm.fragment  MenuItem com.elewashy.egyfilm.fragment  MoreOptionsBottomSheetFragment com.elewashy.egyfilm.fragment  MotionEvent com.elewashy.egyfilm.fragment  NotificationChannel com.elewashy.egyfilm.fragment  NotificationCompat com.elewashy.egyfilm.fragment  NotificationManager com.elewashy.egyfilm.fragment  OpenLinkValidator com.elewashy.egyfilm.fragment  Pattern com.elewashy.egyfilm.fragment  R com.elewashy.egyfilm.fragment  
ReentrantLock com.elewashy.egyfilm.fragment  
RegexPatterns com.elewashy.egyfilm.fragment  	Resources com.elewashy.egyfilm.fragment  Set com.elewashy.egyfilm.fragment  SettingsActivity com.elewashy.egyfilm.fragment  ShapeableImageView com.elewashy.egyfilm.fragment  ShareCompat com.elewashy.egyfilm.fragment  Snackbar com.elewashy.egyfilm.fragment  SpannableStringBuilder com.elewashy.egyfilm.fragment  String com.elewashy.egyfilm.fragment  Suppress com.elewashy.egyfilm.fragment  SuppressLint com.elewashy.egyfilm.fragment  System com.elewashy.egyfilm.fragment  Thread com.elewashy.egyfilm.fragment  TimeUnit com.elewashy.egyfilm.fragment  Toast com.elewashy.egyfilm.fragment  Triple com.elewashy.egyfilm.fragment  URL com.elewashy.egyfilm.fragment  
URLDecoder com.elewashy.egyfilm.fragment  URLUtil com.elewashy.egyfilm.fragment  Uri com.elewashy.egyfilm.fragment  ValidLinkChecker com.elewashy.egyfilm.fragment  View com.elewashy.egyfilm.fragment  	ViewGroup com.elewashy.egyfilm.fragment  Volatile com.elewashy.egyfilm.fragment  WebChromeClient com.elewashy.egyfilm.fragment  WebResourceRequest com.elewashy.egyfilm.fragment  WebResourceResponse com.elewashy.egyfilm.fragment  WebSettings com.elewashy.egyfilm.fragment  WebView com.elewashy.egyfilm.fragment  
WebViewClient com.elewashy.egyfilm.fragment  also com.elewashy.egyfilm.fragment  any com.elewashy.egyfilm.fragment  apply com.elewashy.egyfilm.fragment  binding com.elewashy.egyfilm.fragment  bufferedReader com.elewashy.egyfilm.fragment  contains com.elewashy.egyfilm.fragment  context com.elewashy.egyfilm.fragment  createStartIntent com.elewashy.egyfilm.fragment  emptySet com.elewashy.egyfilm.fragment  endsWith com.elewashy.egyfilm.fragment  filter com.elewashy.egyfilm.fragment  	filterNot com.elewashy.egyfilm.fragment  flatMap com.elewashy.egyfilm.fragment  forEach com.elewashy.egyfilm.fragment  getDownloadUrl com.elewashy.egyfilm.fragment  getInstance com.elewashy.egyfilm.fragment  indexOf com.elewashy.egyfilm.fragment  
isNotEmpty com.elewashy.egyfilm.fragment  java com.elewashy.egyfilm.fragment  launch com.elewashy.egyfilm.fragment  let com.elewashy.egyfilm.fragment  lines com.elewashy.egyfilm.fragment  listOf com.elewashy.egyfilm.fragment  map com.elewashy.egyfilm.fragment  mapOf com.elewashy.egyfilm.fragment  matches com.elewashy.egyfilm.fragment  
mutableListOf com.elewashy.egyfilm.fragment  mutableSetOf com.elewashy.egyfilm.fragment  plus com.elewashy.egyfilm.fragment  println com.elewashy.egyfilm.fragment  readText com.elewashy.egyfilm.fragment  replace com.elewashy.egyfilm.fragment  requireActivity com.elewashy.egyfilm.fragment  requireContext com.elewashy.egyfilm.fragment  run com.elewashy.egyfilm.fragment  	showToast com.elewashy.egyfilm.fragment  split com.elewashy.egyfilm.fragment  
startsWith com.elewashy.egyfilm.fragment  	substring com.elewashy.egyfilm.fragment  substringAfterLast com.elewashy.egyfilm.fragment  synchronized com.elewashy.egyfilm.fragment  to com.elewashy.egyfilm.fragment  toByteArray com.elewashy.egyfilm.fragment  toRegex com.elewashy.egyfilm.fragment  toSet com.elewashy.egyfilm.fragment  
trimIndent com.elewashy.egyfilm.fragment  until com.elewashy.egyfilm.fragment  use com.elewashy.egyfilm.fragment  webIcon com.elewashy.egyfilm.fragment  withContext com.elewashy.egyfilm.fragment  withLock com.elewashy.egyfilm.fragment  	AdBlocker 'com.elewashy.egyfilm.fragment.AdBlocker  
AtomicInteger 'com.elewashy.egyfilm.fragment.AdBlocker  Boolean 'com.elewashy.egyfilm.fragment.AdBlocker  Build 'com.elewashy.egyfilm.fragment.AdBlocker  
CHANNEL_ID 'com.elewashy.egyfilm.fragment.AdBlocker  Callable 'com.elewashy.egyfilm.fragment.AdBlocker  	Companion 'com.elewashy.egyfilm.fragment.AdBlocker  Context 'com.elewashy.egyfilm.fragment.AdBlocker  	Exception 'com.elewashy.egyfilm.fragment.AdBlocker  	Executors 'com.elewashy.egyfilm.fragment.AdBlocker  FREQUENT_HOURS_MS 'com.elewashy.egyfilm.fragment.AdBlocker  FREQUENT_UPDATE_URL 'com.elewashy.egyfilm.fragment.AdBlocker  HOURS_MS 'com.elewashy.egyfilm.fragment.AdBlocker  Handler 'com.elewashy.egyfilm.fragment.AdBlocker  InterruptedException 'com.elewashy.egyfilm.fragment.AdBlocker  ListMetadata 'com.elewashy.egyfilm.fragment.AdBlocker  Looper 'com.elewashy.egyfilm.fragment.AdBlocker  NOTIFICATION_EASYLIST_UPDATE 'com.elewashy.egyfilm.fragment.AdBlocker  NOTIFICATION_FREQUENT_UPDATE 'com.elewashy.egyfilm.fragment.AdBlocker  NotificationChannel 'com.elewashy.egyfilm.fragment.AdBlocker  NotificationCompat 'com.elewashy.egyfilm.fragment.AdBlocker  NotificationManager 'com.elewashy.egyfilm.fragment.AdBlocker  R 'com.elewashy.egyfilm.fragment.AdBlocker  
ReentrantLock 'com.elewashy.egyfilm.fragment.AdBlocker  Set 'com.elewashy.egyfilm.fragment.AdBlocker  String 'com.elewashy.egyfilm.fragment.AdBlocker  System 'com.elewashy.egyfilm.fragment.AdBlocker  Thread 'com.elewashy.egyfilm.fragment.AdBlocker  TimeUnit 'com.elewashy.egyfilm.fragment.AdBlocker  Triple 'com.elewashy.egyfilm.fragment.AdBlocker  URL 'com.elewashy.egyfilm.fragment.AdBlocker  Uri 'com.elewashy.egyfilm.fragment.AdBlocker  Volatile 'com.elewashy.egyfilm.fragment.AdBlocker  adHosts 'com.elewashy.egyfilm.fragment.AdBlocker  also 'com.elewashy.egyfilm.fragment.AdBlocker  any 'com.elewashy.egyfilm.fragment.AdBlocker  apply 'com.elewashy.egyfilm.fragment.AdBlocker  bufferedReader 'com.elewashy.egyfilm.fragment.AdBlocker  checkIfListModified 'com.elewashy.egyfilm.fragment.AdBlocker  contains 'com.elewashy.egyfilm.fragment.AdBlocker  context 'com.elewashy.egyfilm.fragment.AdBlocker  createNotificationChannel 'com.elewashy.egyfilm.fragment.AdBlocker  emptySet 'com.elewashy.egyfilm.fragment.AdBlocker  filter 'com.elewashy.egyfilm.fragment.AdBlocker  	filterNot 'com.elewashy.egyfilm.fragment.AdBlocker  flatMap 'com.elewashy.egyfilm.fragment.AdBlocker  getInstance 'com.elewashy.egyfilm.fragment.AdBlocker  getStoredMetadata 'com.elewashy.egyfilm.fragment.AdBlocker  instance 'com.elewashy.egyfilm.fragment.AdBlocker  isAd 'com.elewashy.egyfilm.fragment.AdBlocker  
isNotEmpty 'com.elewashy.egyfilm.fragment.AdBlocker  java 'com.elewashy.egyfilm.fragment.AdBlocker  lines 'com.elewashy.egyfilm.fragment.AdBlocker  listOf 'com.elewashy.egyfilm.fragment.AdBlocker  loadLocalHosts 'com.elewashy.egyfilm.fragment.AdBlocker  lock 'com.elewashy.egyfilm.fragment.AdBlocker  map 'com.elewashy.egyfilm.fragment.AdBlocker  mutableSetOf 'com.elewashy.egyfilm.fragment.AdBlocker  notificationManager 'com.elewashy.egyfilm.fragment.AdBlocker  
parseEasyList 'com.elewashy.egyfilm.fragment.AdBlocker  plus 'com.elewashy.egyfilm.fragment.AdBlocker  readText 'com.elewashy.egyfilm.fragment.AdBlocker  saveLocalHosts 'com.elewashy.egyfilm.fragment.AdBlocker  saveMetadata 'com.elewashy.egyfilm.fragment.AdBlocker  sharedPreferences 'com.elewashy.egyfilm.fragment.AdBlocker  split 'com.elewashy.egyfilm.fragment.AdBlocker  startBackgroundUpdate 'com.elewashy.egyfilm.fragment.AdBlocker  
startsWith 'com.elewashy.egyfilm.fragment.AdBlocker  synchronized 'com.elewashy.egyfilm.fragment.AdBlocker  toSet 'com.elewashy.egyfilm.fragment.AdBlocker  updateEasyList 'com.elewashy.egyfilm.fragment.AdBlocker  updateFrequentList 'com.elewashy.egyfilm.fragment.AdBlocker  use 'com.elewashy.egyfilm.fragment.AdBlocker  withLock 'com.elewashy.egyfilm.fragment.AdBlocker  	AdBlocker 1com.elewashy.egyfilm.fragment.AdBlocker.Companion  
AtomicInteger 1com.elewashy.egyfilm.fragment.AdBlocker.Companion  Build 1com.elewashy.egyfilm.fragment.AdBlocker.Companion  Callable 1com.elewashy.egyfilm.fragment.AdBlocker.Companion  Context 1com.elewashy.egyfilm.fragment.AdBlocker.Companion  	Executors 1com.elewashy.egyfilm.fragment.AdBlocker.Companion  Handler 1com.elewashy.egyfilm.fragment.AdBlocker.Companion  ListMetadata 1com.elewashy.egyfilm.fragment.AdBlocker.Companion  Looper 1com.elewashy.egyfilm.fragment.AdBlocker.Companion  NotificationChannel 1com.elewashy.egyfilm.fragment.AdBlocker.Companion  NotificationCompat 1com.elewashy.egyfilm.fragment.AdBlocker.Companion  NotificationManager 1com.elewashy.egyfilm.fragment.AdBlocker.Companion  R 1com.elewashy.egyfilm.fragment.AdBlocker.Companion  
ReentrantLock 1com.elewashy.egyfilm.fragment.AdBlocker.Companion  System 1com.elewashy.egyfilm.fragment.AdBlocker.Companion  Thread 1com.elewashy.egyfilm.fragment.AdBlocker.Companion  TimeUnit 1com.elewashy.egyfilm.fragment.AdBlocker.Companion  Triple 1com.elewashy.egyfilm.fragment.AdBlocker.Companion  URL 1com.elewashy.egyfilm.fragment.AdBlocker.Companion  Uri 1com.elewashy.egyfilm.fragment.AdBlocker.Companion  also 1com.elewashy.egyfilm.fragment.AdBlocker.Companion  any 1com.elewashy.egyfilm.fragment.AdBlocker.Companion  apply 1com.elewashy.egyfilm.fragment.AdBlocker.Companion  bufferedReader 1com.elewashy.egyfilm.fragment.AdBlocker.Companion  contains 1com.elewashy.egyfilm.fragment.AdBlocker.Companion  emptySet 1com.elewashy.egyfilm.fragment.AdBlocker.Companion  filter 1com.elewashy.egyfilm.fragment.AdBlocker.Companion  	filterNot 1com.elewashy.egyfilm.fragment.AdBlocker.Companion  flatMap 1com.elewashy.egyfilm.fragment.AdBlocker.Companion  getInstance 1com.elewashy.egyfilm.fragment.AdBlocker.Companion  instance 1com.elewashy.egyfilm.fragment.AdBlocker.Companion  
isNotEmpty 1com.elewashy.egyfilm.fragment.AdBlocker.Companion  lines 1com.elewashy.egyfilm.fragment.AdBlocker.Companion  listOf 1com.elewashy.egyfilm.fragment.AdBlocker.Companion  lock 1com.elewashy.egyfilm.fragment.AdBlocker.Companion  map 1com.elewashy.egyfilm.fragment.AdBlocker.Companion  mutableSetOf 1com.elewashy.egyfilm.fragment.AdBlocker.Companion  plus 1com.elewashy.egyfilm.fragment.AdBlocker.Companion  readText 1com.elewashy.egyfilm.fragment.AdBlocker.Companion  split 1com.elewashy.egyfilm.fragment.AdBlocker.Companion  
startsWith 1com.elewashy.egyfilm.fragment.AdBlocker.Companion  synchronized 1com.elewashy.egyfilm.fragment.AdBlocker.Companion  toSet 1com.elewashy.egyfilm.fragment.AdBlocker.Companion  use 1com.elewashy.egyfilm.fragment.AdBlocker.Companion  withLock 1com.elewashy.egyfilm.fragment.AdBlocker.Companion  etag 4com.elewashy.egyfilm.fragment.AdBlocker.ListMetadata  lastModified 4com.elewashy.egyfilm.fragment.AdBlocker.ListMetadata  net ,com.elewashy.egyfilm.fragment.AdBlocker.java  HttpURLConnection 0com.elewashy.egyfilm.fragment.AdBlocker.java.net  ActivityInfo ,com.elewashy.egyfilm.fragment.BrowseFragment  	AdBlocker ,com.elewashy.egyfilm.fragment.BrowseFragment  Base64 ,com.elewashy.egyfilm.fragment.BrowseFragment  Bitmap ,com.elewashy.egyfilm.fragment.BrowseFragment  
BitmapFactory ,com.elewashy.egyfilm.fragment.BrowseFragment  Build ,com.elewashy.egyfilm.fragment.BrowseFragment  ByteArrayInputStream ,com.elewashy.egyfilm.fragment.BrowseFragment  Color ,com.elewashy.egyfilm.fragment.BrowseFragment  
ColorDrawable ,com.elewashy.egyfilm.fragment.BrowseFragment  
ContentValues ,com.elewashy.egyfilm.fragment.BrowseFragment  
CookieManager ,com.elewashy.egyfilm.fragment.BrowseFragment  DownloadService ,com.elewashy.egyfilm.fragment.BrowseFragment  Environment ,com.elewashy.egyfilm.fragment.BrowseFragment  FragmentBrowseBinding ,com.elewashy.egyfilm.fragment.BrowseFragment  Handler ,com.elewashy.egyfilm.fragment.BrowseFragment  Intent ,com.elewashy.egyfilm.fragment.BrowseFragment  JsonDownloader ,com.elewashy.egyfilm.fragment.BrowseFragment  Looper ,com.elewashy.egyfilm.fragment.BrowseFragment  MaterialAlertDialogBuilder ,com.elewashy.egyfilm.fragment.BrowseFragment  
MediaStore ,com.elewashy.egyfilm.fragment.BrowseFragment  OpenLinkValidator ,com.elewashy.egyfilm.fragment.BrowseFragment  R ,com.elewashy.egyfilm.fragment.BrowseFragment  
RegexPatterns ,com.elewashy.egyfilm.fragment.BrowseFragment  	Resources ,com.elewashy.egyfilm.fragment.BrowseFragment  ShapeableImageView ,com.elewashy.egyfilm.fragment.BrowseFragment  ShareCompat ,com.elewashy.egyfilm.fragment.BrowseFragment  Snackbar ,com.elewashy.egyfilm.fragment.BrowseFragment  SpannableStringBuilder ,com.elewashy.egyfilm.fragment.BrowseFragment  System ,com.elewashy.egyfilm.fragment.BrowseFragment  Toast ,com.elewashy.egyfilm.fragment.BrowseFragment  
URLDecoder ,com.elewashy.egyfilm.fragment.BrowseFragment  URLUtil ,com.elewashy.egyfilm.fragment.BrowseFragment  Uri ,com.elewashy.egyfilm.fragment.BrowseFragment  ValidLinkChecker ,com.elewashy.egyfilm.fragment.BrowseFragment  View ,com.elewashy.egyfilm.fragment.BrowseFragment  WebResourceResponse ,com.elewashy.egyfilm.fragment.BrowseFragment  WebSettings ,com.elewashy.egyfilm.fragment.BrowseFragment  WebView ,com.elewashy.egyfilm.fragment.BrowseFragment  any ,com.elewashy.egyfilm.fragment.BrowseFragment  apply ,com.elewashy.egyfilm.fragment.BrowseFragment  binding ,com.elewashy.egyfilm.fragment.BrowseFragment  contains ,com.elewashy.egyfilm.fragment.BrowseFragment  createStartIntent ,com.elewashy.egyfilm.fragment.BrowseFragment  getInstance ,com.elewashy.egyfilm.fragment.BrowseFragment  indexOf ,com.elewashy.egyfilm.fragment.BrowseFragment  let ,com.elewashy.egyfilm.fragment.BrowseFragment  listOf ,com.elewashy.egyfilm.fragment.BrowseFragment  matches ,com.elewashy.egyfilm.fragment.BrowseFragment  
mutableListOf ,com.elewashy.egyfilm.fragment.BrowseFragment  registerForContextMenu ,com.elewashy.egyfilm.fragment.BrowseFragment  replace ,com.elewashy.egyfilm.fragment.BrowseFragment  requireActivity ,com.elewashy.egyfilm.fragment.BrowseFragment  requireContext ,com.elewashy.egyfilm.fragment.BrowseFragment  run ,com.elewashy.egyfilm.fragment.BrowseFragment  saveImageToGallery ,com.elewashy.egyfilm.fragment.BrowseFragment  saveImageToGalleryForSharing ,com.elewashy.egyfilm.fragment.BrowseFragment  
startActivity ,com.elewashy.egyfilm.fragment.BrowseFragment  
startsWith ,com.elewashy.egyfilm.fragment.BrowseFragment  	substring ,com.elewashy.egyfilm.fragment.BrowseFragment  toByteArray ,com.elewashy.egyfilm.fragment.BrowseFragment  toRegex ,com.elewashy.egyfilm.fragment.BrowseFragment  
trimIndent ,com.elewashy.egyfilm.fragment.BrowseFragment  urlNew ,com.elewashy.egyfilm.fragment.BrowseFragment  use ,com.elewashy.egyfilm.fragment.BrowseFragment  wasInFullscreen ,com.elewashy.egyfilm.fragment.BrowseFragment  webIcon ,com.elewashy.egyfilm.fragment.BrowseFragment  ContextMenuInfo )com.elewashy.egyfilm.fragment.ContextMenu  Boolean ,com.elewashy.egyfilm.fragment.JsonDownloader  	Companion ,com.elewashy.egyfilm.fragment.JsonDownloader  Context ,com.elewashy.egyfilm.fragment.JsonDownloader  CoroutineScope ,com.elewashy.egyfilm.fragment.JsonDownloader  Dispatchers ,com.elewashy.egyfilm.fragment.JsonDownloader  DownloadService ,com.elewashy.egyfilm.fragment.JsonDownloader  Environment ,com.elewashy.egyfilm.fragment.JsonDownloader  	Exception ,com.elewashy.egyfilm.fragment.JsonDownloader  File ,com.elewashy.egyfilm.fragment.JsonDownloader  Handler ,com.elewashy.egyfilm.fragment.JsonDownloader  HttpURLConnection ,com.elewashy.egyfilm.fragment.JsonDownloader  
JSONObject ,com.elewashy.egyfilm.fragment.JsonDownloader  Job ,com.elewashy.egyfilm.fragment.JsonDownloader  JsonDownloader ,com.elewashy.egyfilm.fragment.JsonDownloader  Looper ,com.elewashy.egyfilm.fragment.JsonDownloader  Pattern ,com.elewashy.egyfilm.fragment.JsonDownloader  String ,com.elewashy.egyfilm.fragment.JsonDownloader  Toast ,com.elewashy.egyfilm.fragment.JsonDownloader  URL ,com.elewashy.egyfilm.fragment.JsonDownloader  Volatile ,com.elewashy.egyfilm.fragment.JsonDownloader  also ,com.elewashy.egyfilm.fragment.JsonDownloader  bufferedReader ,com.elewashy.egyfilm.fragment.JsonDownloader  contains ,com.elewashy.egyfilm.fragment.JsonDownloader  context ,com.elewashy.egyfilm.fragment.JsonDownloader  createStartIntent ,com.elewashy.egyfilm.fragment.JsonDownloader  endsWith ,com.elewashy.egyfilm.fragment.JsonDownloader  getDownloadUrl ,com.elewashy.egyfilm.fragment.JsonDownloader  getInstance ,com.elewashy.egyfilm.fragment.JsonDownloader  
handleJsonUrl ,com.elewashy.egyfilm.fragment.JsonDownloader  instance ,com.elewashy.egyfilm.fragment.JsonDownloader  launch ,com.elewashy.egyfilm.fragment.JsonDownloader  mainHandler ,com.elewashy.egyfilm.fragment.JsonDownloader  mapOf ,com.elewashy.egyfilm.fragment.JsonDownloader  readText ,com.elewashy.egyfilm.fragment.JsonDownloader  run ,com.elewashy.egyfilm.fragment.JsonDownloader  scope ,com.elewashy.egyfilm.fragment.JsonDownloader  	showToast ,com.elewashy.egyfilm.fragment.JsonDownloader  substringAfterLast ,com.elewashy.egyfilm.fragment.JsonDownloader  synchronized ,com.elewashy.egyfilm.fragment.JsonDownloader  to ,com.elewashy.egyfilm.fragment.JsonDownloader  toByteArray ,com.elewashy.egyfilm.fragment.JsonDownloader  until ,com.elewashy.egyfilm.fragment.JsonDownloader  use ,com.elewashy.egyfilm.fragment.JsonDownloader  withContext ,com.elewashy.egyfilm.fragment.JsonDownloader  CoroutineScope 6com.elewashy.egyfilm.fragment.JsonDownloader.Companion  Dispatchers 6com.elewashy.egyfilm.fragment.JsonDownloader.Companion  DownloadService 6com.elewashy.egyfilm.fragment.JsonDownloader.Companion  Environment 6com.elewashy.egyfilm.fragment.JsonDownloader.Companion  File 6com.elewashy.egyfilm.fragment.JsonDownloader.Companion  Handler 6com.elewashy.egyfilm.fragment.JsonDownloader.Companion  HttpURLConnection 6com.elewashy.egyfilm.fragment.JsonDownloader.Companion  
JSONObject 6com.elewashy.egyfilm.fragment.JsonDownloader.Companion  Job 6com.elewashy.egyfilm.fragment.JsonDownloader.Companion  JsonDownloader 6com.elewashy.egyfilm.fragment.JsonDownloader.Companion  Looper 6com.elewashy.egyfilm.fragment.JsonDownloader.Companion  Pattern 6com.elewashy.egyfilm.fragment.JsonDownloader.Companion  Toast 6com.elewashy.egyfilm.fragment.JsonDownloader.Companion  URL 6com.elewashy.egyfilm.fragment.JsonDownloader.Companion  also 6com.elewashy.egyfilm.fragment.JsonDownloader.Companion  bufferedReader 6com.elewashy.egyfilm.fragment.JsonDownloader.Companion  contains 6com.elewashy.egyfilm.fragment.JsonDownloader.Companion  context 6com.elewashy.egyfilm.fragment.JsonDownloader.Companion  createStartIntent 6com.elewashy.egyfilm.fragment.JsonDownloader.Companion  endsWith 6com.elewashy.egyfilm.fragment.JsonDownloader.Companion  getDownloadUrl 6com.elewashy.egyfilm.fragment.JsonDownloader.Companion  getInstance 6com.elewashy.egyfilm.fragment.JsonDownloader.Companion  instance 6com.elewashy.egyfilm.fragment.JsonDownloader.Companion  launch 6com.elewashy.egyfilm.fragment.JsonDownloader.Companion  mapOf 6com.elewashy.egyfilm.fragment.JsonDownloader.Companion  readText 6com.elewashy.egyfilm.fragment.JsonDownloader.Companion  run 6com.elewashy.egyfilm.fragment.JsonDownloader.Companion  	showToast 6com.elewashy.egyfilm.fragment.JsonDownloader.Companion  substringAfterLast 6com.elewashy.egyfilm.fragment.JsonDownloader.Companion  synchronized 6com.elewashy.egyfilm.fragment.JsonDownloader.Companion  to 6com.elewashy.egyfilm.fragment.JsonDownloader.Companion  toByteArray 6com.elewashy.egyfilm.fragment.JsonDownloader.Companion  until 6com.elewashy.egyfilm.fragment.JsonDownloader.Companion  use 6com.elewashy.egyfilm.fragment.JsonDownloader.Companion  withContext 6com.elewashy.egyfilm.fragment.JsonDownloader.Companion  Bundle <com.elewashy.egyfilm.fragment.MoreOptionsBottomSheetFragment  	Companion <com.elewashy.egyfilm.fragment.MoreOptionsBottomSheetFragment  DownloadActivity <com.elewashy.egyfilm.fragment.MoreOptionsBottomSheetFragment  Intent <com.elewashy.egyfilm.fragment.MoreOptionsBottomSheetFragment  LayoutInflater <com.elewashy.egyfilm.fragment.MoreOptionsBottomSheetFragment  MainActivity <com.elewashy.egyfilm.fragment.MoreOptionsBottomSheetFragment  MaterialButton <com.elewashy.egyfilm.fragment.MoreOptionsBottomSheetFragment  R <com.elewashy.egyfilm.fragment.MoreOptionsBottomSheetFragment  SettingsActivity <com.elewashy.egyfilm.fragment.MoreOptionsBottomSheetFragment  TAG <com.elewashy.egyfilm.fragment.MoreOptionsBottomSheetFragment  View <com.elewashy.egyfilm.fragment.MoreOptionsBottomSheetFragment  	ViewGroup <com.elewashy.egyfilm.fragment.MoreOptionsBottomSheetFragment  activity <com.elewashy.egyfilm.fragment.MoreOptionsBottomSheetFragment  dismiss <com.elewashy.egyfilm.fragment.MoreOptionsBottomSheetFragment  java <com.elewashy.egyfilm.fragment.MoreOptionsBottomSheetFragment  show <com.elewashy.egyfilm.fragment.MoreOptionsBottomSheetFragment  
startActivity <com.elewashy.egyfilm.fragment.MoreOptionsBottomSheetFragment  DownloadActivity Fcom.elewashy.egyfilm.fragment.MoreOptionsBottomSheetFragment.Companion  Intent Fcom.elewashy.egyfilm.fragment.MoreOptionsBottomSheetFragment.Companion  R Fcom.elewashy.egyfilm.fragment.MoreOptionsBottomSheetFragment.Companion  SettingsActivity Fcom.elewashy.egyfilm.fragment.MoreOptionsBottomSheetFragment.Companion  TAG Fcom.elewashy.egyfilm.fragment.MoreOptionsBottomSheetFragment.Companion  java Fcom.elewashy.egyfilm.fragment.MoreOptionsBottomSheetFragment.Companion  Boolean /com.elewashy.egyfilm.fragment.OpenLinkValidator  Callable /com.elewashy.egyfilm.fragment.OpenLinkValidator  	Companion /com.elewashy.egyfilm.fragment.OpenLinkValidator  Context /com.elewashy.egyfilm.fragment.OpenLinkValidator  	Exception /com.elewashy.egyfilm.fragment.OpenLinkValidator  	Executors /com.elewashy.egyfilm.fragment.OpenLinkValidator  OpenLinkValidator /com.elewashy.egyfilm.fragment.OpenLinkValidator  Set /com.elewashy.egyfilm.fragment.OpenLinkValidator  String /com.elewashy.egyfilm.fragment.OpenLinkValidator  URL /com.elewashy.egyfilm.fragment.OpenLinkValidator  Uri /com.elewashy.egyfilm.fragment.OpenLinkValidator  Volatile /com.elewashy.egyfilm.fragment.OpenLinkValidator  also /com.elewashy.egyfilm.fragment.OpenLinkValidator  any /com.elewashy.egyfilm.fragment.OpenLinkValidator  contains /com.elewashy.egyfilm.fragment.OpenLinkValidator  emptySet /com.elewashy.egyfilm.fragment.OpenLinkValidator  extractOpenLinks /com.elewashy.egyfilm.fragment.OpenLinkValidator  getInstance /com.elewashy.egyfilm.fragment.OpenLinkValidator  instance /com.elewashy.egyfilm.fragment.OpenLinkValidator  
isNotEmpty /com.elewashy.egyfilm.fragment.OpenLinkValidator  isOpenLinkValid /com.elewashy.egyfilm.fragment.OpenLinkValidator  lines /com.elewashy.egyfilm.fragment.OpenLinkValidator  listOf /com.elewashy.egyfilm.fragment.OpenLinkValidator  loadStoredOpenLinks /com.elewashy.egyfilm.fragment.OpenLinkValidator  map /com.elewashy.egyfilm.fragment.OpenLinkValidator  mutableSetOf /com.elewashy.egyfilm.fragment.OpenLinkValidator  	openLinks /com.elewashy.egyfilm.fragment.OpenLinkValidator  preferences /com.elewashy.egyfilm.fragment.OpenLinkValidator  println /com.elewashy.egyfilm.fragment.OpenLinkValidator  readText /com.elewashy.egyfilm.fragment.OpenLinkValidator  saveOpenLinksLocally /com.elewashy.egyfilm.fragment.OpenLinkValidator  split /com.elewashy.egyfilm.fragment.OpenLinkValidator  
startsWith /com.elewashy.egyfilm.fragment.OpenLinkValidator  synchronized /com.elewashy.egyfilm.fragment.OpenLinkValidator  updateOpenLinks /com.elewashy.egyfilm.fragment.OpenLinkValidator  Callable 9com.elewashy.egyfilm.fragment.OpenLinkValidator.Companion  Context 9com.elewashy.egyfilm.fragment.OpenLinkValidator.Companion  	Executors 9com.elewashy.egyfilm.fragment.OpenLinkValidator.Companion  OpenLinkValidator 9com.elewashy.egyfilm.fragment.OpenLinkValidator.Companion  URL 9com.elewashy.egyfilm.fragment.OpenLinkValidator.Companion  Uri 9com.elewashy.egyfilm.fragment.OpenLinkValidator.Companion  also 9com.elewashy.egyfilm.fragment.OpenLinkValidator.Companion  any 9com.elewashy.egyfilm.fragment.OpenLinkValidator.Companion  contains 9com.elewashy.egyfilm.fragment.OpenLinkValidator.Companion  emptySet 9com.elewashy.egyfilm.fragment.OpenLinkValidator.Companion  getInstance 9com.elewashy.egyfilm.fragment.OpenLinkValidator.Companion  instance 9com.elewashy.egyfilm.fragment.OpenLinkValidator.Companion  
isNotEmpty 9com.elewashy.egyfilm.fragment.OpenLinkValidator.Companion  lines 9com.elewashy.egyfilm.fragment.OpenLinkValidator.Companion  listOf 9com.elewashy.egyfilm.fragment.OpenLinkValidator.Companion  map 9com.elewashy.egyfilm.fragment.OpenLinkValidator.Companion  mutableSetOf 9com.elewashy.egyfilm.fragment.OpenLinkValidator.Companion  println 9com.elewashy.egyfilm.fragment.OpenLinkValidator.Companion  readText 9com.elewashy.egyfilm.fragment.OpenLinkValidator.Companion  split 9com.elewashy.egyfilm.fragment.OpenLinkValidator.Companion  
startsWith 9com.elewashy.egyfilm.fragment.OpenLinkValidator.Companion  synchronized 9com.elewashy.egyfilm.fragment.OpenLinkValidator.Companion  listOf +com.elewashy.egyfilm.fragment.RegexPatterns  patterns +com.elewashy.egyfilm.fragment.RegexPatterns  Boolean .com.elewashy.egyfilm.fragment.ValidLinkChecker  Callable .com.elewashy.egyfilm.fragment.ValidLinkChecker  	Companion .com.elewashy.egyfilm.fragment.ValidLinkChecker  Context .com.elewashy.egyfilm.fragment.ValidLinkChecker  	Exception .com.elewashy.egyfilm.fragment.ValidLinkChecker  	Executors .com.elewashy.egyfilm.fragment.ValidLinkChecker  
ReentrantLock .com.elewashy.egyfilm.fragment.ValidLinkChecker  Set .com.elewashy.egyfilm.fragment.ValidLinkChecker  String .com.elewashy.egyfilm.fragment.ValidLinkChecker  URL .com.elewashy.egyfilm.fragment.ValidLinkChecker  Uri .com.elewashy.egyfilm.fragment.ValidLinkChecker  ValidLinkChecker .com.elewashy.egyfilm.fragment.ValidLinkChecker  Volatile .com.elewashy.egyfilm.fragment.ValidLinkChecker  also .com.elewashy.egyfilm.fragment.ValidLinkChecker  any .com.elewashy.egyfilm.fragment.ValidLinkChecker  contains .com.elewashy.egyfilm.fragment.ValidLinkChecker  emptySet .com.elewashy.egyfilm.fragment.ValidLinkChecker  getInstance .com.elewashy.egyfilm.fragment.ValidLinkChecker  instance .com.elewashy.egyfilm.fragment.ValidLinkChecker  
isNotEmpty .com.elewashy.egyfilm.fragment.ValidLinkChecker  isValidLink .com.elewashy.egyfilm.fragment.ValidLinkChecker  lines .com.elewashy.egyfilm.fragment.ValidLinkChecker  listOf .com.elewashy.egyfilm.fragment.ValidLinkChecker  loadLocalLinks .com.elewashy.egyfilm.fragment.ValidLinkChecker  lock .com.elewashy.egyfilm.fragment.ValidLinkChecker  map .com.elewashy.egyfilm.fragment.ValidLinkChecker  mutableSetOf .com.elewashy.egyfilm.fragment.ValidLinkChecker  
parseLinkList .com.elewashy.egyfilm.fragment.ValidLinkChecker  println .com.elewashy.egyfilm.fragment.ValidLinkChecker  readText .com.elewashy.egyfilm.fragment.ValidLinkChecker  saveLocalLinks .com.elewashy.egyfilm.fragment.ValidLinkChecker  sharedPreferences .com.elewashy.egyfilm.fragment.ValidLinkChecker  split .com.elewashy.egyfilm.fragment.ValidLinkChecker  
startsWith .com.elewashy.egyfilm.fragment.ValidLinkChecker  updateValidLinks .com.elewashy.egyfilm.fragment.ValidLinkChecker  
validLinks .com.elewashy.egyfilm.fragment.ValidLinkChecker  withLock .com.elewashy.egyfilm.fragment.ValidLinkChecker  Callable 8com.elewashy.egyfilm.fragment.ValidLinkChecker.Companion  Context 8com.elewashy.egyfilm.fragment.ValidLinkChecker.Companion  	Executors 8com.elewashy.egyfilm.fragment.ValidLinkChecker.Companion  
ReentrantLock 8com.elewashy.egyfilm.fragment.ValidLinkChecker.Companion  URL 8com.elewashy.egyfilm.fragment.ValidLinkChecker.Companion  Uri 8com.elewashy.egyfilm.fragment.ValidLinkChecker.Companion  ValidLinkChecker 8com.elewashy.egyfilm.fragment.ValidLinkChecker.Companion  also 8com.elewashy.egyfilm.fragment.ValidLinkChecker.Companion  any 8com.elewashy.egyfilm.fragment.ValidLinkChecker.Companion  contains 8com.elewashy.egyfilm.fragment.ValidLinkChecker.Companion  emptySet 8com.elewashy.egyfilm.fragment.ValidLinkChecker.Companion  getInstance 8com.elewashy.egyfilm.fragment.ValidLinkChecker.Companion  instance 8com.elewashy.egyfilm.fragment.ValidLinkChecker.Companion  
isNotEmpty 8com.elewashy.egyfilm.fragment.ValidLinkChecker.Companion  lines 8com.elewashy.egyfilm.fragment.ValidLinkChecker.Companion  listOf 8com.elewashy.egyfilm.fragment.ValidLinkChecker.Companion  lock 8com.elewashy.egyfilm.fragment.ValidLinkChecker.Companion  map 8com.elewashy.egyfilm.fragment.ValidLinkChecker.Companion  mutableSetOf 8com.elewashy.egyfilm.fragment.ValidLinkChecker.Companion  println 8com.elewashy.egyfilm.fragment.ValidLinkChecker.Companion  readText 8com.elewashy.egyfilm.fragment.ValidLinkChecker.Companion  split 8com.elewashy.egyfilm.fragment.ValidLinkChecker.Companion  
startsWith 8com.elewashy.egyfilm.fragment.ValidLinkChecker.Companion  withLock 8com.elewashy.egyfilm.fragment.ValidLinkChecker.Companion  OnTouchListener "com.elewashy.egyfilm.fragment.View  net "com.elewashy.egyfilm.fragment.java  HttpURLConnection &com.elewashy.egyfilm.fragment.java.net  DownloadItem com.elewashy.egyfilm.model  DownloadStatus com.elewashy.egyfilm.model  Int com.elewashy.egyfilm.model  Long com.elewashy.egyfilm.model  String com.elewashy.egyfilm.model  System com.elewashy.egyfilm.model  UpdateResponse com.elewashy.egyfilm.model  DownloadStatus 'com.elewashy.egyfilm.model.DownloadItem  cookies 'com.elewashy.egyfilm.model.DownloadItem  copy 'com.elewashy.egyfilm.model.DownloadItem  	createdAt 'com.elewashy.egyfilm.model.DownloadItem  downloadedBytes 'com.elewashy.egyfilm.model.DownloadItem  failureCount 'com.elewashy.egyfilm.model.DownloadItem  fileName 'com.elewashy.egyfilm.model.DownloadItem  filePath 'com.elewashy.egyfilm.model.DownloadItem  id 'com.elewashy.egyfilm.model.DownloadItem  let 'com.elewashy.egyfilm.model.DownloadItem  mimeType 'com.elewashy.egyfilm.model.DownloadItem  origin 'com.elewashy.egyfilm.model.DownloadItem  progress 'com.elewashy.egyfilm.model.DownloadItem  referer 'com.elewashy.egyfilm.model.DownloadItem  source 'com.elewashy.egyfilm.model.DownloadItem  status 'com.elewashy.egyfilm.model.DownloadItem  
totalBytes 'com.elewashy.egyfilm.model.DownloadItem  url 'com.elewashy.egyfilm.model.DownloadItem  	userAgent 'com.elewashy.egyfilm.model.DownloadItem  	CANCELLED )com.elewashy.egyfilm.model.DownloadStatus  	COMPLETED )com.elewashy.egyfilm.model.DownloadStatus  DOWNLOADING )com.elewashy.egyfilm.model.DownloadStatus  FAILED )com.elewashy.egyfilm.model.DownloadStatus  PAUSED )com.elewashy.egyfilm.model.DownloadStatus  PENDING )com.elewashy.egyfilm.model.DownloadStatus  apk_url )com.elewashy.egyfilm.model.UpdateResponse  latest_version )com.elewashy.egyfilm.model.UpdateResponse  let )com.elewashy.egyfilm.model.UpdateResponse  update_message )com.elewashy.egyfilm.model.UpdateResponse  ACTION_CANCEL_DOWNLOAD com.elewashy.egyfilm.service  ACTION_CHECK_FAILED_DOWNLOADS com.elewashy.egyfilm.service  ACTION_DOWNLOAD_UPDATE com.elewashy.egyfilm.service  ACTION_PAUSE_DOWNLOAD com.elewashy.egyfilm.service  ACTION_RESUME_DOWNLOAD com.elewashy.egyfilm.service  ACTION_START_DOWNLOAD com.elewashy.egyfilm.service  AUTO_RETRY_INTERVAL_MS com.elewashy.egyfilm.service  AlarmManager com.elewashy.egyfilm.service  
AtomicInteger com.elewashy.egyfilm.service  
AtomicLong com.elewashy.egyfilm.service  Binder com.elewashy.egyfilm.service  Boolean com.elewashy.egyfilm.service  Build com.elewashy.egyfilm.service  	ByteArray com.elewashy.egyfilm.service  CHANNEL_DESCRIPTION com.elewashy.egyfilm.service  
CHANNEL_ID com.elewashy.egyfilm.service  CHANNEL_NAME com.elewashy.egyfilm.service  CancellationException com.elewashy.egyfilm.service  ConcurrentHashMap com.elewashy.egyfilm.service  ConnectivityManager com.elewashy.egyfilm.service  Context com.elewashy.egyfilm.service  CoroutineScope com.elewashy.egyfilm.service  Dispatchers com.elewashy.egyfilm.service  DownloadActivity com.elewashy.egyfilm.service  DownloadItem com.elewashy.egyfilm.service  DownloadService com.elewashy.egyfilm.service  DownloadStatus com.elewashy.egyfilm.service  
EXTRA_COOKIES com.elewashy.egyfilm.service  EXTRA_DOWNLOAD_ID com.elewashy.egyfilm.service  EXTRA_DOWNLOAD_ID_UPDATE com.elewashy.egyfilm.service  EXTRA_DOWNLOAD_LIST_CHANGED com.elewashy.egyfilm.service  EXTRA_FILE_NAME com.elewashy.egyfilm.service  EXTRA_MIME_TYPE com.elewashy.egyfilm.service  EXTRA_ORIGIN com.elewashy.egyfilm.service  
EXTRA_REFERER com.elewashy.egyfilm.service  EXTRA_SOURCE com.elewashy.egyfilm.service  	EXTRA_URL com.elewashy.egyfilm.service  EXTRA_USER_AGENT com.elewashy.egyfilm.service  Environment com.elewashy.egyfilm.service  	Exception com.elewashy.egyfilm.service  File com.elewashy.egyfilm.service  FileOutputStream com.elewashy.egyfilm.service  FileProvider com.elewashy.egyfilm.service  FirebaseMessagingService com.elewashy.egyfilm.service  Gson com.elewashy.egyfilm.service  HttpURLConnection com.elewashy.egyfilm.service  IBinder com.elewashy.egyfilm.service  IOException com.elewashy.egyfilm.service  IllegalArgumentException com.elewashy.egyfilm.service  InputStream com.elewashy.egyfilm.service  Int com.elewashy.egyfilm.service  Intent com.elewashy.egyfilm.service  Job com.elewashy.egyfilm.service  KEY_DOWNLOAD_ITEMS com.elewashy.egyfilm.service  KEY_LAST_DOWNLOAD_ID com.elewashy.egyfilm.service  List com.elewashy.egyfilm.service  LocalBroadcastManager com.elewashy.egyfilm.service  Log com.elewashy.egyfilm.service  Long com.elewashy.egyfilm.service  MAX_CONCURRENT_JSON_DOWNLOADS com.elewashy.egyfilm.service  MAX_FAILURE_COUNT com.elewashy.egyfilm.service  MainActivity com.elewashy.egyfilm.service  MimeTypeMap com.elewashy.egyfilm.service  
MutableMap com.elewashy.egyfilm.service  MyFirebaseMessagingService com.elewashy.egyfilm.service  Network com.elewashy.egyfilm.service  NetworkCapabilities com.elewashy.egyfilm.service  NetworkRequest com.elewashy.egyfilm.service  Notification com.elewashy.egyfilm.service  NotificationChannel com.elewashy.egyfilm.service  NotificationCompat com.elewashy.egyfilm.service  NotificationManager com.elewashy.egyfilm.service  
PREFS_NAME com.elewashy.egyfilm.service  
PendingIntent com.elewashy.egyfilm.service  R com.elewashy.egyfilm.service  Regex com.elewashy.egyfilm.service  
RemoteMessage com.elewashy.egyfilm.service  START_STICKY com.elewashy.egyfilm.service  STOP_FOREGROUND_REMOVE com.elewashy.egyfilm.service  Service com.elewashy.egyfilm.service  SharedPreferences com.elewashy.egyfilm.service  StandardCharsets com.elewashy.egyfilm.service  String com.elewashy.egyfilm.service  
SupervisorJob com.elewashy.egyfilm.service  Suppress com.elewashy.egyfilm.service  Synchronized com.elewashy.egyfilm.service  System com.elewashy.egyfilm.service  TAG com.elewashy.egyfilm.service  	TypeToken com.elewashy.egyfilm.service  URL com.elewashy.egyfilm.service  
URLDecoder com.elewashy.egyfilm.service  UnsupportedEncodingException com.elewashy.egyfilm.service  Uri com.elewashy.egyfilm.service  any com.elewashy.egyfilm.service  apply com.elewashy.egyfilm.service  
coerceAtLeast com.elewashy.egyfilm.service  	compareBy com.elewashy.egyfilm.service  contains com.elewashy.egyfilm.service  createControlIntent com.elewashy.egyfilm.service  
downloadItems com.elewashy.egyfilm.service  downloadJobs com.elewashy.egyfilm.service  endsWith com.elewashy.egyfilm.service  failedDownloads com.elewashy.egyfilm.service  filter com.elewashy.egyfilm.service  forEach com.elewashy.egyfilm.service  format com.elewashy.egyfilm.service  getCleanFileName com.elewashy.egyfilm.service  isActive com.elewashy.egyfilm.service  isBlank com.elewashy.egyfilm.service  isEmpty com.elewashy.egyfilm.service  isNetworkAvailable com.elewashy.egyfilm.service  
isNotEmpty com.elewashy.egyfilm.service  java com.elewashy.egyfilm.service  lastIndexOf com.elewashy.egyfilm.service  launch com.elewashy.egyfilm.service  let com.elewashy.egyfilm.service  	lowercase com.elewashy.egyfilm.service  map com.elewashy.egyfilm.service  minByOrNull com.elewashy.egyfilm.service  
plusAssign com.elewashy.egyfilm.service  removeSurrounding com.elewashy.egyfilm.service  replace com.elewashy.egyfilm.service  
retryAttempts com.elewashy.egyfilm.service  retryFailedDownloads com.elewashy.egyfilm.service  saveDownloadState com.elewashy.egyfilm.service  serviceScope com.elewashy.egyfilm.service  set com.elewashy.egyfilm.service  
sortedWith com.elewashy.egyfilm.service  split com.elewashy.egyfilm.service  startOrQueueDownload com.elewashy.egyfilm.service  
startsWith com.elewashy.egyfilm.service  	substring com.elewashy.egyfilm.service  substringBeforeLast com.elewashy.egyfilm.service  take com.elewashy.egyfilm.service  thenByDescending com.elewashy.egyfilm.service  toList com.elewashy.egyfilm.service  toLongOrNull com.elewashy.egyfilm.service  trim com.elewashy.egyfilm.service  updateDownloadProgress com.elewashy.egyfilm.service  updateDownloadStatus com.elewashy.egyfilm.service  updateNotification com.elewashy.egyfilm.service  with com.elewashy.egyfilm.service  NetworkCallback 0com.elewashy.egyfilm.service.ConnectivityManager  ACTION_CANCEL_DOWNLOAD ,com.elewashy.egyfilm.service.DownloadService  ACTION_CHECK_FAILED_DOWNLOADS ,com.elewashy.egyfilm.service.DownloadService  ACTION_DOWNLOAD_UPDATE ,com.elewashy.egyfilm.service.DownloadService  ACTION_OPEN_DOWNLOADS ,com.elewashy.egyfilm.service.DownloadService  ACTION_PAUSE_DOWNLOAD ,com.elewashy.egyfilm.service.DownloadService  ACTION_RESUME_DOWNLOAD ,com.elewashy.egyfilm.service.DownloadService  ACTION_START_DOWNLOAD ,com.elewashy.egyfilm.service.DownloadService  AUTO_RETRY_INTERVAL_MS ,com.elewashy.egyfilm.service.DownloadService  AlarmManager ,com.elewashy.egyfilm.service.DownloadService  
AtomicInteger ,com.elewashy.egyfilm.service.DownloadService  
AtomicLong ,com.elewashy.egyfilm.service.DownloadService  Binder ,com.elewashy.egyfilm.service.DownloadService  Boolean ,com.elewashy.egyfilm.service.DownloadService  Build ,com.elewashy.egyfilm.service.DownloadService  	ByteArray ,com.elewashy.egyfilm.service.DownloadService  CancellationException ,com.elewashy.egyfilm.service.DownloadService  	Companion ,com.elewashy.egyfilm.service.DownloadService  ConcurrentHashMap ,com.elewashy.egyfilm.service.DownloadService  ConnectivityManager ,com.elewashy.egyfilm.service.DownloadService  Context ,com.elewashy.egyfilm.service.DownloadService  CoroutineScope ,com.elewashy.egyfilm.service.DownloadService  Dispatchers ,com.elewashy.egyfilm.service.DownloadService  DownloadActivity ,com.elewashy.egyfilm.service.DownloadService  DownloadBinder ,com.elewashy.egyfilm.service.DownloadService  DownloadItem ,com.elewashy.egyfilm.service.DownloadService  DownloadService ,com.elewashy.egyfilm.service.DownloadService  DownloadStatus ,com.elewashy.egyfilm.service.DownloadService  
EXTRA_COOKIES ,com.elewashy.egyfilm.service.DownloadService  EXTRA_DOWNLOAD_ID ,com.elewashy.egyfilm.service.DownloadService  EXTRA_DOWNLOAD_ID_UPDATE ,com.elewashy.egyfilm.service.DownloadService  EXTRA_DOWNLOAD_LIST_CHANGED ,com.elewashy.egyfilm.service.DownloadService  EXTRA_FILE_NAME ,com.elewashy.egyfilm.service.DownloadService  EXTRA_MIME_TYPE ,com.elewashy.egyfilm.service.DownloadService  EXTRA_ORIGIN ,com.elewashy.egyfilm.service.DownloadService  
EXTRA_REFERER ,com.elewashy.egyfilm.service.DownloadService  EXTRA_SOURCE ,com.elewashy.egyfilm.service.DownloadService  	EXTRA_URL ,com.elewashy.egyfilm.service.DownloadService  EXTRA_USER_AGENT ,com.elewashy.egyfilm.service.DownloadService  Environment ,com.elewashy.egyfilm.service.DownloadService  	Exception ,com.elewashy.egyfilm.service.DownloadService  File ,com.elewashy.egyfilm.service.DownloadService  FileOutputStream ,com.elewashy.egyfilm.service.DownloadService  FileProvider ,com.elewashy.egyfilm.service.DownloadService  Gson ,com.elewashy.egyfilm.service.DownloadService  HttpURLConnection ,com.elewashy.egyfilm.service.DownloadService  IBinder ,com.elewashy.egyfilm.service.DownloadService  IOException ,com.elewashy.egyfilm.service.DownloadService  IllegalArgumentException ,com.elewashy.egyfilm.service.DownloadService  InputStream ,com.elewashy.egyfilm.service.DownloadService  Int ,com.elewashy.egyfilm.service.DownloadService  Intent ,com.elewashy.egyfilm.service.DownloadService  Job ,com.elewashy.egyfilm.service.DownloadService  KEY_DOWNLOAD_ITEMS ,com.elewashy.egyfilm.service.DownloadService  KEY_LAST_DOWNLOAD_ID ,com.elewashy.egyfilm.service.DownloadService  List ,com.elewashy.egyfilm.service.DownloadService  LocalBroadcastManager ,com.elewashy.egyfilm.service.DownloadService  Log ,com.elewashy.egyfilm.service.DownloadService  Long ,com.elewashy.egyfilm.service.DownloadService  MAX_CONCURRENT_JSON_DOWNLOADS ,com.elewashy.egyfilm.service.DownloadService  MAX_FAILURE_COUNT ,com.elewashy.egyfilm.service.DownloadService  MimeTypeMap ,com.elewashy.egyfilm.service.DownloadService  Network ,com.elewashy.egyfilm.service.DownloadService  NetworkCapabilities ,com.elewashy.egyfilm.service.DownloadService  NetworkRequest ,com.elewashy.egyfilm.service.DownloadService  Notification ,com.elewashy.egyfilm.service.DownloadService  NotificationChannel ,com.elewashy.egyfilm.service.DownloadService  NotificationCompat ,com.elewashy.egyfilm.service.DownloadService  NotificationManager ,com.elewashy.egyfilm.service.DownloadService  
PREFS_NAME ,com.elewashy.egyfilm.service.DownloadService  
PendingIntent ,com.elewashy.egyfilm.service.DownloadService  R ,com.elewashy.egyfilm.service.DownloadService  Regex ,com.elewashy.egyfilm.service.DownloadService  START_STICKY ,com.elewashy.egyfilm.service.DownloadService  STOP_FOREGROUND_REMOVE ,com.elewashy.egyfilm.service.DownloadService  SharedPreferences ,com.elewashy.egyfilm.service.DownloadService  StandardCharsets ,com.elewashy.egyfilm.service.DownloadService  String ,com.elewashy.egyfilm.service.DownloadService  
SupervisorJob ,com.elewashy.egyfilm.service.DownloadService  Suppress ,com.elewashy.egyfilm.service.DownloadService  Synchronized ,com.elewashy.egyfilm.service.DownloadService  System ,com.elewashy.egyfilm.service.DownloadService  TAG ,com.elewashy.egyfilm.service.DownloadService  	TypeToken ,com.elewashy.egyfilm.service.DownloadService  URL ,com.elewashy.egyfilm.service.DownloadService  
URLDecoder ,com.elewashy.egyfilm.service.DownloadService  UnsupportedEncodingException ,com.elewashy.egyfilm.service.DownloadService  Uri ,com.elewashy.egyfilm.service.DownloadService  activeJsonDownloads ,com.elewashy.egyfilm.service.DownloadService  any ,com.elewashy.egyfilm.service.DownloadService  applicationContext ,com.elewashy.egyfilm.service.DownloadService  apply ,com.elewashy.egyfilm.service.DownloadService  binder ,com.elewashy.egyfilm.service.DownloadService  !checkAndStartPendingJsonDownloads ,com.elewashy.egyfilm.service.DownloadService  checkStartForeground ,com.elewashy.egyfilm.service.DownloadService  checkStopForeground ,com.elewashy.egyfilm.service.DownloadService  
coerceAtLeast ,com.elewashy.egyfilm.service.DownloadService  	compareBy ,com.elewashy.egyfilm.service.DownloadService  connectivityManager ,com.elewashy.egyfilm.service.DownloadService  contains ,com.elewashy.egyfilm.service.DownloadService  contentResolver ,com.elewashy.egyfilm.service.DownloadService  createControlIntent ,com.elewashy.egyfilm.service.DownloadService  createForegroundNotification ,com.elewashy.egyfilm.service.DownloadService  createNotificationChannel ,com.elewashy.egyfilm.service.DownloadService  createStartIntent ,com.elewashy.egyfilm.service.DownloadService  downloadIdCounter ,com.elewashy.egyfilm.service.DownloadService  
downloadItems ,com.elewashy.egyfilm.service.DownloadService  downloadJobs ,com.elewashy.egyfilm.service.DownloadService  endsWith ,com.elewashy.egyfilm.service.DownloadService  failedDownloads ,com.elewashy.egyfilm.service.DownloadService  filter ,com.elewashy.egyfilm.service.DownloadService  foregroundNotificationId ,com.elewashy.egyfilm.service.DownloadService  format ,com.elewashy.egyfilm.service.DownloadService  getCleanFileName ,com.elewashy.egyfilm.service.DownloadService  getDownloadItems ,com.elewashy.egyfilm.service.DownloadService  getSharedPreferences ,com.elewashy.egyfilm.service.DownloadService  	getString ,com.elewashy.egyfilm.service.DownloadService  getSystemService ,com.elewashy.egyfilm.service.DownloadService  gson ,com.elewashy.egyfilm.service.DownloadService  handleCancelDownload ,com.elewashy.egyfilm.service.DownloadService  handlePauseDownload ,com.elewashy.egyfilm.service.DownloadService  handleResumeDownload ,com.elewashy.egyfilm.service.DownloadService  handleStartDownload ,com.elewashy.egyfilm.service.DownloadService  isActive ,com.elewashy.egyfilm.service.DownloadService  isBlank ,com.elewashy.egyfilm.service.DownloadService  isEmpty ,com.elewashy.egyfilm.service.DownloadService  isForeground ,com.elewashy.egyfilm.service.DownloadService  isNetworkAvailable ,com.elewashy.egyfilm.service.DownloadService  
isNotEmpty ,com.elewashy.egyfilm.service.DownloadService  java ,com.elewashy.egyfilm.service.DownloadService  lastIndexOf ,com.elewashy.egyfilm.service.DownloadService  launch ,com.elewashy.egyfilm.service.DownloadService  let ,com.elewashy.egyfilm.service.DownloadService  loadDownloadState ,com.elewashy.egyfilm.service.DownloadService  	lowercase ,com.elewashy.egyfilm.service.DownloadService  map ,com.elewashy.egyfilm.service.DownloadService  minByOrNull ,com.elewashy.egyfilm.service.DownloadService  networkCallback ,com.elewashy.egyfilm.service.DownloadService  notificationChannelId ,com.elewashy.egyfilm.service.DownloadService  notificationManager ,com.elewashy.egyfilm.service.DownloadService  
plusAssign ,com.elewashy.egyfilm.service.DownloadService  removeSurrounding ,com.elewashy.egyfilm.service.DownloadService  replace ,com.elewashy.egyfilm.service.DownloadService  
retryAttempts ,com.elewashy.egyfilm.service.DownloadService  retryFailedDownloads ,com.elewashy.egyfilm.service.DownloadService  saveDownloadState ,com.elewashy.egyfilm.service.DownloadService  scheduleFailedDownloadsCheck ,com.elewashy.egyfilm.service.DownloadService  sendUpdateBroadcast ,com.elewashy.egyfilm.service.DownloadService  
serviceJob ,com.elewashy.egyfilm.service.DownloadService  serviceScope ,com.elewashy.egyfilm.service.DownloadService  set ,com.elewashy.egyfilm.service.DownloadService  setupNetworkCallback ,com.elewashy.egyfilm.service.DownloadService  sharedPreferences ,com.elewashy.egyfilm.service.DownloadService  showFailurePauseNotification ,com.elewashy.egyfilm.service.DownloadService  
sortedWith ,com.elewashy.egyfilm.service.DownloadService  split ,com.elewashy.egyfilm.service.DownloadService  startDownloadJobInternal ,com.elewashy.egyfilm.service.DownloadService  startForeground ,com.elewashy.egyfilm.service.DownloadService  startOrQueueDownload ,com.elewashy.egyfilm.service.DownloadService  
startsWith ,com.elewashy.egyfilm.service.DownloadService  stopForeground ,com.elewashy.egyfilm.service.DownloadService  	substring ,com.elewashy.egyfilm.service.DownloadService  substringBeforeLast ,com.elewashy.egyfilm.service.DownloadService  take ,com.elewashy.egyfilm.service.DownloadService  thenByDescending ,com.elewashy.egyfilm.service.DownloadService  toList ,com.elewashy.egyfilm.service.DownloadService  toLongOrNull ,com.elewashy.egyfilm.service.DownloadService  trim ,com.elewashy.egyfilm.service.DownloadService  updateDownloadProgress ,com.elewashy.egyfilm.service.DownloadService  updateDownloadStatus ,com.elewashy.egyfilm.service.DownloadService  updateNotification ,com.elewashy.egyfilm.service.DownloadService  ACTION_CANCEL_DOWNLOAD 6com.elewashy.egyfilm.service.DownloadService.Companion  ACTION_CHECK_FAILED_DOWNLOADS 6com.elewashy.egyfilm.service.DownloadService.Companion  ACTION_DOWNLOAD_UPDATE 6com.elewashy.egyfilm.service.DownloadService.Companion  ACTION_OPEN_DOWNLOADS 6com.elewashy.egyfilm.service.DownloadService.Companion  ACTION_PAUSE_DOWNLOAD 6com.elewashy.egyfilm.service.DownloadService.Companion  ACTION_RESUME_DOWNLOAD 6com.elewashy.egyfilm.service.DownloadService.Companion  ACTION_START_DOWNLOAD 6com.elewashy.egyfilm.service.DownloadService.Companion  AUTO_RETRY_INTERVAL_MS 6com.elewashy.egyfilm.service.DownloadService.Companion  AlarmManager 6com.elewashy.egyfilm.service.DownloadService.Companion  
AtomicInteger 6com.elewashy.egyfilm.service.DownloadService.Companion  
AtomicLong 6com.elewashy.egyfilm.service.DownloadService.Companion  Build 6com.elewashy.egyfilm.service.DownloadService.Companion  	ByteArray 6com.elewashy.egyfilm.service.DownloadService.Companion  CancellationException 6com.elewashy.egyfilm.service.DownloadService.Companion  ConcurrentHashMap 6com.elewashy.egyfilm.service.DownloadService.Companion  Context 6com.elewashy.egyfilm.service.DownloadService.Companion  CoroutineScope 6com.elewashy.egyfilm.service.DownloadService.Companion  Dispatchers 6com.elewashy.egyfilm.service.DownloadService.Companion  DownloadActivity 6com.elewashy.egyfilm.service.DownloadService.Companion  DownloadItem 6com.elewashy.egyfilm.service.DownloadService.Companion  DownloadService 6com.elewashy.egyfilm.service.DownloadService.Companion  DownloadStatus 6com.elewashy.egyfilm.service.DownloadService.Companion  
EXTRA_COOKIES 6com.elewashy.egyfilm.service.DownloadService.Companion  EXTRA_DOWNLOAD_ID 6com.elewashy.egyfilm.service.DownloadService.Companion  EXTRA_DOWNLOAD_ID_UPDATE 6com.elewashy.egyfilm.service.DownloadService.Companion  EXTRA_DOWNLOAD_LIST_CHANGED 6com.elewashy.egyfilm.service.DownloadService.Companion  EXTRA_FILE_NAME 6com.elewashy.egyfilm.service.DownloadService.Companion  EXTRA_MIME_TYPE 6com.elewashy.egyfilm.service.DownloadService.Companion  EXTRA_ORIGIN 6com.elewashy.egyfilm.service.DownloadService.Companion  
EXTRA_REFERER 6com.elewashy.egyfilm.service.DownloadService.Companion  EXTRA_SOURCE 6com.elewashy.egyfilm.service.DownloadService.Companion  	EXTRA_URL 6com.elewashy.egyfilm.service.DownloadService.Companion  EXTRA_USER_AGENT 6com.elewashy.egyfilm.service.DownloadService.Companion  Environment 6com.elewashy.egyfilm.service.DownloadService.Companion  File 6com.elewashy.egyfilm.service.DownloadService.Companion  FileOutputStream 6com.elewashy.egyfilm.service.DownloadService.Companion  FileProvider 6com.elewashy.egyfilm.service.DownloadService.Companion  Gson 6com.elewashy.egyfilm.service.DownloadService.Companion  HttpURLConnection 6com.elewashy.egyfilm.service.DownloadService.Companion  Intent 6com.elewashy.egyfilm.service.DownloadService.Companion  KEY_DOWNLOAD_ITEMS 6com.elewashy.egyfilm.service.DownloadService.Companion  KEY_LAST_DOWNLOAD_ID 6com.elewashy.egyfilm.service.DownloadService.Companion  LocalBroadcastManager 6com.elewashy.egyfilm.service.DownloadService.Companion  Log 6com.elewashy.egyfilm.service.DownloadService.Companion  MAX_CONCURRENT_JSON_DOWNLOADS 6com.elewashy.egyfilm.service.DownloadService.Companion  MAX_FAILURE_COUNT 6com.elewashy.egyfilm.service.DownloadService.Companion  MimeTypeMap 6com.elewashy.egyfilm.service.DownloadService.Companion  NetworkCapabilities 6com.elewashy.egyfilm.service.DownloadService.Companion  NetworkRequest 6com.elewashy.egyfilm.service.DownloadService.Companion  NotificationChannel 6com.elewashy.egyfilm.service.DownloadService.Companion  NotificationCompat 6com.elewashy.egyfilm.service.DownloadService.Companion  NotificationManager 6com.elewashy.egyfilm.service.DownloadService.Companion  
PREFS_NAME 6com.elewashy.egyfilm.service.DownloadService.Companion  
PendingIntent 6com.elewashy.egyfilm.service.DownloadService.Companion  R 6com.elewashy.egyfilm.service.DownloadService.Companion  Regex 6com.elewashy.egyfilm.service.DownloadService.Companion  START_STICKY 6com.elewashy.egyfilm.service.DownloadService.Companion  STOP_FOREGROUND_REMOVE 6com.elewashy.egyfilm.service.DownloadService.Companion  StandardCharsets 6com.elewashy.egyfilm.service.DownloadService.Companion  String 6com.elewashy.egyfilm.service.DownloadService.Companion  
SupervisorJob 6com.elewashy.egyfilm.service.DownloadService.Companion  System 6com.elewashy.egyfilm.service.DownloadService.Companion  TAG 6com.elewashy.egyfilm.service.DownloadService.Companion  URL 6com.elewashy.egyfilm.service.DownloadService.Companion  
URLDecoder 6com.elewashy.egyfilm.service.DownloadService.Companion  any 6com.elewashy.egyfilm.service.DownloadService.Companion  apply 6com.elewashy.egyfilm.service.DownloadService.Companion  
coerceAtLeast 6com.elewashy.egyfilm.service.DownloadService.Companion  	compareBy 6com.elewashy.egyfilm.service.DownloadService.Companion  contains 6com.elewashy.egyfilm.service.DownloadService.Companion  createControlIntent 6com.elewashy.egyfilm.service.DownloadService.Companion  createStartIntent 6com.elewashy.egyfilm.service.DownloadService.Companion  
downloadItems 6com.elewashy.egyfilm.service.DownloadService.Companion  downloadJobs 6com.elewashy.egyfilm.service.DownloadService.Companion  endsWith 6com.elewashy.egyfilm.service.DownloadService.Companion  failedDownloads 6com.elewashy.egyfilm.service.DownloadService.Companion  filter 6com.elewashy.egyfilm.service.DownloadService.Companion  format 6com.elewashy.egyfilm.service.DownloadService.Companion  getCleanFileName 6com.elewashy.egyfilm.service.DownloadService.Companion  isActive 6com.elewashy.egyfilm.service.DownloadService.Companion  isBlank 6com.elewashy.egyfilm.service.DownloadService.Companion  isEmpty 6com.elewashy.egyfilm.service.DownloadService.Companion  isNetworkAvailable 6com.elewashy.egyfilm.service.DownloadService.Companion  
isNotEmpty 6com.elewashy.egyfilm.service.DownloadService.Companion  java 6com.elewashy.egyfilm.service.DownloadService.Companion  lastIndexOf 6com.elewashy.egyfilm.service.DownloadService.Companion  launch 6com.elewashy.egyfilm.service.DownloadService.Companion  let 6com.elewashy.egyfilm.service.DownloadService.Companion  	lowercase 6com.elewashy.egyfilm.service.DownloadService.Companion  map 6com.elewashy.egyfilm.service.DownloadService.Companion  minByOrNull 6com.elewashy.egyfilm.service.DownloadService.Companion  
plusAssign 6com.elewashy.egyfilm.service.DownloadService.Companion  removeSurrounding 6com.elewashy.egyfilm.service.DownloadService.Companion  replace 6com.elewashy.egyfilm.service.DownloadService.Companion  
retryAttempts 6com.elewashy.egyfilm.service.DownloadService.Companion  retryFailedDownloads 6com.elewashy.egyfilm.service.DownloadService.Companion  saveDownloadState 6com.elewashy.egyfilm.service.DownloadService.Companion  serviceScope 6com.elewashy.egyfilm.service.DownloadService.Companion  set 6com.elewashy.egyfilm.service.DownloadService.Companion  
sortedWith 6com.elewashy.egyfilm.service.DownloadService.Companion  split 6com.elewashy.egyfilm.service.DownloadService.Companion  startOrQueueDownload 6com.elewashy.egyfilm.service.DownloadService.Companion  
startsWith 6com.elewashy.egyfilm.service.DownloadService.Companion  	substring 6com.elewashy.egyfilm.service.DownloadService.Companion  substringBeforeLast 6com.elewashy.egyfilm.service.DownloadService.Companion  take 6com.elewashy.egyfilm.service.DownloadService.Companion  thenByDescending 6com.elewashy.egyfilm.service.DownloadService.Companion  toList 6com.elewashy.egyfilm.service.DownloadService.Companion  toLongOrNull 6com.elewashy.egyfilm.service.DownloadService.Companion  trim 6com.elewashy.egyfilm.service.DownloadService.Companion  updateDownloadProgress 6com.elewashy.egyfilm.service.DownloadService.Companion  updateDownloadStatus 6com.elewashy.egyfilm.service.DownloadService.Companion  updateNotification 6com.elewashy.egyfilm.service.DownloadService.Companion  NetworkCallback @com.elewashy.egyfilm.service.DownloadService.ConnectivityManager  
getService ;com.elewashy.egyfilm.service.DownloadService.DownloadBinder  Build 7com.elewashy.egyfilm.service.MyFirebaseMessagingService  CHANNEL_DESCRIPTION 7com.elewashy.egyfilm.service.MyFirebaseMessagingService  
CHANNEL_ID 7com.elewashy.egyfilm.service.MyFirebaseMessagingService  CHANNEL_NAME 7com.elewashy.egyfilm.service.MyFirebaseMessagingService  Context 7com.elewashy.egyfilm.service.MyFirebaseMessagingService  Intent 7com.elewashy.egyfilm.service.MyFirebaseMessagingService  Log 7com.elewashy.egyfilm.service.MyFirebaseMessagingService  MainActivity 7com.elewashy.egyfilm.service.MyFirebaseMessagingService  
MutableMap 7com.elewashy.egyfilm.service.MyFirebaseMessagingService  NotificationChannel 7com.elewashy.egyfilm.service.MyFirebaseMessagingService  NotificationCompat 7com.elewashy.egyfilm.service.MyFirebaseMessagingService  NotificationManager 7com.elewashy.egyfilm.service.MyFirebaseMessagingService  
PendingIntent 7com.elewashy.egyfilm.service.MyFirebaseMessagingService  R 7com.elewashy.egyfilm.service.MyFirebaseMessagingService  
RemoteMessage 7com.elewashy.egyfilm.service.MyFirebaseMessagingService  String 7com.elewashy.egyfilm.service.MyFirebaseMessagingService  System 7com.elewashy.egyfilm.service.MyFirebaseMessagingService  TAG 7com.elewashy.egyfilm.service.MyFirebaseMessagingService  apply 7com.elewashy.egyfilm.service.MyFirebaseMessagingService  createNotificationChannel 7com.elewashy.egyfilm.service.MyFirebaseMessagingService  getSharedPreferences 7com.elewashy.egyfilm.service.MyFirebaseMessagingService  getSystemService 7com.elewashy.egyfilm.service.MyFirebaseMessagingService  handleDataMessage 7com.elewashy.egyfilm.service.MyFirebaseMessagingService  
isNotEmpty 7com.elewashy.egyfilm.service.MyFirebaseMessagingService  java 7com.elewashy.egyfilm.service.MyFirebaseMessagingService  let 7com.elewashy.egyfilm.service.MyFirebaseMessagingService  sendNotification 7com.elewashy.egyfilm.service.MyFirebaseMessagingService  sendRegistrationToServer 7com.elewashy.egyfilm.service.MyFirebaseMessagingService  with 7com.elewashy.egyfilm.service.MyFirebaseMessagingService  Build Acom.elewashy.egyfilm.service.MyFirebaseMessagingService.Companion  CHANNEL_DESCRIPTION Acom.elewashy.egyfilm.service.MyFirebaseMessagingService.Companion  
CHANNEL_ID Acom.elewashy.egyfilm.service.MyFirebaseMessagingService.Companion  CHANNEL_NAME Acom.elewashy.egyfilm.service.MyFirebaseMessagingService.Companion  Context Acom.elewashy.egyfilm.service.MyFirebaseMessagingService.Companion  Intent Acom.elewashy.egyfilm.service.MyFirebaseMessagingService.Companion  Log Acom.elewashy.egyfilm.service.MyFirebaseMessagingService.Companion  MainActivity Acom.elewashy.egyfilm.service.MyFirebaseMessagingService.Companion  NotificationChannel Acom.elewashy.egyfilm.service.MyFirebaseMessagingService.Companion  NotificationCompat Acom.elewashy.egyfilm.service.MyFirebaseMessagingService.Companion  NotificationManager Acom.elewashy.egyfilm.service.MyFirebaseMessagingService.Companion  
PendingIntent Acom.elewashy.egyfilm.service.MyFirebaseMessagingService.Companion  R Acom.elewashy.egyfilm.service.MyFirebaseMessagingService.Companion  System Acom.elewashy.egyfilm.service.MyFirebaseMessagingService.Companion  TAG Acom.elewashy.egyfilm.service.MyFirebaseMessagingService.Companion  apply Acom.elewashy.egyfilm.service.MyFirebaseMessagingService.Companion  
isNotEmpty Acom.elewashy.egyfilm.service.MyFirebaseMessagingService.Companion  java Acom.elewashy.egyfilm.service.MyFirebaseMessagingService.Companion  let Acom.elewashy.egyfilm.service.MyFirebaseMessagingService.Companion  with Acom.elewashy.egyfilm.service.MyFirebaseMessagingService.Companion  	Alignment com.elewashy.egyfilm.ui.screens  Arrangement com.elewashy.egyfilm.ui.screens  Boolean com.elewashy.egyfilm.ui.screens  Button com.elewashy.egyfilm.ui.screens  ButtonDefaults com.elewashy.egyfilm.ui.screens  CardDefaults com.elewashy.egyfilm.ui.screens  CircularProgressIndicator com.elewashy.egyfilm.ui.screens  Color com.elewashy.egyfilm.ui.screens  Column com.elewashy.egyfilm.ui.screens  
Composable com.elewashy.egyfilm.ui.screens  
EgyFilmRed com.elewashy.egyfilm.ui.screens  EgyFilmRedTransparent com.elewashy.egyfilm.ui.screens  FastOutSlowInEasing com.elewashy.egyfilm.ui.screens  Float com.elewashy.egyfilm.ui.screens  
FontWeight com.elewashy.egyfilm.ui.screens  Image com.elewashy.egyfilm.ui.screens  LinearProgressIndicator com.elewashy.egyfilm.ui.screens  
MaterialTheme com.elewashy.egyfilm.ui.screens  Modifier com.elewashy.egyfilm.ui.screens  R com.elewashy.egyfilm.ui.screens  RoundedCornerShape com.elewashy.egyfilm.ui.screens  Spacer com.elewashy.egyfilm.ui.screens  SplashScreen com.elewashy.egyfilm.ui.screens  String com.elewashy.egyfilm.ui.screens  Text com.elewashy.egyfilm.ui.screens  	TextAlign com.elewashy.egyfilm.ui.screens  Unit com.elewashy.egyfilm.ui.screens  UpdateDialog com.elewashy.egyfilm.ui.screens  alpha com.elewashy.egyfilm.ui.screens  buttonColors com.elewashy.egyfilm.ui.screens  
cardColors com.elewashy.egyfilm.ui.screens  delay com.elewashy.egyfilm.ui.screens  fillMaxSize com.elewashy.egyfilm.ui.screens  height com.elewashy.egyfilm.ui.screens  padding com.elewashy.egyfilm.ui.screens  painterResource com.elewashy.egyfilm.ui.screens  scale com.elewashy.egyfilm.ui.screens  size com.elewashy.egyfilm.ui.screens  tween com.elewashy.egyfilm.ui.screens  width com.elewashy.egyfilm.ui.screens  Activity com.elewashy.egyfilm.ui.theme  Black com.elewashy.egyfilm.ui.theme  Boolean com.elewashy.egyfilm.ui.theme  Build com.elewashy.egyfilm.ui.theme  
Composable com.elewashy.egyfilm.ui.theme  DarkColorScheme com.elewashy.egyfilm.ui.theme  EgyFilmBackground com.elewashy.egyfilm.ui.theme  EgyFilmBlack com.elewashy.egyfilm.ui.theme  EgyFilmBlackTransparent com.elewashy.egyfilm.ui.theme  EgyFilmDarkGray com.elewashy.egyfilm.ui.theme  EgyFilmError com.elewashy.egyfilm.ui.theme  EgyFilmGray com.elewashy.egyfilm.ui.theme  EgyFilmLightGray com.elewashy.egyfilm.ui.theme  EgyFilmOnSurface com.elewashy.egyfilm.ui.theme  EgyFilmOnSurfaceVariant com.elewashy.egyfilm.ui.theme  
EgyFilmRed com.elewashy.egyfilm.ui.theme  EgyFilmRedDark com.elewashy.egyfilm.ui.theme  EgyFilmRedTransparent com.elewashy.egyfilm.ui.theme  EgyFilmSuccess com.elewashy.egyfilm.ui.theme  EgyFilmSurface com.elewashy.egyfilm.ui.theme  EgyFilmSurfaceVariant com.elewashy.egyfilm.ui.theme  EgyFilmTheme com.elewashy.egyfilm.ui.theme  EgyFilmWarning com.elewashy.egyfilm.ui.theme  EgyFilmWhiteTransparent com.elewashy.egyfilm.ui.theme  
FontFamily com.elewashy.egyfilm.ui.theme  
FontWeight com.elewashy.egyfilm.ui.theme  LightColorScheme com.elewashy.egyfilm.ui.theme  
Typography com.elewashy.egyfilm.ui.theme  Unit com.elewashy.egyfilm.ui.theme  White com.elewashy.egyfilm.ui.theme  WindowCompat com.elewashy.egyfilm.ui.theme  ActivityCompat com.elewashy.egyfilm.util  ActivityResultContracts com.elewashy.egyfilm.util  ActivityResultLauncher com.elewashy.egyfilm.util  AlertDialog com.elewashy.egyfilm.util  AppCompatActivity com.elewashy.egyfilm.util  Array com.elewashy.egyfilm.util  Boolean com.elewashy.egyfilm.util  Build com.elewashy.egyfilm.util  
ContextCompat com.elewashy.egyfilm.util  Environment com.elewashy.egyfilm.util  	Exception com.elewashy.egyfilm.util  Intent com.elewashy.egyfilm.util  Manifest com.elewashy.egyfilm.util  PackageManager com.elewashy.egyfilm.util  PermissionManager com.elewashy.egyfilm.util  Settings com.elewashy.egyfilm.util  String com.elewashy.egyfilm.util  Unit com.elewashy.egyfilm.util  Uri com.elewashy.egyfilm.util  all com.elewashy.egyfilm.util  any com.elewashy.egyfilm.util  arrayOf com.elewashy.egyfilm.util  ActivityCompat +com.elewashy.egyfilm.util.PermissionManager  ActivityResultContracts +com.elewashy.egyfilm.util.PermissionManager  ActivityResultLauncher +com.elewashy.egyfilm.util.PermissionManager  AlertDialog +com.elewashy.egyfilm.util.PermissionManager  AppCompatActivity +com.elewashy.egyfilm.util.PermissionManager  Array +com.elewashy.egyfilm.util.PermissionManager  Boolean +com.elewashy.egyfilm.util.PermissionManager  Build +com.elewashy.egyfilm.util.PermissionManager  
ContextCompat +com.elewashy.egyfilm.util.PermissionManager  Environment +com.elewashy.egyfilm.util.PermissionManager  	Exception +com.elewashy.egyfilm.util.PermissionManager  Intent +com.elewashy.egyfilm.util.PermissionManager  Manifest +com.elewashy.egyfilm.util.PermissionManager  PackageManager +com.elewashy.egyfilm.util.PermissionManager  Settings +com.elewashy.egyfilm.util.PermissionManager  String +com.elewashy.egyfilm.util.PermissionManager  Unit +com.elewashy.egyfilm.util.PermissionManager  Uri +com.elewashy.egyfilm.util.PermissionManager  activity +com.elewashy.egyfilm.util.PermissionManager  all +com.elewashy.egyfilm.util.PermissionManager  any +com.elewashy.egyfilm.util.PermissionManager  arrayOf +com.elewashy.egyfilm.util.PermissionManager  handlePermissionDenied +com.elewashy.egyfilm.util.PermissionManager  hasAllRequiredPermissions +com.elewashy.egyfilm.util.PermissionManager  hasStoragePermission +com.elewashy.egyfilm.util.PermissionManager  installPackagesLauncher +com.elewashy.egyfilm.util.PermissionManager  manageExternalStorageLauncher +com.elewashy.egyfilm.util.PermissionManager  onPermissionDenied +com.elewashy.egyfilm.util.PermissionManager  onPermissionGranted +com.elewashy.egyfilm.util.PermissionManager  openAppSettings +com.elewashy.egyfilm.util.PermissionManager  requestAllRequiredPermissions +com.elewashy.egyfilm.util.PermissionManager   requestInstallPackagesPermission +com.elewashy.egyfilm.util.PermissionManager  requestLegacyStoragePermissions +com.elewashy.egyfilm.util.PermissionManager  &requestManageExternalStoragePermission +com.elewashy.egyfilm.util.PermissionManager  requestNotificationPermission +com.elewashy.egyfilm.util.PermissionManager  requestPermissionsLauncher +com.elewashy.egyfilm.util.PermissionManager  requestStoragePermission +com.elewashy.egyfilm.util.PermissionManager  setupPermissionLaunchers +com.elewashy.egyfilm.util.PermissionManager  showPermissionDeniedDialog +com.elewashy.egyfilm.util.PermissionManager  showPermissionExplanationDialog +com.elewashy.egyfilm.util.PermissionManager  ActivityCompat 5com.elewashy.egyfilm.util.PermissionManager.Companion  ActivityResultContracts 5com.elewashy.egyfilm.util.PermissionManager.Companion  AlertDialog 5com.elewashy.egyfilm.util.PermissionManager.Companion  Build 5com.elewashy.egyfilm.util.PermissionManager.Companion  
ContextCompat 5com.elewashy.egyfilm.util.PermissionManager.Companion  Environment 5com.elewashy.egyfilm.util.PermissionManager.Companion  Intent 5com.elewashy.egyfilm.util.PermissionManager.Companion  Manifest 5com.elewashy.egyfilm.util.PermissionManager.Companion  PackageManager 5com.elewashy.egyfilm.util.PermissionManager.Companion  Settings 5com.elewashy.egyfilm.util.PermissionManager.Companion  Uri 5com.elewashy.egyfilm.util.PermissionManager.Companion  all 5com.elewashy.egyfilm.util.PermissionManager.Companion  any 5com.elewashy.egyfilm.util.PermissionManager.Companion  arrayOf 5com.elewashy.egyfilm.util.PermissionManager.Companion  OnCompleteListener com.google.android.gms.tasks  Task com.google.android.gms.tasks  <SAM-CONSTRUCTOR> /com.google.android.gms.tasks.OnCompleteListener  addOnCompleteListener !com.google.android.gms.tasks.Task  	exception !com.google.android.gms.tasks.Task  isSuccessful !com.google.android.gms.tasks.Task  result !com.google.android.gms.tasks.Task  BottomSheetDialogFragment 'com.google.android.material.bottomsheet  DownloadActivity Acom.google.android.material.bottomsheet.BottomSheetDialogFragment  Intent Acom.google.android.material.bottomsheet.BottomSheetDialogFragment  MainActivity Acom.google.android.material.bottomsheet.BottomSheetDialogFragment  MaterialButton Acom.google.android.material.bottomsheet.BottomSheetDialogFragment  R Acom.google.android.material.bottomsheet.BottomSheetDialogFragment  SettingsActivity Acom.google.android.material.bottomsheet.BottomSheetDialogFragment  dismiss Acom.google.android.material.bottomsheet.BottomSheetDialogFragment  java Acom.google.android.material.bottomsheet.BottomSheetDialogFragment  MaterialButton "com.google.android.material.button  	isEnabled 1com.google.android.material.button.MaterialButton  setOnClickListener 1com.google.android.material.button.MaterialButton  text 1com.google.android.material.button.MaterialButton  MaterialAlertDialogBuilder "com.google.android.material.dialog  create =com.google.android.material.dialog.MaterialAlertDialogBuilder  setView =com.google.android.material.dialog.MaterialAlertDialogBuilder  ShapeableImageView %com.google.android.material.imageview  layoutParams 8com.google.android.material.imageview.ShapeableImageView  
requestLayout 8com.google.android.material.imageview.ShapeableImageView  setImageBitmap 8com.google.android.material.imageview.ShapeableImageView  LinearProgressIndicator -com.google.android.material.progressindicator  progress Ecom.google.android.material.progressindicator.LinearProgressIndicator  
visibility Ecom.google.android.material.progressindicator.LinearProgressIndicator  Snackbar $com.google.android.material.snackbar  LENGTH_LONG ;com.google.android.material.snackbar.BaseTransientBottomBar  LENGTH_SHORT ;com.google.android.material.snackbar.BaseTransientBottomBar  LENGTH_LONG -com.google.android.material.snackbar.Snackbar  LENGTH_SHORT -com.google.android.material.snackbar.Snackbar  make -com.google.android.material.snackbar.Snackbar  show -com.google.android.material.snackbar.Snackbar  TextInputEditText %com.google.android.material.textfield  text 7com.google.android.material.textfield.TextInputEditText  FirebaseMessaging com.google.firebase.messaging  FirebaseMessagingService com.google.firebase.messaging  
RemoteMessage com.google.firebase.messaging  Build 3com.google.firebase.messaging.EnhancedIntentService  CHANNEL_DESCRIPTION 3com.google.firebase.messaging.EnhancedIntentService  
CHANNEL_ID 3com.google.firebase.messaging.EnhancedIntentService  CHANNEL_NAME 3com.google.firebase.messaging.EnhancedIntentService  Context 3com.google.firebase.messaging.EnhancedIntentService  Intent 3com.google.firebase.messaging.EnhancedIntentService  Log 3com.google.firebase.messaging.EnhancedIntentService  MainActivity 3com.google.firebase.messaging.EnhancedIntentService  NotificationChannel 3com.google.firebase.messaging.EnhancedIntentService  NotificationCompat 3com.google.firebase.messaging.EnhancedIntentService  NotificationManager 3com.google.firebase.messaging.EnhancedIntentService  
PendingIntent 3com.google.firebase.messaging.EnhancedIntentService  R 3com.google.firebase.messaging.EnhancedIntentService  System 3com.google.firebase.messaging.EnhancedIntentService  TAG 3com.google.firebase.messaging.EnhancedIntentService  apply 3com.google.firebase.messaging.EnhancedIntentService  
isNotEmpty 3com.google.firebase.messaging.EnhancedIntentService  java 3com.google.firebase.messaging.EnhancedIntentService  let 3com.google.firebase.messaging.EnhancedIntentService  with 3com.google.firebase.messaging.EnhancedIntentService  getInstance /com.google.firebase.messaging.FirebaseMessaging  subscribeToTopic /com.google.firebase.messaging.FirebaseMessaging  token /com.google.firebase.messaging.FirebaseMessaging  Build 6com.google.firebase.messaging.FirebaseMessagingService  CHANNEL_DESCRIPTION 6com.google.firebase.messaging.FirebaseMessagingService  
CHANNEL_ID 6com.google.firebase.messaging.FirebaseMessagingService  CHANNEL_NAME 6com.google.firebase.messaging.FirebaseMessagingService  Context 6com.google.firebase.messaging.FirebaseMessagingService  Intent 6com.google.firebase.messaging.FirebaseMessagingService  Log 6com.google.firebase.messaging.FirebaseMessagingService  MainActivity 6com.google.firebase.messaging.FirebaseMessagingService  NotificationChannel 6com.google.firebase.messaging.FirebaseMessagingService  NotificationCompat 6com.google.firebase.messaging.FirebaseMessagingService  NotificationManager 6com.google.firebase.messaging.FirebaseMessagingService  
PendingIntent 6com.google.firebase.messaging.FirebaseMessagingService  R 6com.google.firebase.messaging.FirebaseMessagingService  System 6com.google.firebase.messaging.FirebaseMessagingService  TAG 6com.google.firebase.messaging.FirebaseMessagingService  apply 6com.google.firebase.messaging.FirebaseMessagingService  
isNotEmpty 6com.google.firebase.messaging.FirebaseMessagingService  java 6com.google.firebase.messaging.FirebaseMessagingService  let 6com.google.firebase.messaging.FirebaseMessagingService  with 6com.google.firebase.messaging.FirebaseMessagingService  Notification +com.google.firebase.messaging.RemoteMessage  data +com.google.firebase.messaging.RemoteMessage  from +com.google.firebase.messaging.RemoteMessage  notification +com.google.firebase.messaging.RemoteMessage  body 8com.google.firebase.messaging.RemoteMessage.Notification  let 8com.google.firebase.messaging.RemoteMessage.Notification  title 8com.google.firebase.messaging.RemoteMessage.Notification  Gson com.google.gson  fromJson com.google.gson.Gson  toJson com.google.gson.Gson  	TypeToken com.google.gson.reflect  type !com.google.gson.reflect.TypeToken  Boolean java.io  BufferedReader java.io  ByteArrayInputStream java.io  ByteArrayOutputStream java.io  Context java.io  CoroutineScope java.io  Dispatchers java.io  DownloadService java.io  Environment java.io  	Exception java.io  File java.io  FileOutputStream java.io  Handler java.io  HttpURLConnection java.io  IOException java.io  InputStream java.io  
JSONObject java.io  Job java.io  JsonDownloader java.io  Looper java.io  OutputStream java.io  Pattern java.io  String java.io  Toast java.io  URL java.io  UnsupportedEncodingException java.io  Volatile java.io  also java.io  bufferedReader java.io  contains java.io  context java.io  createStartIntent java.io  endsWith java.io  getDownloadUrl java.io  launch java.io  mapOf java.io  readText java.io  run java.io  	showToast java.io  substringAfterLast java.io  synchronized java.io  to java.io  toByteArray java.io  until java.io  use java.io  withContext java.io  readText java.io.BufferedReader  use java.io.BufferedReader  absolutePath java.io.File  delete java.io.File  exists java.io.File  length java.io.File  	listFiles java.io.File  mkdirs java.io.File  name java.io.File  
parentFile java.io.File  close java.io.FileOutputStream  flush java.io.FileOutputStream  write java.io.FileOutputStream  message java.io.IOException  close java.io.InputStream  read java.io.InputStream  flush java.io.OutputStream  use java.io.OutputStream  write java.io.OutputStream  Class 	java.lang  	Exception 	java.lang  IllegalArgumentException 	java.lang  InterruptedException 	java.lang  Runnable 	java.lang  SecurityException 	java.lang  Thread 	java.lang  message java.lang.Exception  printStackTrace java.lang.Exception  <SAM-CONSTRUCTOR> java.lang.Runnable  currentTimeMillis java.lang.System  start java.lang.Thread  
BigDecimal 	java.math  
BigInteger 	java.math  HttpURLConnection java.net  URL java.net  
URLDecoder java.net  HTTP_OK java.net.HttpURLConnection  HTTP_PARTIAL java.net.HttpURLConnection  connect java.net.HttpURLConnection  connectTimeout java.net.HttpURLConnection  contentType java.net.HttpURLConnection  
disconnect java.net.HttpURLConnection  doOutput java.net.HttpURLConnection  getHeaderField java.net.HttpURLConnection  inputStream java.net.HttpURLConnection  outputStream java.net.HttpURLConnection  readTimeout java.net.HttpURLConnection  
requestMethod java.net.HttpURLConnection  responseCode java.net.HttpURLConnection  setRequestProperty java.net.HttpURLConnection  openConnection java.net.URL  path java.net.URL  readText java.net.URL  connect java.net.URLConnection  connectTimeout java.net.URLConnection  contentType java.net.URLConnection  doOutput java.net.URLConnection  getHeaderField java.net.URLConnection  inputStream java.net.URLConnection  outputStream java.net.URLConnection  readTimeout java.net.URLConnection  setRequestProperty java.net.URLConnection  decode java.net.URLDecoder  StandardCharsets java.nio.charset  name java.nio.charset.Charset  UTF_8 !java.nio.charset.StandardCharsets  SimpleDateFormat 	java.text  format java.text.DateFormat  format java.text.Format  format java.text.SimpleDateFormat  ActivityFilterUpdatesBinding 	java.util  	AdBlocker 	java.util  AppCompatActivity 	java.util  Bundle 	java.util  
Comparator 	java.util  Date 	java.util  Dispatchers 	java.util  	Exception 	java.util  Locale 	java.util  MODE_PRIVATE 	java.util  OpenLinkValidator 	java.util  R 	java.util  SimpleDateFormat 	java.util  String 	java.util  System 	java.util  Timer 	java.util  Toast 	java.util  ValidLinkChecker 	java.util  View 	java.util  	adBlocker 	java.util  apply 	java.util  binding 	java.util  clearAdBlockerTimeConstraints 	java.util  getInstance 	java.util  	getString 	java.util  launch 	java.util  loadLastUpdatedTimes 	java.util  maxOf 	java.util  openLinkValidator 	java.util  saveUpdateTime 	java.util  validLinkChecker 	java.util  withContext 	java.util  thenByDescending java.util.Comparator  
getDefault java.util.Locale  ActivityInfo java.util.concurrent  	AdBlocker java.util.concurrent  
AtomicInteger java.util.concurrent  Base64 java.util.concurrent  Bitmap java.util.concurrent  
BitmapFactory java.util.concurrent  Boolean java.util.concurrent  Build java.util.concurrent  Bundle java.util.concurrent  ByteArrayInputStream java.util.concurrent  Callable java.util.concurrent  CancellationException java.util.concurrent  Color java.util.concurrent  
ColorDrawable java.util.concurrent  ConcurrentHashMap java.util.concurrent  
ContentValues java.util.concurrent  Context java.util.concurrent  ContextMenu java.util.concurrent  
CookieManager java.util.concurrent  CustomViewCallback java.util.concurrent  
Deprecated java.util.concurrent  DownloadService java.util.concurrent  Environment java.util.concurrent  	Exception java.util.concurrent  	Executors java.util.concurrent  Fragment java.util.concurrent  FragmentBrowseBinding java.util.concurrent  Future java.util.concurrent  Handler java.util.concurrent  Int java.util.concurrent  Intent java.util.concurrent  InterruptedException java.util.concurrent  JsonDownloader java.util.concurrent  LayoutInflater java.util.concurrent  ListMetadata java.util.concurrent  Looper java.util.concurrent  MainActivity java.util.concurrent  MaterialAlertDialogBuilder java.util.concurrent  
MediaStore java.util.concurrent  MenuItem java.util.concurrent  MotionEvent java.util.concurrent  NotificationChannel java.util.concurrent  NotificationCompat java.util.concurrent  NotificationManager java.util.concurrent  OpenLinkValidator java.util.concurrent  R java.util.concurrent  
ReentrantLock java.util.concurrent  
RegexPatterns java.util.concurrent  	Resources java.util.concurrent  Set java.util.concurrent  ShapeableImageView java.util.concurrent  ShareCompat java.util.concurrent  Snackbar java.util.concurrent  SpannableStringBuilder java.util.concurrent  String java.util.concurrent  Suppress java.util.concurrent  SuppressLint java.util.concurrent  System java.util.concurrent  Thread java.util.concurrent  TimeUnit java.util.concurrent  Toast java.util.concurrent  Triple java.util.concurrent  URL java.util.concurrent  
URLDecoder java.util.concurrent  URLUtil java.util.concurrent  Uri java.util.concurrent  ValidLinkChecker java.util.concurrent  View java.util.concurrent  	ViewGroup java.util.concurrent  Volatile java.util.concurrent  WebChromeClient java.util.concurrent  WebResourceRequest java.util.concurrent  WebResourceResponse java.util.concurrent  WebSettings java.util.concurrent  WebView java.util.concurrent  
WebViewClient java.util.concurrent  also java.util.concurrent  any java.util.concurrent  apply java.util.concurrent  binding java.util.concurrent  bufferedReader java.util.concurrent  contains java.util.concurrent  createStartIntent java.util.concurrent  emptySet java.util.concurrent  filter java.util.concurrent  	filterNot java.util.concurrent  flatMap java.util.concurrent  forEach java.util.concurrent  getInstance java.util.concurrent  indexOf java.util.concurrent  
isNotEmpty java.util.concurrent  java java.util.concurrent  let java.util.concurrent  lines java.util.concurrent  listOf java.util.concurrent  map java.util.concurrent  matches java.util.concurrent  
mutableListOf java.util.concurrent  mutableSetOf java.util.concurrent  plus java.util.concurrent  println java.util.concurrent  readText java.util.concurrent  replace java.util.concurrent  requireActivity java.util.concurrent  requireContext java.util.concurrent  run java.util.concurrent  split java.util.concurrent  
startsWith java.util.concurrent  	substring java.util.concurrent  synchronized java.util.concurrent  toByteArray java.util.concurrent  toRegex java.util.concurrent  toSet java.util.concurrent  
trimIndent java.util.concurrent  use java.util.concurrent  webIcon java.util.concurrent  withLock java.util.concurrent  <SAM-CONSTRUCTOR> java.util.concurrent.Callable  clear &java.util.concurrent.ConcurrentHashMap  get &java.util.concurrent.ConcurrentHashMap  remove &java.util.concurrent.ConcurrentHashMap  set &java.util.concurrent.ConcurrentHashMap  values &java.util.concurrent.ConcurrentHashMap  ContextMenuInfo  java.util.concurrent.ContextMenu  awaitTermination $java.util.concurrent.ExecutorService  shutdown $java.util.concurrent.ExecutorService  shutdownNow $java.util.concurrent.ExecutorService  submit $java.util.concurrent.ExecutorService  newFixedThreadPool java.util.concurrent.Executors  newSingleThreadExecutor java.util.concurrent.Executors  get java.util.concurrent.Future  MINUTES java.util.concurrent.TimeUnit  SECONDS java.util.concurrent.TimeUnit  OnTouchListener java.util.concurrent.View  
AtomicInteger java.util.concurrent.atomic  
AtomicLong java.util.concurrent.atomic  decrementAndGet )java.util.concurrent.atomic.AtomicInteger  get )java.util.concurrent.atomic.AtomicInteger  incrementAndGet )java.util.concurrent.atomic.AtomicInteger  get &java.util.concurrent.atomic.AtomicLong  incrementAndGet &java.util.concurrent.atomic.AtomicLong  set &java.util.concurrent.atomic.AtomicLong  net java.util.concurrent.java  HttpURLConnection java.util.concurrent.java.net  
ReentrantLock java.util.concurrent.locks  withLock (java.util.concurrent.locks.ReentrantLock  Pattern java.util.regex  find java.util.regex.Matcher  group java.util.regex.Matcher  compile java.util.regex.Pattern  matcher java.util.regex.Pattern  Array kotlin  BooleanArray kotlin  	ByteArray kotlin  	CharArray kotlin  CharSequence kotlin  
Comparable kotlin  
Deprecated kotlin  DoubleArray kotlin  
FloatArray kotlin  	Function0 kotlin  	Function1 kotlin  	Function2 kotlin  	Function5 kotlin  IntArray kotlin  	LongArray kotlin  Nothing kotlin  Pair kotlin  Result kotlin  
ShortArray kotlin  String kotlin  Suppress kotlin  Triple kotlin  
UByteArray kotlin  	UIntArray kotlin  
ULongArray kotlin  UShortArray kotlin  also kotlin  apply kotlin  arrayOf kotlin  
isInitialized kotlin  let kotlin  map kotlin  plus kotlin  run kotlin  synchronized kotlin  to kotlin  toList kotlin  use kotlin  with kotlin  any kotlin.Array  not kotlin.Boolean  size kotlin.ByteArray  isEmpty kotlin.CharSequence  sp 
kotlin.Double  times 
kotlin.Double  toInt 
kotlin.Double  	compareTo kotlin.Float  
unaryMinus kotlin.Float  invoke kotlin.Function0  invoke kotlin.Function1  
coerceAtLeast 
kotlin.Int  	compareTo 
kotlin.Int  div 
kotlin.Int  inc 
kotlin.Int  minus 
kotlin.Int  or 
kotlin.Int  plus 
kotlin.Int  times 
kotlin.Int  	compareTo kotlin.Long  div kotlin.Long  minus kotlin.Long  plus kotlin.Long  
plusAssign kotlin.Long  times kotlin.Long  toInt kotlin.Long  
unaryMinus kotlin.Long  	Companion 
kotlin.String  contains 
kotlin.String  endsWith 
kotlin.String  format 
kotlin.String  indexOf 
kotlin.String  isBlank 
kotlin.String  isEmpty 
kotlin.String  
isNotEmpty 
kotlin.String  lastIndexOf 
kotlin.String  length 
kotlin.String  let 
kotlin.String  lines 
kotlin.String  	lowercase 
kotlin.String  matches 
kotlin.String  plus 
kotlin.String  removeSurrounding 
kotlin.String  replace 
kotlin.String  split 
kotlin.String  
startsWith 
kotlin.String  	substring 
kotlin.String  substringAfterLast 
kotlin.String  substringBeforeLast 
kotlin.String  take 
kotlin.String  to 
kotlin.String  toByteArray 
kotlin.String  toIntOrNull 
kotlin.String  toLongOrNull 
kotlin.String  toRegex 
kotlin.String  trim 
kotlin.String  
trimIndent 
kotlin.String  format kotlin.String.Companion  message kotlin.Throwable  printStackTrace kotlin.Throwable  
component1 
kotlin.Triple  
component2 
kotlin.Triple  
component3 
kotlin.Triple  first 
kotlin.Triple  
Collection kotlin.collections  IntIterator kotlin.collections  Iterable kotlin.collections  Iterator kotlin.collections  List kotlin.collections  Map kotlin.collections  MutableCollection kotlin.collections  MutableList kotlin.collections  
MutableMap kotlin.collections  
MutableSet kotlin.collections  Set kotlin.collections  all kotlin.collections  any kotlin.collections  contains kotlin.collections  	emptyList kotlin.collections  emptySet kotlin.collections  filter kotlin.collections  	filterNot kotlin.collections  flatMap kotlin.collections  forEach kotlin.collections  indexOf kotlin.collections  isEmpty kotlin.collections  
isNotEmpty kotlin.collections  lastIndexOf kotlin.collections  listOf kotlin.collections  map kotlin.collections  mapOf kotlin.collections  maxOf kotlin.collections  minByOrNull kotlin.collections  
mutableListOf kotlin.collections  mutableSetOf kotlin.collections  plus kotlin.collections  
plusAssign kotlin.collections  set kotlin.collections  
sortedWith kotlin.collections  take kotlin.collections  toByteArray kotlin.collections  toList kotlin.collections  toSet kotlin.collections  all kotlin.collections.Collection  hasNext kotlin.collections.IntIterator  next kotlin.collections.IntIterator  hasNext kotlin.collections.Iterator  next kotlin.collections.Iterator  any kotlin.collections.List  filter kotlin.collections.List  flatMap kotlin.collections.List  get kotlin.collections.List  isEmpty kotlin.collections.List  
isNotEmpty kotlin.collections.List  iterator kotlin.collections.List  map kotlin.collections.List  minByOrNull kotlin.collections.List  size kotlin.collections.List  
sortedWith kotlin.collections.List  toSet kotlin.collections.List  Entry kotlin.collections.Map  get kotlin.collections.Map  values kotlin.collections.Map  any $kotlin.collections.MutableCollection  filter $kotlin.collections.MutableCollection  map $kotlin.collections.MutableCollection  toList $kotlin.collections.MutableCollection  add kotlin.collections.MutableList  any kotlin.collections.MutableList  get kotlin.collections.MutableMap  add kotlin.collections.MutableSet  addAll kotlin.collections.MutableSet  any kotlin.collections.MutableSet  clear kotlin.collections.MutableSet  
isNotEmpty kotlin.collections.MutableSet  contains kotlin.collections.Set  	filterNot kotlin.collections.Set  
isNotEmpty kotlin.collections.Set  plus kotlin.collections.Set  	compareBy kotlin.comparisons  maxOf kotlin.comparisons  thenByDescending kotlin.comparisons  scheduleAtFixedRate kotlin.concurrent  thread kotlin.concurrent  withLock kotlin.concurrent  CoroutineContext kotlin.coroutines  SuspendFunction1 kotlin.coroutines  plus "kotlin.coroutines.CoroutineContext  bufferedReader 	kotlin.io  endsWith 	kotlin.io  println 	kotlin.io  readText 	kotlin.io  
startsWith 	kotlin.io  use 	kotlin.io  Synchronized 
kotlin.jvm  Volatile 
kotlin.jvm  java 
kotlin.jvm  	CharRange 
kotlin.ranges  IntRange 
kotlin.ranges  	LongRange 
kotlin.ranges  	UIntRange 
kotlin.ranges  
ULongRange 
kotlin.ranges  
coerceAtLeast 
kotlin.ranges  contains 
kotlin.ranges  until 
kotlin.ranges  iterator kotlin.ranges.IntProgression  iterator kotlin.ranges.IntRange  KMutableProperty0 kotlin.reflect  KMutableProperty1 kotlin.reflect  java kotlin.reflect.KClass  
isInitialized  kotlin.reflect.KMutableProperty0  Sequence kotlin.sequences  all kotlin.sequences  any kotlin.sequences  contains kotlin.sequences  filter kotlin.sequences  	filterNot kotlin.sequences  flatMap kotlin.sequences  forEach kotlin.sequences  indexOf kotlin.sequences  lastIndexOf kotlin.sequences  map kotlin.sequences  maxOf kotlin.sequences  minByOrNull kotlin.sequences  plus kotlin.sequences  
sortedWith kotlin.sequences  take kotlin.sequences  toList kotlin.sequences  toSet kotlin.sequences  Regex kotlin.text  all kotlin.text  any kotlin.text  contains kotlin.text  endsWith kotlin.text  filter kotlin.text  	filterNot kotlin.text  flatMap kotlin.text  forEach kotlin.text  format kotlin.text  indexOf kotlin.text  isBlank kotlin.text  isEmpty kotlin.text  
isNotEmpty kotlin.text  lastIndexOf kotlin.text  lines kotlin.text  	lowercase kotlin.text  map kotlin.text  matches kotlin.text  maxOf kotlin.text  minByOrNull kotlin.text  plus kotlin.text  removeSurrounding kotlin.text  replace kotlin.text  set kotlin.text  split kotlin.text  
startsWith kotlin.text  	substring kotlin.text  substringAfterLast kotlin.text  substringBeforeLast kotlin.text  take kotlin.text  toByteArray kotlin.text  toIntOrNull kotlin.text  toList kotlin.text  toLongOrNull kotlin.text  toRegex kotlin.text  toSet kotlin.text  trim kotlin.text  
trimIndent kotlin.text  ACTION_CANCEL_DOWNLOAD kotlinx.coroutines  ACTION_CHECK_FAILED_DOWNLOADS kotlinx.coroutines  ACTION_DOWNLOAD_UPDATE kotlinx.coroutines  ACTION_PAUSE_DOWNLOAD kotlinx.coroutines  ACTION_RESUME_DOWNLOAD kotlinx.coroutines  ACTION_START_DOWNLOAD kotlinx.coroutines  AUTO_RETRY_INTERVAL_MS kotlinx.coroutines  AlarmManager kotlinx.coroutines  
AtomicInteger kotlinx.coroutines  
AtomicLong kotlinx.coroutines  Binder kotlinx.coroutines  Boolean kotlinx.coroutines  Build kotlinx.coroutines  	ByteArray kotlinx.coroutines  CancellationException kotlinx.coroutines  CompletableJob kotlinx.coroutines  ConcurrentHashMap kotlinx.coroutines  ConnectivityManager kotlinx.coroutines  Context kotlinx.coroutines  CoroutineDispatcher kotlinx.coroutines  CoroutineScope kotlinx.coroutines  Delay kotlinx.coroutines  Dispatchers kotlinx.coroutines  DownloadActivity kotlinx.coroutines  DownloadItem kotlinx.coroutines  DownloadService kotlinx.coroutines  DownloadStatus kotlinx.coroutines  
EXTRA_COOKIES kotlinx.coroutines  EXTRA_DOWNLOAD_ID kotlinx.coroutines  EXTRA_DOWNLOAD_ID_UPDATE kotlinx.coroutines  EXTRA_DOWNLOAD_LIST_CHANGED kotlinx.coroutines  EXTRA_FILE_NAME kotlinx.coroutines  EXTRA_MIME_TYPE kotlinx.coroutines  EXTRA_ORIGIN kotlinx.coroutines  
EXTRA_REFERER kotlinx.coroutines  EXTRA_SOURCE kotlinx.coroutines  	EXTRA_URL kotlinx.coroutines  EXTRA_USER_AGENT kotlinx.coroutines  Environment kotlinx.coroutines  	Exception kotlinx.coroutines  File kotlinx.coroutines  FileOutputStream kotlinx.coroutines  FileProvider kotlinx.coroutines  Gson kotlinx.coroutines  Handler kotlinx.coroutines  HttpURLConnection kotlinx.coroutines  IBinder kotlinx.coroutines  IOException kotlinx.coroutines  IllegalArgumentException kotlinx.coroutines  InputStream kotlinx.coroutines  Int kotlinx.coroutines  Intent kotlinx.coroutines  
JSONObject kotlinx.coroutines  Job kotlinx.coroutines  JsonDownloader kotlinx.coroutines  KEY_DOWNLOAD_ITEMS kotlinx.coroutines  KEY_LAST_DOWNLOAD_ID kotlinx.coroutines  List kotlinx.coroutines  LocalBroadcastManager kotlinx.coroutines  Log kotlinx.coroutines  Long kotlinx.coroutines  Looper kotlinx.coroutines  MAX_CONCURRENT_JSON_DOWNLOADS kotlinx.coroutines  MAX_FAILURE_COUNT kotlinx.coroutines  MainCoroutineDispatcher kotlinx.coroutines  MimeTypeMap kotlinx.coroutines  Network kotlinx.coroutines  NetworkCapabilities kotlinx.coroutines  NetworkRequest kotlinx.coroutines  Notification kotlinx.coroutines  NotificationChannel kotlinx.coroutines  NotificationCompat kotlinx.coroutines  NotificationManager kotlinx.coroutines  
PREFS_NAME kotlinx.coroutines  Pattern kotlinx.coroutines  
PendingIntent kotlinx.coroutines  R kotlinx.coroutines  Regex kotlinx.coroutines  START_STICKY kotlinx.coroutines  STOP_FOREGROUND_REMOVE kotlinx.coroutines  Service kotlinx.coroutines  SharedPreferences kotlinx.coroutines  StandardCharsets kotlinx.coroutines  String kotlinx.coroutines  
SupervisorJob kotlinx.coroutines  Suppress kotlinx.coroutines  Synchronized kotlinx.coroutines  System kotlinx.coroutines  TAG kotlinx.coroutines  Toast kotlinx.coroutines  	TypeToken kotlinx.coroutines  URL kotlinx.coroutines  
URLDecoder kotlinx.coroutines  UnsupportedEncodingException kotlinx.coroutines  Uri kotlinx.coroutines  Volatile kotlinx.coroutines  also kotlinx.coroutines  any kotlinx.coroutines  apply kotlinx.coroutines  bufferedReader kotlinx.coroutines  
coerceAtLeast kotlinx.coroutines  	compareBy kotlinx.coroutines  contains kotlinx.coroutines  context kotlinx.coroutines  createControlIntent kotlinx.coroutines  createStartIntent kotlinx.coroutines  delay kotlinx.coroutines  
downloadItems kotlinx.coroutines  downloadJobs kotlinx.coroutines  endsWith kotlinx.coroutines  failedDownloads kotlinx.coroutines  filter kotlinx.coroutines  forEach kotlinx.coroutines  format kotlinx.coroutines  getCleanFileName kotlinx.coroutines  getDownloadUrl kotlinx.coroutines  isActive kotlinx.coroutines  isBlank kotlinx.coroutines  isEmpty kotlinx.coroutines  isNetworkAvailable kotlinx.coroutines  
isNotEmpty kotlinx.coroutines  java kotlinx.coroutines  lastIndexOf kotlinx.coroutines  launch kotlinx.coroutines  let kotlinx.coroutines  	lowercase kotlinx.coroutines  map kotlinx.coroutines  mapOf kotlinx.coroutines  minByOrNull kotlinx.coroutines  
plusAssign kotlinx.coroutines  readText kotlinx.coroutines  removeSurrounding kotlinx.coroutines  replace kotlinx.coroutines  
retryAttempts kotlinx.coroutines  retryFailedDownloads kotlinx.coroutines  run kotlinx.coroutines  saveDownloadState kotlinx.coroutines  serviceScope kotlinx.coroutines  set kotlinx.coroutines  	showToast kotlinx.coroutines  
sortedWith kotlinx.coroutines  split kotlinx.coroutines  startOrQueueDownload kotlinx.coroutines  
startsWith kotlinx.coroutines  	substring kotlinx.coroutines  substringAfterLast kotlinx.coroutines  substringBeforeLast kotlinx.coroutines  synchronized kotlinx.coroutines  take kotlinx.coroutines  thenByDescending kotlinx.coroutines  to kotlinx.coroutines  toByteArray kotlinx.coroutines  toList kotlinx.coroutines  toLongOrNull kotlinx.coroutines  trim kotlinx.coroutines  until kotlinx.coroutines  updateDownloadProgress kotlinx.coroutines  updateDownloadStatus kotlinx.coroutines  updateNotification kotlinx.coroutines  use kotlinx.coroutines  withContext kotlinx.coroutines  cancel !kotlinx.coroutines.CompletableJob  NetworkCallback &kotlinx.coroutines.ConnectivityManager  plus &kotlinx.coroutines.CoroutineDispatcher  	ByteArray !kotlinx.coroutines.CoroutineScope  CancellationException !kotlinx.coroutines.CoroutineScope  Dispatchers !kotlinx.coroutines.CoroutineScope  DownloadService !kotlinx.coroutines.CoroutineScope  DownloadStatus !kotlinx.coroutines.CoroutineScope  Environment !kotlinx.coroutines.CoroutineScope  FastOutSlowInEasing !kotlinx.coroutines.CoroutineScope  File !kotlinx.coroutines.CoroutineScope  FileOutputStream !kotlinx.coroutines.CoroutineScope  HttpURLConnection !kotlinx.coroutines.CoroutineScope  
JSONObject !kotlinx.coroutines.CoroutineScope  Log !kotlinx.coroutines.CoroutineScope  Pattern !kotlinx.coroutines.CoroutineScope  R !kotlinx.coroutines.CoroutineScope  StandardCharsets !kotlinx.coroutines.CoroutineScope  System !kotlinx.coroutines.CoroutineScope  TAG !kotlinx.coroutines.CoroutineScope  Toast !kotlinx.coroutines.CoroutineScope  URL !kotlinx.coroutines.CoroutineScope  
URLDecoder !kotlinx.coroutines.CoroutineScope  View !kotlinx.coroutines.CoroutineScope  	adBlocker !kotlinx.coroutines.CoroutineScope  binding !kotlinx.coroutines.CoroutineScope  browseFragment !kotlinx.coroutines.CoroutineScope  bufferedReader !kotlinx.coroutines.CoroutineScope  clearAdBlockerTimeConstraints !kotlinx.coroutines.CoroutineScope  contains !kotlinx.coroutines.CoroutineScope  context !kotlinx.coroutines.CoroutineScope  createStartIntent !kotlinx.coroutines.CoroutineScope  delay !kotlinx.coroutines.CoroutineScope  
downloadItems !kotlinx.coroutines.CoroutineScope  downloadJobs !kotlinx.coroutines.CoroutineScope  downloadService !kotlinx.coroutines.CoroutineScope  endsWith !kotlinx.coroutines.CoroutineScope  failedDownloads !kotlinx.coroutines.CoroutineScope  filter !kotlinx.coroutines.CoroutineScope  getCleanFileName !kotlinx.coroutines.CoroutineScope  getDownloadUrl !kotlinx.coroutines.CoroutineScope  	getString !kotlinx.coroutines.CoroutineScope  isActive !kotlinx.coroutines.CoroutineScope  isBlank !kotlinx.coroutines.CoroutineScope  isBound !kotlinx.coroutines.CoroutineScope  
isInitialized !kotlinx.coroutines.CoroutineScope  isNetworkAvailable !kotlinx.coroutines.CoroutineScope  
isNotEmpty !kotlinx.coroutines.CoroutineScope  launch !kotlinx.coroutines.CoroutineScope  let !kotlinx.coroutines.CoroutineScope  
loadDownloads !kotlinx.coroutines.CoroutineScope  loadLastUpdatedTimes !kotlinx.coroutines.CoroutineScope  mapOf !kotlinx.coroutines.CoroutineScope  openLinkValidator !kotlinx.coroutines.CoroutineScope  
plusAssign !kotlinx.coroutines.CoroutineScope  readText !kotlinx.coroutines.CoroutineScope  removeSurrounding !kotlinx.coroutines.CoroutineScope  
retryAttempts !kotlinx.coroutines.CoroutineScope  retryFailedDownloads !kotlinx.coroutines.CoroutineScope  run !kotlinx.coroutines.CoroutineScope  saveDownloadState !kotlinx.coroutines.CoroutineScope  saveUpdateTime !kotlinx.coroutines.CoroutineScope  set !kotlinx.coroutines.CoroutineScope  	showToast !kotlinx.coroutines.CoroutineScope  split !kotlinx.coroutines.CoroutineScope  startOrQueueDownload !kotlinx.coroutines.CoroutineScope  startService !kotlinx.coroutines.CoroutineScope  
startsWith !kotlinx.coroutines.CoroutineScope  	substring !kotlinx.coroutines.CoroutineScope  substringAfterLast !kotlinx.coroutines.CoroutineScope  substringBeforeLast !kotlinx.coroutines.CoroutineScope  to !kotlinx.coroutines.CoroutineScope  toByteArray !kotlinx.coroutines.CoroutineScope  toLongOrNull !kotlinx.coroutines.CoroutineScope  trim !kotlinx.coroutines.CoroutineScope  tween !kotlinx.coroutines.CoroutineScope  until !kotlinx.coroutines.CoroutineScope  updateDownloadProgress !kotlinx.coroutines.CoroutineScope  updateDownloadStatus !kotlinx.coroutines.CoroutineScope  updateNotification !kotlinx.coroutines.CoroutineScope  use !kotlinx.coroutines.CoroutineScope  validLinkChecker !kotlinx.coroutines.CoroutineScope  withContext !kotlinx.coroutines.CoroutineScope  IO kotlinx.coroutines.Dispatchers  Main kotlinx.coroutines.Dispatchers  cancel kotlinx.coroutines.Job  
JSONObject org.json  
getJSONObject org.json.JSONArray  length org.json.JSONArray  getJSONArray org.json.JSONObject  	getString org.json.JSONObject                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    