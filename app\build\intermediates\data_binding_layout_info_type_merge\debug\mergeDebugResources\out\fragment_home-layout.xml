<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_home" modulePackage="com.elewashy.egyfilm" filePath="app\src\main\res\layout\fragment_home.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/fragment_home_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="48" endOffset="51"/></Target><Target id="@+id/searchView" view="SearchView"><Expressions/><location startLine="9" startOffset="4" endLine="21" endOffset="43"/></Target><Target id="@+id/recyclerView" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="23" startOffset="4" endLine="32" endOffset="47"/></Target><Target id="@+id/viewAllBtn" view="TextView"><Expressions/><location startLine="35" startOffset="4" endLine="45" endOffset="40"/></Target></Targets></Layout>