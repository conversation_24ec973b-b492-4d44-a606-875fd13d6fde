<?xml version="1.0" encoding="utf-8"?>
<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="64dp"
    android:height="64dp"
    android:viewportWidth="64"
    android:viewportHeight="64">
    
    <group
        android:name="rotationGroup"
        android:pivotX="32"
        android:pivotY="32">
        
        <!-- Background arc -->
        <path
            android:name="backgroundArc"
            android:pathData="M32,8 A24,24 0 1,1 31.99,8"
            android:strokeWidth="3"
            android:strokeColor="#22E50914"
            android:strokeLineCap="round"
            android:fillType="nonZero" />
        
        <!-- Primary loading arc -->
        <path
            android:name="loadingArc"
            android:pathData="M32,8 A24,24 0 0,1 56,32"
            android:strokeWidth="4"
            android:strokeColor="#E50914"
            android:strokeLineCap="round"
            android:trimPathStart="0"
            android:trimPathEnd="0.3"
            android:fillType="nonZero" />
            
        <!-- Secondary accent arc -->
        <path
            android:name="accentArc"
            android:pathData="M32,8 A24,24 0 0,1 56,32"
            android:strokeWidth="2"
            android:strokeColor="#B81D24"
            android:strokeLineCap="round"
            android:trimPathStart="0.1"
            android:trimPathEnd="0.2"
            android:fillType="nonZero" />
            
    </group>
</vector>