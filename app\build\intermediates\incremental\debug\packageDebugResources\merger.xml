<?xml version="1.0" encoding="utf-8"?>
<merger version="3" xmlns:ns1="http://schemas.android.com/tools"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\res"><file name="activity_fade_out_down" path="C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\res\anim\activity_fade_out_down.xml" qualifiers="" type="anim"/><file name="activity_slide_in_up" path="C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\res\anim\activity_slide_in_up.xml" qualifiers="" type="anim"/><file name="bounce_in" path="C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\res\anim\bounce_in.xml" qualifiers="" type="anim"/><file name="egyfilm_progress_rotation" path="C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\res\anim\egyfilm_progress_rotation.xml" qualifiers="" type="anim"/><file name="fade_in" path="C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\res\anim\fade_in.xml" qualifiers="" type="anim"/><file name="fade_out" path="C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\res\anim\fade_out.xml" qualifiers="" type="anim"/><file name="fade_out_slide_down" path="C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\res\anim\fade_out_slide_down.xml" qualifiers="" type="anim"/><file name="pulse_animation" path="C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\res\anim\pulse_animation.xml" qualifiers="" type="anim"/><file name="slide_in_right" path="C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\res\anim\slide_in_right.xml" qualifiers="" type="anim"/><file name="slide_out_left" path="C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\res\anim\slide_out_left.xml" qualifiers="" type="anim"/><file name="slide_up" path="C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\res\anim\slide_up.xml" qualifiers="" type="anim"/><file name="slow_progress_rotation" path="C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\res\anim\slow_progress_rotation.xml" qualifiers="" type="anim"/><file name="egyfilm_progress_animator" path="C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\res\animator\egyfilm_progress_animator.xml" qualifiers="" type="animator"/><file name="enhanced_loading_animator" path="C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\res\animator\enhanced_loading_animator.xml" qualifiers="" type="animator"/><file name="bg_gradient" path="C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\res\drawable\bg_gradient.jpg" qualifiers="" type="drawable"/><file name="circular_button_background" path="C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\res\drawable\circular_button_background.xml" qualifiers="" type="drawable"/><file name="circular_button_background_stroke" path="C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\res\drawable\circular_button_background_stroke.xml" qualifiers="" type="drawable"/><file name="custom_design" path="C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\res\drawable\custom_design.xml" qualifiers="" type="drawable"/><file name="custom_tabs_textview" path="C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\res\drawable\custom_tabs_textview.xml" qualifiers="" type="drawable"/><file name="download_card_gradient" path="C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\res\drawable\download_card_gradient.xml" qualifiers="" type="drawable"/><file name="egyfilm_progress_drawable" path="C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\res\drawable\egyfilm_progress_drawable.xml" qualifiers="" type="drawable"/><file name="enhanced_loading_arc" path="C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\res\drawable\enhanced_loading_arc.xml" qualifiers="" type="drawable"/><file name="enhanced_loading_drawable" path="C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\res\drawable\enhanced_loading_drawable.xml" qualifiers="" type="drawable"/><file name="ic_add" path="C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\res\drawable\ic_add.xml" qualifiers="" type="drawable"/><file name="ic_back" path="C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\res\drawable\ic_back.xml" qualifiers="" type="drawable"/><file name="ic_cancel" path="C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\res\drawable\ic_cancel.xml" qualifiers="" type="drawable"/><file name="ic_delete" path="C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\res\drawable\ic_delete.xml" qualifiers="" type="drawable"/><file name="ic_download" path="C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\res\drawable\ic_download.xml" qualifiers="" type="drawable"/><file name="ic_download_file" path="C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\res\drawable\ic_download_file.xml" qualifiers="" type="drawable"/><file name="ic_d_google" path="C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\res\drawable\ic_d_google.png" qualifiers="" type="drawable"/><file name="ic_d_youtube" path="C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\res\drawable\ic_d_youtube.png" qualifiers="" type="drawable"/><file name="ic_file_complete" path="C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\res\drawable\ic_file_complete.xml" qualifiers="" type="drawable"/><file name="ic_forward" path="C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\res\drawable\ic_forward.xml" qualifiers="" type="drawable"/><file name="ic_fullscreen" path="C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\res\drawable\ic_fullscreen.xml" qualifiers="" type="drawable"/><file name="ic_fullscreen_exit" path="C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\res\drawable\ic_fullscreen_exit.xml" qualifiers="" type="drawable"/><file name="ic_globe" path="C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\res\drawable\ic_globe.xml" qualifiers="" type="drawable"/><file name="ic_home" path="C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\res\drawable\ic_home.xml" qualifiers="" type="drawable"/><file name="ic_launcher_background" path="C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\res\drawable\ic_launcher_background.xml" qualifiers="" type="drawable"/><file name="ic_more_vert" path="C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\res\drawable\ic_more_vert.xml" qualifiers="" type="drawable"/><file name="ic_open_file" path="C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\res\drawable\ic_open_file.xml" qualifiers="" type="drawable"/><file name="ic_pause" path="C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\res\drawable\ic_pause.xml" qualifiers="" type="drawable"/><file name="ic_play_arrow" path="C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\res\drawable\ic_play_arrow.xml" qualifiers="" type="drawable"/><file name="ic_refresh" path="C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\res\drawable\ic_refresh.xml" qualifiers="" type="drawable"/><file name="ic_search" path="C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\res\drawable\ic_search.xml" qualifiers="" type="drawable"/><file name="ic_settings" path="C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\res\drawable\ic_settings.xml" qualifiers="" type="drawable"/><file name="loading_arc" path="C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\res\drawable\loading_arc.xml" qualifiers="" type="drawable"/><file name="no_internet_background" path="C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\res\drawable\no_internet_background.xml" qualifiers="" type="drawable"/><file name="progress_circle" path="C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\res\drawable\progress_circle.xml" qualifiers="" type="drawable"/><file name="progress_indeterminate_anim" path="C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\res\drawable\progress_indeterminate_anim.xml" qualifiers="" type="drawable"/><file name="progress_vector" path="C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\res\drawable\progress_vector.xml" qualifiers="" type="drawable"/><file name="search_bar_background" path="C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\res\drawable\search_bar_background.xml" qualifiers="" type="drawable"/><file name="ic_launcher_foreground" path="C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\res\drawable-v24\ic_launcher_foreground.xml" qualifiers="v24" type="drawable"/><file name="activity_download" path="C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\res\layout\activity_download.xml" qualifiers="" type="layout"/><file name="activity_filter_updates" path="C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\res\layout\activity_filter_updates.xml" qualifiers="" type="layout"/><file name="activity_main" path="C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\res\layout\activity_main.xml" qualifiers="" type="layout"/><file name="activity_settings" path="C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\res\layout\activity_settings.xml" qualifiers="" type="layout"/><file name="activity_splash" path="C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\res\layout\activity_splash.xml" qualifiers="" type="layout"/><file name="bottom_sheet_more_options" path="C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\res\layout\bottom_sheet_more_options.xml" qualifiers="" type="layout"/><file name="fragment_browse" path="C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\res\layout\fragment_browse.xml" qualifiers="" type="layout"/><file name="fragment_home" path="C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\res\layout\fragment_home.xml" qualifiers="" type="layout"/><file name="item_download" path="C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\res\layout\item_download.xml" qualifiers="" type="layout"/><file name="more_features" path="C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\res\layout\more_features.xml" qualifiers="" type="layout"/><file name="ic_launcher" path="C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\res\mipmap-anydpi-v26\ic_launcher.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\res\mipmap-anydpi-v26\ic_launcher_round.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\res\mipmap-hdpi\ic_launcher.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\res\mipmap-hdpi\ic_launcher_foreground.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\res\mipmap-hdpi\ic_launcher_round.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\res\mipmap-mdpi\ic_launcher.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\res\mipmap-mdpi\ic_launcher_foreground.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\res\mipmap-mdpi\ic_launcher_round.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\res\mipmap-xhdpi\ic_launcher.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\res\mipmap-xhdpi\ic_launcher_foreground.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\res\mipmap-xhdpi\ic_launcher_round.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\res\mipmap-xxhdpi\ic_launcher.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\res\mipmap-xxhdpi\ic_launcher_foreground.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\res\mipmap-xxhdpi\ic_launcher_round.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\res\mipmap-xxxhdpi\ic_launcher.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\res\mipmap-xxxhdpi\ic_launcher_foreground.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\res\mipmap-xxxhdpi\ic_launcher_round.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file path="C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\res\values\colors.xml" qualifiers=""><color name="egyfilm_red">#E50914</color><color name="egyfilm_red_dark">#B81D24</color><color name="egyfilm_black">#000000</color><color name="egyfilm_dark_gray">#141414</color><color name="egyfilm_gray">#808080</color><color name="egyfilm_light_gray">#E5E5E5</color><color name="black">#FF000000</color><color name="white">#FFFFFFFF</color><color name="primary">@color/egyfilm_red</color><color name="primary_dark">@color/egyfilm_red_dark</color><color name="colorPrimary">@color/egyfilm_red</color><color name="background">@color/egyfilm_black</color><color name="surface">@color/egyfilm_dark_gray</color><color name="text_primary">@color/white</color><color name="text_secondary">@color/egyfilm_gray</color><color name="egyfilm_background">@color/egyfilm_dark_gray</color><color name="gray_text">@color/egyfilm_gray</color><integer-array name="myColors">
        <item>@color/egyfilm_red</item>
        <item>@color/egyfilm_red_dark</item>
        <item>@color/egyfilm_black</item>
        <item>@color/egyfilm_dark_gray</item>
        <item>@color/egyfilm_gray</item>
    </integer-array></file><file path="C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\res\values\ic_launcher_background.xml" qualifiers=""><color name="ic_launcher_background">#FFFFFF</color></file><file path="C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">EgyFilm</string><string name="enter_url">Welcome to EgyFilm</string><string name="home_btn">Home Button</string><string name="settings_btn">Settings Button</string><string name="search_hint">Search</string><string name="homePage">HomePage</string><string name="back">Back</string><string name="forward">Forward</string><string name="fullscreen">FullScreen</string><string name="view_all">View All</string><string name="refresh_btn">Refresh</string><string name="downloads">Downloads</string><string name="download_icon_desc">File type icon</string><string name="pause_resume_button_desc">Pause or Resume Download</string><string name="cancel_button_desc">Cancel Download</string><string name="retry_button_desc">Retry Download</string><string name="delete_button_desc">Delete File</string><string name="open_file_button_desc">Open File</string><string name="more_options">More Options</string><string name="settings">Settings</string><string name="filter_updates">Filter Updates</string><string name="filter_updates_desc">Manage and update app filters</string><string name="adblocker_filter">AdBlocker Filter</string><string name="valid_filter">Valid Links Filter</string><string name="open_filter">Open Links Filter</string><string name="update_all_filters">Update All Filters</string><string name="update">Update</string><string name="updating">Updating...</string><string name="update_success">Update Successful</string><string name="update_failed">Update Failed</string><string name="last_updated">Last Updated: %s</string><string name="never_updated">Never Updated</string></file><file path="C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\res\values\styles.xml" qualifiers=""><style name="egyfilmProgressStyle" parent="Widget.MaterialComponents.CircularProgressIndicator">
        <item name="indicatorSize">48dp</item>
        <item name="trackThickness">3dp</item>
        <item name="trackCornerRadius">2dp</item>
        <item name="indicatorColor">@color/egyfilm_red</item>
        <item name="trackColor">@android:color/transparent</item>
        <item name="indicatorDirectionCircular">clockwise</item>
        <item name="android:indeterminateOnly">true</item>
        <item name="android:indeterminateDuration">2500</item>
        <item name="circularProgressIndicatorStyle">@style/Widget.MaterialComponents.CircularProgressIndicator</item>
        
        <item name="indicatorInset">0dp</item>
        <item name="minHideDelay">0</item>
    </style></file><file path="C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\res\values\themes.xml" qualifiers=""><style name="Theme.egyfilm" parent="Theme.MaterialComponents.NoActionBar">
        
        <item name="colorPrimary">@color/egyfilm_red</item>
        <item name="colorPrimaryVariant">@color/egyfilm_red_dark</item>
        <item name="colorOnPrimary">@color/white</item>
        
        <item name="colorSecondary">@color/egyfilm_red</item>
        <item name="colorSecondaryVariant">@color/egyfilm_red_dark</item>
        <item name="colorOnSecondary">@color/white</item>
        
        <item name="android:windowBackground">@color/egyfilm_black</item>
        <item name="backgroundColor">@color/egyfilm_black</item>
        <item name="colorSurface">@color/egyfilm_dark_gray</item>
        <item name="colorOnSurface">@color/white</item>
        
        <item name="android:textColorPrimary">@color/white</item>
        <item name="android:textColorSecondary">@color/egyfilm_gray</item>
        
        <item name="android:statusBarColor">@color/egyfilm_black</item>
        
        <item name="android:navigationBarColor">@color/egyfilm_black</item>
        
        <item name="android:forceDarkAllowed" ns1:targetApi="q">false</item>
    </style><style name="Theme.egyfilm.NoActionBar.Fullscreen" parent="Theme.egyfilm">
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowLayoutInDisplayCutoutMode" ns1:targetApi="o_mr1">shortEdges</item>
        <item name="android:windowTranslucentStatus">true</item>
        <item name="android:windowTranslucentNavigation">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:fitsSystemWindows">false</item>
    </style><style name="roundCornerDialog" parent="ThemeOverlay.MaterialComponents.Dialog.Alert">
        <item name="shapeAppearanceOverlay">@style/shape</item>
        <item name="android:background">@color/egyfilm_dark_gray</item>
        <item name="colorSurface">@color/egyfilm_dark_gray</item>
        <item name="android:textColorPrimary">@color/white</item>
        <item name="android:textColorSecondary">@color/egyfilm_gray</item>
    </style><style name="shape">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">8dp</item>
    </style></file><file path="C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\res\values-night\themes.xml" qualifiers="night-v8"><style name="Theme.egyfilm" parent="Theme.MaterialComponents.NoActionBar">
        
        <item name="colorPrimary">@color/egyfilm_red</item>
        <item name="colorPrimaryVariant">@color/egyfilm_red_dark</item>
        <item name="colorOnPrimary">@color/white</item>
        
        <item name="colorSecondary">@color/egyfilm_red</item>
        <item name="colorSecondaryVariant">@color/egyfilm_red_dark</item>
        <item name="colorOnSecondary">@color/white</item>
        
        <item name="android:windowBackground">@color/egyfilm_black</item>
        <item name="backgroundColor">@color/egyfilm_black</item>
        <item name="colorSurface">@color/egyfilm_dark_gray</item>
        <item name="colorOnSurface">@color/white</item>
        
        <item name="android:textColorPrimary">@color/white</item>
        <item name="android:textColorSecondary">@color/egyfilm_gray</item>
        
        <item name="android:statusBarColor">@color/egyfilm_black</item>
        
        <item name="android:navigationBarColor">@color/egyfilm_black</item>
    </style><style name="Theme.egyfilm.NoActionBar.Fullscreen" parent="Theme.egyfilm">
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowLayoutInDisplayCutoutMode" ns1:targetApi="o_mr1">shortEdges</item>
        <item name="android:windowTranslucentStatus">true</item>
        <item name="android:windowTranslucentNavigation">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:fitsSystemWindows">false</item>
    </style></file><file name="activity_main_scene" path="C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\res\xml\activity_main_scene.xml" qualifiers="" type="xml"/><file name="backup_rules" path="C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\res\xml\backup_rules.xml" qualifiers="" type="xml"/><file name="data_extraction_rules" path="C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\res\xml\data_extraction_rules.xml" qualifiers="" type="xml"/><file name="network_security_config" path="C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\res\xml\network_security_config.xml" qualifiers="" type="xml"/><file name="provider_paths" path="C:\Users\<USER>\Desktop\EgyFilm-App\app\src\main\res\xml\provider_paths.xml" qualifiers="" type="xml"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\EgyFilm-App\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\EgyFilm-App\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\EgyFilm-App\app\build\generated\res\resValues\debug"/><source path="C:\Users\<USER>\Desktop\EgyFilm-App\app\build\generated\res\processDebugGoogleServices"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\EgyFilm-App\app\build\generated\res\resValues\debug"/><source path="C:\Users\<USER>\Desktop\EgyFilm-App\app\build\generated\res\processDebugGoogleServices"><file path="C:\Users\<USER>\Desktop\EgyFilm-App\app\build\generated\res\processDebugGoogleServices\values\values.xml" qualifiers=""><string name="gcm_defaultSenderId" translatable="false">617958848640</string><string name="google_api_key" translatable="false">AIzaSyCoJkU_0yI-CtYeVU6U40fEOzn_5MN3SwI</string><string name="google_app_id" translatable="false">1:617958848640:android:6beb8157cec53bbbdf4e65</string><string name="google_crash_reporting_api_key" translatable="false">AIzaSyCoJkU_0yI-CtYeVU6U40fEOzn_5MN3SwI</string><string name="google_storage_bucket" translatable="false">egyfilm-app.firebasestorage.app</string><string name="project_id" translatable="false">egyfilm-app</string></file></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="res-processDebugGoogleServices$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="res-processDebugGoogleServices" generated-set="res-processDebugGoogleServices$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><mergedItems/></merger>