{"logs": [{"outputFile": "com.elewashy.egyfilm.app-mergeDebugResources-66:/values-sl/values-sl.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\e8013e1c38b0d03c268afa9230065a69\\transformed\\appcompat-1.7.0\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,217,319,427,514,617,736,817,895,987,1081,1176,1270,1365,1459,1555,1655,1747,1839,1923,2031,2139,2239,2352,2460,2565,2745,2845", "endColumns": "111,101,107,86,102,118,80,77,91,93,94,93,94,93,95,99,91,91,83,107,107,99,112,107,104,179,99,83", "endOffsets": "212,314,422,509,612,731,812,890,982,1076,1171,1265,1360,1454,1550,1650,1742,1834,1918,2026,2134,2234,2347,2455,2560,2740,2840,2924"}, "to": {"startLines": "8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,200", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "417,529,631,739,826,929,1048,1129,1207,1299,1393,1488,1582,1677,1771,1867,1967,2059,2151,2235,2343,2451,2551,2664,2772,2877,3057,19208", "endColumns": "111,101,107,86,102,118,80,77,91,93,94,93,94,93,95,99,91,91,83,107,107,99,112,107,104,179,99,83", "endOffsets": "524,626,734,821,924,1043,1124,1202,1294,1388,1483,1577,1672,1766,1862,1962,2054,2146,2230,2338,2446,2546,2659,2767,2872,3052,3152,19287"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\71f8bc87e80bd35756c6bcfc97f71602\\transformed\\ui-release\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,281,377,475,560,637,724,816,898,980,1066,1138,1215,1295,1373,1451,1521", "endColumns": "94,80,95,97,84,76,86,91,81,81,85,71,76,79,77,77,69,120", "endOffsets": "195,276,372,470,555,632,719,811,893,975,1061,1133,1210,1290,1368,1446,1516,1637"}, "to": {"startLines": "50,51,70,71,72,77,78,195,196,198,199,203,205,206,207,209,210,211", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4651,4746,7051,7147,7245,7630,7707,18789,18881,19040,19122,19455,19605,19682,19762,19941,20019,20089", "endColumns": "94,80,95,97,84,76,86,91,81,81,85,71,76,79,77,77,69,120", "endOffsets": "4741,4822,7142,7240,7325,7702,7789,18876,18958,19117,19203,19522,19677,19757,19835,20014,20084,20205"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\68cc43b1f116f6befee1484e2c2e084f\\transformed\\core-1.15.0\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,352,456,559,661,778", "endColumns": "96,101,97,103,102,101,116,100", "endOffsets": "147,249,347,451,554,656,773,874"}, "to": {"startLines": "40,41,42,43,44,45,46,208", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3612,3709,3811,3909,4013,4116,4218,19840", "endColumns": "96,101,97,103,102,101,116,100", "endOffsets": "3704,3806,3904,4008,4111,4213,4330,19936"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\52ee2787b45be912bfe498d0191617c7\\transformed\\foundation-release\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,145", "endColumns": "89,90", "endOffsets": "140,231"}, "to": {"startLines": "212,213", "startColumns": "4,4", "startOffsets": "20210,20300", "endColumns": "89,90", "endOffsets": "20295,20386"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\695db91b505a701047f9507726e12587\\transformed\\material3-release\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,184,313,431,560,670,766,879,1019,1145,1288,1373,1472,1565,1662,1779,1901,2005,2142,2276,2407,2591,2718,2841,2966,3088,3182,3280,3400,3524,3624,3733,3839,3982,4129,4238,4340,4424,4519,4615,4723,4811,4897,5000,5082,5165,5260,5360,5451,5548,5636,5740,5837,5939,6081,6163,6269", "endColumns": "128,128,117,128,109,95,112,139,125,142,84,98,92,96,116,121,103,136,133,130,183,126,122,124,121,93,97,119,123,99,108,105,142,146,108,101,83,94,95,107,87,85,102,81,82,94,99,90,96,87,103,96,101,141,81,105,98", "endOffsets": "179,308,426,555,665,761,874,1014,1140,1283,1368,1467,1560,1657,1774,1896,2000,2137,2271,2402,2586,2713,2836,2961,3083,3177,3275,3395,3519,3619,3728,3834,3977,4124,4233,4335,4419,4514,4610,4718,4806,4892,4995,5077,5160,5255,5355,5446,5543,5631,5735,5832,5934,6076,6158,6264,6363"}, "to": {"startLines": "80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7857,7986,8115,8233,8362,8472,8568,8681,8821,8947,9090,9175,9274,9367,9464,9581,9703,9807,9944,10078,10209,10393,10520,10643,10768,10890,10984,11082,11202,11326,11426,11535,11641,11784,11931,12040,12142,12226,12321,12417,12525,12613,12699,12802,12884,12967,13062,13162,13253,13350,13438,13542,13639,13741,13883,13965,14071", "endColumns": "128,128,117,128,109,95,112,139,125,142,84,98,92,96,116,121,103,136,133,130,183,126,122,124,121,93,97,119,123,99,108,105,142,146,108,101,83,94,95,107,87,85,102,81,82,94,99,90,96,87,103,96,101,141,81,105,98", "endOffsets": "7981,8110,8228,8357,8467,8563,8676,8816,8942,9085,9170,9269,9362,9459,9576,9698,9802,9939,10073,10204,10388,10515,10638,10763,10885,10979,11077,11197,11321,11421,11530,11636,11779,11926,12035,12137,12221,12316,12412,12520,12608,12694,12797,12879,12962,13057,13157,13248,13345,13433,13537,13634,13736,13878,13960,14066,14165"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\1df05e36fa9740a9bf8fb7606a9ee5d1\\transformed\\play-services-base-18.1.0\\res\\values-sl\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,456,581,684,827,953,1063,1163,1317,1420,1583,1709,1857,2005,2071,2129", "endColumns": "101,160,124,102,142,125,109,99,153,102,162,125,147,147,65,57,79", "endOffsets": "294,455,580,683,826,952,1062,1162,1316,1419,1582,1708,1856,2004,2070,2128,2208"}, "to": {"startLines": "52,53,54,55,56,57,58,59,61,62,63,64,65,66,67,68,69", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4827,4933,5098,5227,5334,5481,5611,5725,5969,6127,6234,6401,6531,6683,6835,6905,6967", "endColumns": "105,164,128,106,146,129,113,103,157,106,166,129,151,151,69,61,83", "endOffsets": "4928,5093,5222,5329,5476,5606,5720,5824,6122,6229,6396,6526,6678,6830,6900,6962,7046"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\c7adff6432088b47c9cce630115db3d4\\transformed\\material-1.12.0\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,367,456,545,633,731,822,928,1054,1138,1202,1268,1362,1438,1501,1613,1673,1738,1792,1862,1922,1978,2090,2147,2209,2265,2338,2472,2557,2634,2723,2804,2889,3032,3116,3199,3333,3422,3499,3555,3610,3676,3749,3826,3897,3976,4050,4126,4201,4274,4379,4467,4540,4630,4721,4793,4867,4958,5010,5092,5159,5243,5330,5392,5456,5519,5588,5691,5799,5897,6001,6061,6120,6197,6284,6360", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82", "endColumns": "12,88,88,87,97,90,105,125,83,63,65,93,75,62,111,59,64,53,69,59,55,111,56,61,55,72,133,84,76,88,80,84,142,83,82,133,88,76,55,54,65,72,76,70,78,73,75,74,72,104,87,72,89,90,71,73,90,51,81,66,83,86,61,63,62,68,102,107,97,103,59,58,76,86,75,77", "endOffsets": "362,451,540,628,726,817,923,1049,1133,1197,1263,1357,1433,1496,1608,1668,1733,1787,1857,1917,1973,2085,2142,2204,2260,2333,2467,2552,2629,2718,2799,2884,3027,3111,3194,3328,3417,3494,3550,3605,3671,3744,3821,3892,3971,4045,4121,4196,4269,4374,4462,4535,4625,4716,4788,4862,4953,5005,5087,5154,5238,5325,5387,5451,5514,5583,5686,5794,5892,5996,6056,6115,6192,6279,6355,6433"}, "to": {"startLines": "2,35,36,37,38,39,47,48,49,73,74,75,76,79,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,197,201,202,204", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3157,3246,3335,3423,3521,4335,4441,4567,7330,7394,7460,7554,7794,14170,14282,14342,14407,14461,14531,14591,14647,14759,14816,14878,14934,15007,15141,15226,15303,15392,15473,15558,15701,15785,15868,16002,16091,16168,16224,16279,16345,16418,16495,16566,16645,16719,16795,16870,16943,17048,17136,17209,17299,17390,17462,17536,17627,17679,17761,17828,17912,17999,18061,18125,18188,18257,18360,18468,18566,18670,18730,18963,19292,19379,19527", "endLines": "7,35,36,37,38,39,47,48,49,73,74,75,76,79,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,197,201,202,204", "endColumns": "12,88,88,87,97,90,105,125,83,63,65,93,75,62,111,59,64,53,69,59,55,111,56,61,55,72,133,84,76,88,80,84,142,83,82,133,88,76,55,54,65,72,76,70,78,73,75,74,72,104,87,72,89,90,71,73,90,51,81,66,83,86,61,63,62,68,102,107,97,103,59,58,76,86,75,77", "endOffsets": "412,3241,3330,3418,3516,3607,4436,4562,4646,7389,7455,7549,7625,7852,14277,14337,14402,14456,14526,14586,14642,14754,14811,14873,14929,15002,15136,15221,15298,15387,15468,15553,15696,15780,15863,15997,16086,16163,16219,16274,16340,16413,16490,16561,16640,16714,16790,16865,16938,17043,17131,17204,17294,17385,17457,17531,17622,17674,17756,17823,17907,17994,18056,18120,18183,18252,18355,18463,18561,18665,18725,18784,19035,19374,19450,19600"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\233292c89c1b85b15f1b8e2d4e33fbe5\\transformed\\play-services-basement-18.3.0\\res\\values-sl\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "135", "endOffsets": "330"}, "to": {"startLines": "60", "startColumns": "4", "startOffsets": "5829", "endColumns": "139", "endOffsets": "5964"}}]}]}