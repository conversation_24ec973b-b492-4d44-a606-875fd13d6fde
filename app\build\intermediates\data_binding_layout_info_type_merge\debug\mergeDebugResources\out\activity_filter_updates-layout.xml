<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_filter_updates" modulePackage="com.elewashy.egyfilm" filePath="app\src\main\res\layout\activity_filter_updates.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/activity_filter_updates_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="291" endOffset="14"/></Target><Target id="@+id/backButton" view="ImageButton"><Expressions/><location startLine="20" startOffset="8" endLine="27" endOffset="45"/></Target><Target id="@+id/adBlockerLastUpdate" view="TextView"><Expressions/><location startLine="96" startOffset="24" endLine="103" endOffset="60"/></Target><Target id="@+id/adBlockerStatus" view="TextView"><Expressions/><location startLine="105" startOffset="24" endLine="112" endOffset="55"/></Target><Target id="@+id/adBlockerProgress" view="ProgressBar"><Expressions/><location startLine="116" startOffset="20" endLine="121" endOffset="51"/></Target><Target id="@+id/updateAdBlockerBtn" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="123" startOffset="20" endLine="130" endOffset="73"/></Target><Target id="@+id/validLinksLastUpdate" view="TextView"><Expressions/><location startLine="165" startOffset="24" endLine="172" endOffset="60"/></Target><Target id="@+id/validLinksStatus" view="TextView"><Expressions/><location startLine="174" startOffset="24" endLine="181" endOffset="55"/></Target><Target id="@+id/validLinksProgress" view="ProgressBar"><Expressions/><location startLine="185" startOffset="20" endLine="190" endOffset="51"/></Target><Target id="@+id/updateValidLinksBtn" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="192" startOffset="20" endLine="199" endOffset="73"/></Target><Target id="@+id/openLinksLastUpdate" view="TextView"><Expressions/><location startLine="234" startOffset="24" endLine="241" endOffset="60"/></Target><Target id="@+id/openLinksStatus" view="TextView"><Expressions/><location startLine="243" startOffset="24" endLine="250" endOffset="55"/></Target><Target id="@+id/openLinksProgress" view="ProgressBar"><Expressions/><location startLine="254" startOffset="20" endLine="259" endOffset="51"/></Target><Target id="@+id/updateOpenLinksBtn" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="261" startOffset="20" endLine="268" endOffset="73"/></Target><Target id="@+id/updateAllFiltersBtn" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="275" startOffset="12" endLine="285" endOffset="65"/></Target></Targets></Layout>